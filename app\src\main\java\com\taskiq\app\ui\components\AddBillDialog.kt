package com.taskiq.app.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.res.dimensionResource
import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.model.BillType
import com.taskiq.app.ui.theme.textFieldColors
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddBillDialog(
    onDismiss: () -> Unit,
    onBillAdded: (Bill) -> Unit,
    existingBill: Bill? = null,
    isFullScreen: Boolean = false,
    modifier: Modifier = Modifier
) {
    var title by remember { mutableStateOf(existingBill?.title ?: "") }
    var amount by remember { mutableStateOf(existingBill?.amount?.toString() ?: "") }
    var dueDate by remember { mutableStateOf(existingBill?.dueDate ?: LocalDate.now()) }
    var showBillTypeMenu by remember { mutableStateOf(false) }
    var selectedBillType by remember { mutableStateOf(existingBill?.type ?: BillType.CREDIT_CARD) }
    var recurringPeriod by remember { mutableStateOf(existingBill?.recurringPeriod?.toString() ?: "0") }
    var showDatePicker by remember { mutableStateOf(false) }
    var showRepeatMenu by remember { mutableStateOf(false) }
    
    // State for duplicate bill warning
    var showDuplicateWarning by remember { mutableStateOf(false) }
    
    val isEditing = existingBill != null
    val confirmButtonText = if (isEditing) "Update" else "Add Bill"
    
    val dateFormatter = DateTimeFormatter.ofPattern("E, MMM d, yyyy")
    
    @Composable
    fun DialogContent() {
        Column(
            modifier = Modifier
                .padding(if (isFullScreen) 0.dp else 24.dp)
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
                .then(if (isFullScreen) Modifier.padding(horizontal = 16.dp) else Modifier),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // Title
            OutlinedTextField(
                value = title,
                onValueChange = { 
                    title = it
                    showDuplicateWarning = false // Clear warning when title changes
                },
                label = { Text("Bill Title") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                leadingIcon = {
                    Icon(
                        imageVector = getBillTypeIcon(selectedBillType),
                        contentDescription = "Bill Type Icon",
                        tint = MaterialTheme.colorScheme.primary
                    )
                },
                colors = textFieldColors()
            )
            
            // Show duplicate warning if needed
            if (showDuplicateWarning) {
                Text(
                    text = "A bill with this title already exists. Please use a different title.",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }

            // Bill Type
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showBillTypeMenu = true },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowDropDown,
                        contentDescription = "Select bill type",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "Bill Type",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = selectedBillType.name.replace("_", " "),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
            
            Box {
                DropdownMenu(
                    expanded = showBillTypeMenu,
                    onDismissRequest = { showBillTypeMenu = false }
                ) {
                    BillType.values().forEach { type ->
                        DropdownMenuItem(
                            text = { Text(type.name.replace("_", " ")) },
                            onClick = {
                                selectedBillType = type
                                showBillTypeMenu = false
                            }
                        )
                    }
                }
            }
            
            // Amount
            OutlinedTextField(
                value = amount,
                onValueChange = { amount = it },
                label = { Text("Amount") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                singleLine = true,
                leadingIcon = {
                    Text(
                        text = "₹",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                },
                colors = textFieldColors()
            )

            // Due Date
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showDatePicker = true },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.DateRange,
                        contentDescription = "Date",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "Due Date",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = dueDate.format(dateFormatter),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
            
            // Repeat field (replacing Recurring Period)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showRepeatMenu = true },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Repeat,
                        contentDescription = "Repeat",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "Repeat",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = when {
                                recurringPeriod == "0" -> "No Repeat"
                                recurringPeriod == "1" -> "Monthly"
                                recurringPeriod == "12" -> "Yearly" 
                                else -> "Custom (${recurringPeriod} days)"
                            },
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
            
            Box {
                DropdownMenu(
                    expanded = showRepeatMenu,
                    onDismissRequest = { showRepeatMenu = false }
                ) {
                    listOf(
                        "No Repeat" to "0",
                        "Monthly" to "1",
                        "Yearly" to "12",
                        "Custom" to "-1"
                    ).forEach { (label, value) ->
                        DropdownMenuItem(
                            text = { Text(label) },
                            onClick = {
                                if (value == "-1") {
                                    // If Custom is selected and there was a previous custom value, keep it
                                    if (recurringPeriod != "0" && recurringPeriod != "1" && recurringPeriod != "12") {
                                        // Keep the existing custom value
                                    } else {
                                        // Default to 3 days if no previous custom value
                                        recurringPeriod = "3"
                                    }
                                } else {
                                    recurringPeriod = value
                                }
                                showRepeatMenu = false
                            }
                        )
                    }
                }
            }
            
            // Custom days input (only shown when Custom is selected)
            if (recurringPeriod != "0" && recurringPeriod != "1" && recurringPeriod != "12") {
                OutlinedTextField(
                    value = recurringPeriod,
                    onValueChange = { 
                        if (it.isEmpty() || it.toIntOrNull() != null) {
                            recurringPeriod = it
                        }
                    },
                    label = { Text("Number of Days") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    colors = textFieldColors()
                )
            }
            
            // Note about recurring bills
            Text(
                text = "Note: Repeating bills will be automatically added to your schedule.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 16.dp)
            )

            // Buttons
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = onDismiss,
                    modifier = Modifier.weight(1f),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text("Cancel")
                }
                
                Button(
                    onClick = {
                        if (title.isNotBlank() && amount.isNotBlank()) {
                            val bill = if (isEditing) {
                                existingBill!!.copy(
                                    title = title,
                                    amount = amount.toDoubleOrNull() ?: 0.0,
                                    dueDate = dueDate,
                                    type = selectedBillType,
                                    description = "",
                                    recurringPeriod = recurringPeriod.toIntOrNull() ?: 0
                                )
                            } else {
                                Bill(
                                    id = UUID.randomUUID().toString(),
                                    title = title,
                                    amount = amount.toDoubleOrNull() ?: 0.0,
                                    dueDate = dueDate,
                                    type = selectedBillType,
                                    description = "",
                                    status = BillStatus.PENDING,
                                    recurringPeriod = recurringPeriod.toIntOrNull() ?: 0,
                                    userId = "current_user_id", // This would come from auth in a real app
                                    createdAt = LocalDate.now()
                                )
                            }
                            onBillAdded(bill)
                        }
                    },
                    enabled = title.isNotBlank() && amount.isNotBlank(),
                    modifier = Modifier.weight(1f),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text(confirmButtonText)
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
    
    if (isFullScreen) {
        DialogContent()
    } else {
        Dialog(onDismissRequest = onDismiss) {
            Surface(
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface
            ) {
                DialogContent()
            }
        }
    }
    
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = dueDate
                .atStartOfDay(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            dueDate = Instant.ofEpochMilli(millis)
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate()
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDatePicker = false }
                ) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }
}
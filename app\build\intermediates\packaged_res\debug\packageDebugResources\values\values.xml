<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="ic_launcher_background">#0066CC</color>
    <color name="off_white">#FFF8F8F8</color>
    <color name="professional_blue">#FF1565C0</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">TaskIQ</string>
    <string name="app_tagline">For minds that multitask.</string>
    <string name="back">Back</string>
    <string name="clear_all">Clear All</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="default_web_client_id" translatable="false">923766663449-mqg30ij6rit4qfmsog58fea3dfrdaist.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">923766663449</string>
    <string name="google_api_key" translatable="false">AIzaSyDLiFx-qCwDFHkCwaprbOFp-3kysoi1IBs</string>
    <string name="google_app_id" translatable="false">1:923766663449:android:46b297660eda892e987d88</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyDLiFx-qCwDFHkCwaprbOFp-3kysoi1IBs</string>
    <string name="google_storage_bucket" translatable="false">task-reminder-app-bb81d.firebasestorage.app</string>
    <string name="no_notifications">No notifications yet</string>
    <string name="notification_history">Notification History</string>
    <string name="project_id" translatable="false">task-reminder-app-bb81d</string>
    <style name="Theme.TaskIQ" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="Theme.TaskIQ.Main" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
package com.taskiq.app.viewmodel

import android.app.Application
import android.content.Context
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.model.Subtask
import com.taskiq.app.service.BillService
import com.taskiq.app.service.NotificationRegistrar
import com.taskiq.app.service.NotificationService
import com.taskiq.app.service.TaskService
import com.taskiq.app.worker.TaskWorkerManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.util.Calendar
import java.util.Date
import java.util.UUID
import java.util.concurrent.TimeUnit
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import kotlin.math.abs
import java.io.File
import com.taskiq.app.service.GoogleCalendarService

class TaskViewModel(application: Application) : AndroidViewModel(application) {
    
    private val taskService = TaskService(application)
    private val notificationService = NotificationService(application)
    private val taskWorkerManager = TaskWorkerManager(application)
    private val billService = BillService(application)
    private val context = application.applicationContext
    private val gson = com.google.gson.Gson()

    // Add backup service for automatic syncing
    private val backupService = com.taskiq.app.service.BackupService(application)
    
    var isLoading by mutableStateOf(false)
        private set
    
    private val _tasks = mutableStateListOf<Task>()
    val tasks: List<Task> get() = _tasks
    
    private val _importantDates = mutableStateListOf<Task>()
    val importantDates: List<Task> get() = _importantDates
    
    // Bills related state
    private val _bills = MutableStateFlow<List<Bill>>(emptyList())
    val bills = _bills.asStateFlow()
    
    private val _linkedEmails = MutableStateFlow<List<String>>(emptyList())
    val linkedEmails = _linkedEmails.asStateFlow()
    
    var isEmailScanningInProgress by mutableStateOf(false)
        private set
    
    var isCalendarSyncing by mutableStateOf(false)
        private set
        
    // Add persistent state for Google Calendar sync preference
    var isGoogleCalendarSyncEnabled by mutableStateOf(false)
        private set

    // Add function to update Google Calendar sync preference
    fun updateGoogleCalendarSync(enabled: Boolean) {
        isGoogleCalendarSyncEnabled = enabled

        // Save to SharedPreferences for persistence
        try {
            val sharedPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putBoolean("google_calendar_sync_enabled", enabled)
                .apply()
            Log.d("TaskViewModel", "Google Calendar sync setting saved: $enabled")
        } catch (e: Exception) {
            Log.e("TaskViewModel", "Error saving Google Calendar sync setting: ${e.message}")
        }

        if (enabled) {
            // Start immediate sync
            syncWithGoogleCalendar()
            // Start periodic sync
            startPeriodicSync()
        } else {
            // Stop periodic sync when disabled
            stopPeriodicSync()
        }
    }

    // Add function to update email scanning preference
    fun updatePeriodicEmailScanning(enabled: Boolean) {
        isPeriodicEmailScanningEnabled = enabled

        // Save to SharedPreferences for persistence
        try {
            val sharedPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putBoolean("periodic_email_scanning_enabled", enabled)
                .apply()
            Log.d("TaskViewModel", "Periodic email scanning setting saved: $enabled")
        } catch (e: Exception) {
            Log.e("TaskViewModel", "Error saving periodic email scanning setting: ${e.message}")
        }

        if (enabled) {
            // Start periodic email scanning
            startPeriodicEmailScanning()
        } else {
            // Stop periodic email scanning when disabled
            stopPeriodicEmailScanning()
        }
    }

    // Load user preferences from SharedPreferences
    private fun loadUserPreferences() {
        try {
            val sharedPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)

            // Load Google Calendar sync setting
            isGoogleCalendarSyncEnabled = sharedPrefs.getBoolean("google_calendar_sync_enabled", false)
            Log.d("TaskViewModel", "Loaded Google Calendar sync setting: $isGoogleCalendarSyncEnabled")

            // Load periodic email scanning setting
            isPeriodicEmailScanningEnabled = sharedPrefs.getBoolean("periodic_email_scanning_enabled", true)
            Log.d("TaskViewModel", "Loaded periodic email scanning setting: $isPeriodicEmailScanningEnabled")

        } catch (e: Exception) {
            Log.e("TaskViewModel", "Error loading user preferences: ${e.message}")
            // Use default values if loading fails
            isGoogleCalendarSyncEnabled = false
            isPeriodicEmailScanningEnabled = true
        }
    }
    
    private val _lastSyncTime = mutableStateOf<Date?>(null)
    val lastSyncTime get() = _lastSyncTime.value

    // Add periodic sync job management
    private var periodicSyncJob: kotlinx.coroutines.Job? = null

    // Add periodic email scanning job management
    private var periodicEmailScanJob: kotlinx.coroutines.Job? = null

    // Add persistent state for email scanning preference
    var isPeriodicEmailScanningEnabled by mutableStateOf(true)
        private set

    // Method to check for cloud data sync
    fun checkForCloudDataSync() {
        viewModelScope.launch {
            try {
                Log.d("TaskViewModel", "Checking for cloud data sync...")

                val sharedPrefs = context.getSharedPreferences("backup_preferences", Context.MODE_PRIVATE)
                val connectedAccount = sharedPrefs.getString("connected_gmail_account", null)

                if (connectedAccount != null) {
                    // Check if there's newer data in the cloud
                    val lastLocalBackup = backupService.getLastBackupDate()?.time ?: 0

                    // Try to get latest backup from cloud
                    when (val result = backupService.listGoogleDriveBackups(connectedAccount)) {
                        is com.taskiq.app.service.BackupResult.Success -> {
                            val latestCloudBackup = result.data.maxByOrNull { it.createdTime }

                            if (latestCloudBackup != null && latestCloudBackup.createdTime > lastLocalBackup) {
                                Log.d("TaskViewModel", "Found newer cloud data, syncing...")

                                // Download and restore the latest backup
                                when (val downloadResult = backupService.downloadGoogleDriveBackup(latestCloudBackup.id, connectedAccount)) {
                                    is com.taskiq.app.service.BackupResult.Success -> {
                                        when (val restoreResult = backupService.restoreFromBackup(downloadResult.data)) {
                                            is com.taskiq.app.service.BackupResult.Success -> {
                                                Log.d("TaskViewModel", "Cloud data sync completed successfully")
                                                // Reload all data after restore
                                                loadTasks()
                                                loadImportantDates()
                                                loadBills()
                                            }
                                            is com.taskiq.app.service.BackupResult.Error -> {
                                                Log.e("TaskViewModel", "Failed to restore cloud data: ${restoreResult.message}")
                                            }
                                        }
                                    }
                                    is com.taskiq.app.service.BackupResult.Error -> {
                                        Log.e("TaskViewModel", "Failed to download cloud backup: ${downloadResult.message}")
                                    }
                                }
                            } else {
                                Log.d("TaskViewModel", "Local data is up to date")
                            }
                        }
                        is com.taskiq.app.service.BackupResult.Error -> {
                            Log.d("TaskViewModel", "No cloud backups found or error: ${result.message}")
                        }
                    }
                } else {
                    Log.d("TaskViewModel", "No connected account for cloud sync")
                }
            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error during cloud data sync: ${e.message}")
            }
        }
    }

    // Method to trigger automatic backup when data changes
    private fun triggerAutoBackup() {
        viewModelScope.launch {
            try {
                // Check if auto backup is enabled and we have a connected account
                val sharedPrefs = context.getSharedPreferences("backup_preferences", Context.MODE_PRIVATE)
                val autoBackupEnabled = backupService.isAutoBackupEnabled()
                val connectedAccount = sharedPrefs.getString("connected_gmail_account", null)

                if (autoBackupEnabled && connectedAccount != null) {
                    Log.d("TaskViewModel", "Data changed, triggering automatic backup")

                    // Create backup with current data
                    when (val backupResult = backupService.createBackup()) {
                        is com.taskiq.app.service.BackupResult.Success -> {
                            // Upload to Google Drive
                            when (val uploadResult = backupService.backupToGoogleDrive(backupResult.data, connectedAccount)) {
                                is com.taskiq.app.service.BackupResult.Success -> {
                                    Log.d("TaskViewModel", "Automatic backup uploaded successfully")
                                }
                                is com.taskiq.app.service.BackupResult.Error -> {
                                    Log.e("TaskViewModel", "Failed to upload automatic backup: ${uploadResult.message}")
                                }
                            }
                        }
                        is com.taskiq.app.service.BackupResult.Error -> {
                            Log.e("TaskViewModel", "Failed to create automatic backup: ${backupResult.message}")
                        }
                    }
                } else {
                    Log.d("TaskViewModel", "Auto backup not enabled or no connected account")
                }
            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error during automatic backup: ${e.message}")
            }
        }
    }
    
    // Mock data for demo
    private val mockTasks = listOf(
        Task(
            id = UUID.randomUUID().toString(),
            title = "Complete Project Proposal",
            description = "Finish the marketing project proposal for the client meeting",
            dueDate = Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(0)),
            frequency = TaskFrequency.ONE_TIME,
            priority = TaskPriority.HIGH,
            isCompleted = false,
            userId = "user123",
            createdAt = Date()
        )
    )
    
    // Add a new property to track date task creation in progress to prevent race conditions
    private val _dateTaskCreationInProgress = mutableSetOf<String>()
    
    // Lock object to synchronize operations that might create duplicate tasks
    private val addImportantDateLock = Any()
    private val dateProcessingSet = mutableSetOf<String>()
    
    // Add a set to track deleted date tasks by their identifier (date title + date)
    private val manuallyDeletedDateTasks = mutableSetOf<String>()
    
    // Add this set at the class level (at the top of the class with other properties)
    // Set to keep track of deleted recurring tasks to prevent future occurrences
    private val manuallyDeletedRecurringTasks = mutableSetOf<String>()
    
    init {
        // Load user preferences first
        loadUserPreferences()

        loadTasks()
        loadImportantDates()
        loadBills()
        loadLinkedEmails()

        // Run a background process to ensure data is loaded properly
        ensureDataIntegrity()

        // Start periodic sync if Google Calendar sync is enabled
        if (isGoogleCalendarSyncEnabled) {
            startPeriodicSync()
        }

        // Start periodic email scanning if enabled
        if (isPeriodicEmailScanningEnabled) {
            startPeriodicEmailScanning()
        }
    }
    
    private fun ensureDataIntegrity() {
        viewModelScope.launch {
            // Wait a bit to ensure initial loading is complete
            delay(1500)
            
            // Check if dates list is suspiciously empty
            if (_importantDates.isEmpty()) {
                Log.w("TaskViewModel", "Data integrity check: Important dates list is empty after init, forcing reload")
                loadImportantDates()
                
                // Wait again and verify
                delay(1000)
                if (_importantDates.isEmpty()) {
                    Log.e("TaskViewModel", "Data integrity alert: Still no dates after reload, trying emergency recovery")
                    
                    // Try emergency recovery directly
                    val recoverySuccessful = taskService.emergencyDataRecovery()
                    
                    if (recoverySuccessful) {
                        Log.d("TaskViewModel", "Emergency recovery successful, reloading dates")
                        loadImportantDates()
                    }
                }
            }
            
            // Force a save regardless to ensure persistence
            forceSaveAllData()
        }
    }
    
    private fun loadInitialData() {
        viewModelScope.launch {
            // First sync from cloud if user is logged in
            syncDataFromCloud()

            loadTasks()
            loadImportantDates()
            loadBills()
            loadLinkedEmails()

            // Process recurring tasks that are past their due date
            processRecurringTasks()
        }
    }

    /**
     * Sync all data from Supabase cloud storage
     * This should be called when app starts or when user logs in
     */
    fun syncDataFromCloud() {
        viewModelScope.launch {
            try {
                Log.d("TaskViewModel", "Starting cloud data sync...")

                // Sync tasks and important dates
                val taskSyncSuccess = taskService.syncFromCloud()

                // Sync bills and linked emails
                val billSyncSuccess = billService.syncFromCloud()

                if (taskSyncSuccess && billSyncSuccess) {
                    Log.d("TaskViewModel", "Successfully synced all data from cloud")

                    // Reload local data to reflect cloud changes
                    loadTasks()
                    loadImportantDates()
                    loadBills()
                    loadLinkedEmails()
                } else {
                    Log.w("TaskViewModel", "Some data failed to sync from cloud")
                }

            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error syncing data from cloud: ${e.message}", e)
            }
        }
    }
    
    fun loadTasks() {
        viewModelScope.launch {
            isLoading = true
            
            // Simulate network call
            delay(1000)
            
            // Placeholder: Load tasks from service
            val loadedTasks = taskService.getTasks()
            
            // CRITICAL: Filter out duplicate date reminder tasks before even adding them to the list
            val dateReminderGroups = loadedTasks
                .filter { it.title.startsWith("Reminder for ", ignoreCase = true) }
                .groupBy { "${it.title.lowercase().trim()}:${it.dueDate?.time ?: 0}" }
            
            // Create a list of tasks to keep - just one per date key (the newest/most complete one)
            val uniqueDateReminders = dateReminderGroups.map { (key, tasks) ->
                if (tasks.size > 1) {
                    Log.d("TaskViewModel", "LOAD: Found ${tasks.size} duplicates for $key, keeping only the best one")
                    // Keep the task with most subtasks or the newest one
                    tasks.maxByOrNull { t -> t.subtasks.size * 100 + (t.createdAt.time / 1000) } ?: tasks.first()
                } else {
                    tasks.first()
                }
            }
            
            // Other tasks that aren't date reminders
            val nonDateReminders = loadedTasks.filter { !it.title.startsWith("Reminder for ", ignoreCase = true) }
            
            // Combine non-date reminders with the unique date reminders
            val uniqueTasks = nonDateReminders + uniqueDateReminders
            
            // Clear and add the de-duplicated list
            _tasks.clear()
            _tasks.addAll(uniqueTasks)
            
            Log.d("TaskViewModel", "Loaded ${uniqueTasks.size} tasks (filtered out ${loadedTasks.size - uniqueTasks.size} duplicates)")
            
            // Also delete any duplicates from the database
            val duplicatesToDelete = loadedTasks.filter { task -> 
                task.title.startsWith("Reminder for ", ignoreCase = true) && 
                !uniqueDateReminders.any { it.id == task.id }
            }
            
            // Delete duplicates from database
            for (duplicate in duplicatesToDelete) {
                Log.d("TaskViewModel", "LOAD: Deleting duplicate task from database: ${duplicate.title}")
                taskService.deleteTask(duplicate.id)
            }
            
            // Immediately trigger a full duplicate cleanup to catch any edge cases
            forceCleanupAllDuplicates()
            
            isLoading = false
        }
    }
    
    fun loadImportantDates() {
        viewModelScope.launch {
            Log.d("TaskViewModel", "Loading important dates...")
            isLoading = true
            
            // Get important dates from the service
            val loadedDates = taskService.getImportantDates()
            Log.d("TaskViewModel", "Loaded ${loadedDates.size} dates from TaskService")
            
            // Try to restore from individual date files if main storage has issues
            if (loadedDates.isEmpty()) {
                Log.w("TaskViewModel", "No dates found in main storage, trying emergency recovery")
                
                // Try emergency recovery from TaskService first
                val recoverySuccess = taskService.emergencyDataRecovery()
                
                if (recoverySuccess) {
                    Log.d("TaskViewModel", "Emergency recovery successful, reloading dates from service")
                    val recoveredDates = taskService.getImportantDates()
                    if (recoveredDates.isNotEmpty()) {
                        _importantDates.clear()
                        _importantDates.addAll(recoveredDates)
                        Log.d("TaskViewModel", "Recovered ${recoveredDates.size} dates through service recovery")
                        // Check for duplicates in recovered dates
                        if (hasDuplicates(recoveredDates)) {
                            Log.w("TaskViewModel", "Found duplicates in recovered dates, running cleanup")
                            cleanupDuplicateDates(recoveredDates)
                        }
                        
                        // Force a save after recovery to ensure persistence
                        forceSaveAllData()
                        isLoading = false
                        return@launch
                    }
                }
            }
            
            // Clear and add all loaded dates
            _importantDates.clear()
            _importantDates.addAll(loadedDates)
            
            // CRITICAL: Do a special cleanup for date-task pairs
            // Check each important date to make sure it has exactly one corresponding task
            synchronizeDateTaskPairs()
            
            // Process recurring dates after loading
            processRecurringDates()
            
            // Force a save after loading to ensure persistence
            forceSaveAllData()
            
            // Double-check that dates are actually saved
            delay(300)
            forceSaveAllData()
            
            Log.d("TaskViewModel", "Loaded ${_importantDates.size} important dates")
            isLoading = false
        }
    }
    
    // Helper to check for duplicates in a date list
    private fun hasDuplicates(dates: List<Task>): Boolean {
        val titleDateMap = mutableMapOf<String, Int>()
        
        dates.forEach { date ->
            val key = "${date.title.lowercase()}:${date.dueDate?.time ?: 0}"
            titleDateMap[key] = (titleDateMap[key] ?: 0) + 1
        }
        
        return titleDateMap.any { it.value > 1 }
    }
    
    // Helper to clean up duplicate dates
    private fun cleanupDuplicateDates(dates: List<Task>) {
        // Group by title + date key
        val dateGroups = dates.groupBy { date ->
            "${date.title.lowercase()}:${date.dueDate?.time ?: 0}"
        }
        
        // For each group with duplicates, keep only the best one
        dateGroups.forEach { (key, duplicates) ->
            if (duplicates.size > 1) {
                Log.d("TaskViewModel", "Found ${duplicates.size} duplicates for date key: $key")
                
                // Keep the one with most subtasks or most recent
                val bestDate = duplicates.maxByOrNull { date ->
                    date.subtasks.size * 100 + (date.createdAt?.time ?: 0) / 1000
                } ?: duplicates.first()
                
                // Remove all others
                duplicates.filter { it.id != bestDate.id }.forEach { duplicate ->
                    _importantDates.removeAll { it.id == duplicate.id }
                    taskService.deleteImportantDate(duplicate.id)
                }
            }
        }
    }
    
    // New method to ensure proper 1:1 relationship between dates and their tasks
    private fun synchronizeDateTaskPairs() {
        Log.d("TaskViewModel", "Starting synchronization of date-task pairs")
        val tasksToRemove = mutableListOf<Task>()
        
        // First get all date-related tasks
        val dateReminderTasks = _tasks.filter { 
            it.title.startsWith("Reminder for ", ignoreCase = true) 
        }
        
        // Process each important date
        for (date in _importantDates.filter { it.subtasks.isNotEmpty() }) {
            val exactTitle = "Reminder for ${date.title}"
            
            // Find all tasks matching this date
            val matchingTasks = dateReminderTasks.filter { task ->
                task.title.equals(exactTitle, ignoreCase = true) && 
                isSameDate(task.dueDate, date.dueDate)
            }
            
            when {
                // No task exists for this date - create one using syncDateWithTask
                matchingTasks.isEmpty() -> {
                    Log.d("TaskViewModel", "SYNC: Creating missing task for date: ${date.title}")
                    syncDateWithTask(date)
                }
                
                // Multiple tasks exist - keep the best one, remove others
                matchingTasks.size > 1 -> {
                    Log.d("TaskViewModel", "SYNC: Found ${matchingTasks.size} tasks for date: ${date.title}")
                    
                    // Find the best task to keep
                    val bestTask = matchingTasks.maxByOrNull { task ->
                        task.subtasks.size * 100 + (task.createdAt.time / 1000)
                    } ?: matchingTasks.first()
                    
                    // Make sure the best task has up-to-date subtasks
                    val taskIndex = _tasks.indexOfFirst { it.id == bestTask.id }
                    if (taskIndex != -1) {
                        Log.d("TaskViewModel", "SYNC: Updating best task with latest subtasks")
                        val updatedTask = bestTask.copy(
                            subtasks = date.subtasks,
                            isCompleted = date.isCompleted,
                            completedAt = date.completedAt
                        )
                        _tasks[taskIndex] = updatedTask
                        taskService.updateTask(updatedTask)
                    }
                    
                    // Remove all others
                    val duplicates = matchingTasks.filter { it.id != bestTask.id }
                    tasksToRemove.addAll(duplicates)
                    Log.d("TaskViewModel", "SYNC: Marking ${duplicates.size} duplicate tasks for removal")
                }
                
                // Exactly one task exists - make sure subtasks and completion status are in sync
                else -> {
                    val existingTask = matchingTasks.first()
                    
                    // Only update if the subtasks don't match or completion status doesn't match
                    if (existingTask.subtasks != date.subtasks || existingTask.isCompleted != date.isCompleted) {
                        Log.d("TaskViewModel", "SYNC: Updating task to match date: ${date.title}")
                        val taskIndex = _tasks.indexOfFirst { it.id == existingTask.id }
                        if (taskIndex != -1) {
                            val updatedTask = existingTask.copy(
                                subtasks = date.subtasks,
                                isCompleted = date.isCompleted,
                                completedAt = date.completedAt
                            )
                            _tasks[taskIndex] = updatedTask
                            taskService.updateTask(updatedTask)
                        }
                    }
                }
            }
        }
        
        // Now look for orphaned date tasks (tasks with no corresponding date)
        for (dateTask in dateReminderTasks) {
            val titlePart = dateTask.title.substringAfter("Reminder for ", "").trim()
            
            // Look for a matching date
            val matchingDate = _importantDates.any { date ->
                date.title.equals(titlePart, ignoreCase = true) && 
                isSameDate(date.dueDate, dateTask.dueDate) &&
                date.subtasks.isNotEmpty()
            }
            
            // If no matching date exists, this task is orphaned
            if (!matchingDate && !tasksToRemove.contains(dateTask)) {
                Log.d("TaskViewModel", "SYNC: Found orphaned date task with no matching date: ${dateTask.title}")
                tasksToRemove.add(dateTask)
            }
        }
        
        // Remove the duplicates and orphaned tasks
        for (task in tasksToRemove) {
            Log.d("TaskViewModel", "SYNC: Removing duplicate/orphaned task: ${task.title}")
            _tasks.removeAll { it.id == task.id }
            taskService.deleteTask(task.id)
        }
        
        Log.d("TaskViewModel", "Completed synchronization of date-task pairs")
    }
    
    // Process recurring tasks (both regular tasks and due hourly tasks)
    private fun processRecurringTasks() {
        Log.d("TaskViewModel", "Processing recurring tasks...")
        
        val now = Date()
        val calendar = Calendar.getInstance()
        calendar.time = now
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfToday = calendar.time
        
        // We need to process:
        // 1. Any task whose due date is in the past but hasn't been completed
        // 2. And is a recurring task (not one-time)
        // 3. And doesn't already have a "future" instance
        
        // First, group tasks by title and frequency to identify task series
        val taskGroups = _tasks.groupBy { task -> 
            "${task.title.lowercase().trim()}:${task.frequency?.name ?: "ONE_TIME"}"
        }
        
        // Identify past recurring tasks that don't already have future instances
        val pastRecurringTasks = mutableListOf<Task>()
        
        // Process each group of similar tasks
        taskGroups.forEach { (groupKey, tasksInGroup) ->
            // Skip one-time tasks and manually deleted recurring tasks
            if (groupKey.endsWith(":ONE_TIME") || 
                manuallyDeletedRecurringTasks.contains(groupKey)) {
                return@forEach
            }
            
            // Skip date reminder tasks - these are handled differently
            if (groupKey.startsWith("reminder for ")) {
                return@forEach
            }
            
            // Find the oldest overdue task in this group
            val pastTasks = tasksInGroup.filter { task ->
                task.dueDate?.before(startOfToday) ?: false
            }
            
            // Find any future instances of this task (using exact time comparison to avoid duplicates)
            val futureTasks = tasksInGroup.filter { task ->
                task.dueDate?.after(now) ?: false
            }
            
            // Only process if there are past tasks but no future tasks
            if (pastTasks.isNotEmpty() && futureTasks.isEmpty()) {
                // Get the most recent past task
                val oldestPastTask = pastTasks.maxByOrNull { it.dueDate?.time ?: 0 }
                if (oldestPastTask != null) {
                    pastRecurringTasks.add(oldestPastTask)
                }
            }
        }
        
        Log.d("TaskViewModel", "Found ${pastRecurringTasks.size} recurring tasks that need new instances")
        
        // Temporary collection to hold new tasks to avoid concurrent modification issues
        val newTasks = mutableListOf<Task>()
        
        // Process each past recurring task to create the next occurrence
        pastRecurringTasks.forEach { pastTask ->
            // Create the next occurrence of this task
            val nextOccurrence = createNextOccurrence(pastTask)
            if (nextOccurrence.dueDate != null) {
                // Only add future occurrences (prevent generating past occurrences)
                if (nextOccurrence.dueDate.after(now)) {
                    // Check if a task with this exact due date and title already exists (more precise check)
                    val exactDuplicateExists = _tasks.any { existingTask -> 
                        existingTask.title == nextOccurrence.title && 
                        existingTask.dueDate?.time == nextOccurrence.dueDate?.time &&
                        existingTask.frequency == nextOccurrence.frequency 
                    }
                    
                    if (!exactDuplicateExists) {
                        // Add to our collection of new tasks
                        newTasks.add(nextOccurrence)
                        
                        // Mark the original task as "processed" so we don't create duplicates
                        // but DON'T mark it as completed so it still shows as overdue
                        val taskIndex = _tasks.indexOfFirst { it.id == pastTask.id }
                        if (taskIndex != -1) {
                            // Update createdAt timestamp to mark it as processed
                            val updatedTask = pastTask.copy(
                                createdAt = Date() // Update timestamp to prevent processing it again
                            )
                            _tasks[taskIndex] = updatedTask
                            taskService.updateTask(updatedTask)
                            
                            // Log for debugging
                            Log.d("TaskViewModel", "Created next occurrence for overdue task: ${pastTask.title} - New due date: ${nextOccurrence.dueDate}")
                        }
                    } else {
                        Log.d("TaskViewModel", "Skipping duplicate task creation for: ${pastTask.title} - Due date already exists")
                    }
                } else {
                    Log.d("TaskViewModel", "Skipping next occurrence for task ${pastTask.title} as it's already in the past")
                }
            }
        }
        
        // Add all the new tasks at once
        newTasks.forEach { newTask ->
            // Check if this task already exists to avoid duplicate tasks
            val exists = _tasks.any { existingTask -> 
                existingTask.title == newTask.title && 
                existingTask.dueDate?.time == newTask.dueDate?.time &&
                existingTask.frequency == newTask.frequency 
            }
            
            if (!exists) {
                // Add to backend service
                taskService.addTask(newTask)

                // Add to local list
                _tasks.add(newTask)
                
                // Register notifications for the new task occurrence using NotificationRegistrar
                val notificationRegistrar = NotificationRegistrar(getApplication<Application>())
                notificationRegistrar.registerTaskNotification(newTask)
                
                // Schedule reminder (keeping this for backward compatibility)
                if (newTask.reminderTime != null) {
                    if (newTask.frequency == TaskFrequency.HOURLY || 
                       (newTask.frequency == TaskFrequency.CUSTOM && newTask.customFrequencyDays != null && newTask.customFrequencyDays < 24)) {
                        taskWorkerManager.scheduleTaskReminder(newTask)
                    } else {
                        notificationService.scheduleTaskReminder(newTask)
                    }
                }
            }
        }
        
        // After processing regular recurring tasks, clean up any date-related tasks
        // to ensure there are no duplicates
        cleanupDuplicateTasksForDates()
    }
    
    // Add this method to handle recurring dates
    private fun processRecurringDates() {
        // Current date for comparison
        val today = Calendar.getInstance().time
        val todayCal = Calendar.getInstance().apply { time = today }
        
        // Reset time portion to compare dates only
        todayCal.set(Calendar.HOUR_OF_DAY, 0)
        todayCal.set(Calendar.MINUTE, 0)
        todayCal.set(Calendar.SECOND, 0)
        todayCal.set(Calendar.MILLISECOND, 0)
        
        // Find dates that have passed and have recurrence settings
        val pastRecurringDates = _importantDates.filter { date ->
            if (date.frequency == TaskFrequency.ONE_TIME) return@filter false
            
            date.dueDate?.let { dueDate ->
                val dueCal = Calendar.getInstance().apply { time = dueDate }
                // Reset time portion to compare dates only
                dueCal.set(Calendar.HOUR_OF_DAY, 0)
                dueCal.set(Calendar.MINUTE, 0)
                dueCal.set(Calendar.SECOND, 0)
                dueCal.set(Calendar.MILLISECOND, 0)
                
                // Check if the date has passed
                dueCal.before(todayCal)
            } ?: false
        }
        
        Log.d("TaskViewModel", "Found ${pastRecurringDates.size} past recurring dates to process")
        
        // Track processed dates to prevent duplicates
        val processedDateKeys = mutableSetOf<String>()
        
        // Collect new dates to add
        val newDates = mutableListOf<Task>()
        
        // For each past recurring date, create a new occurrence
        pastRecurringDates.forEach { pastDate ->
            // Create a unique key for this date to prevent duplicates
            val dateKey = "${pastDate.title.lowercase()}:${pastDate.dueDate?.time ?: 0}"
            
            // Skip if we've already processed a date with the same title and day
            if (processedDateKeys.contains(dateKey)) {
                Log.d("TaskViewModel", "Skipping already processed date: ${pastDate.title}")
                return@forEach
            }
            
            // Mark as processed
            processedDateKeys.add(dateKey)
            
            // Create a calendar for the new date calculation
            val cal = Calendar.getInstance().apply { time = pastDate.dueDate!! }
            
            // Calculate next occurrence based on frequency
            when (pastDate.frequency) {
                TaskFrequency.MONTHLY -> {
                    do {
                        cal.add(Calendar.MONTH, 1)
                    } while (cal.time.before(today))
                }
                TaskFrequency.YEARLY -> {
                    do {
                        cal.add(Calendar.YEAR, 1)
                    } while (cal.time.before(today))
                }
                TaskFrequency.CUSTOM -> {
                    val customDays = pastDate.customFrequencyDays ?: 28
                    do {
                        cal.add(Calendar.DAY_OF_YEAR, customDays)
                    } while (cal.time.before(today))
                }
                else -> return@forEach // Skip for non-recurring or unsupported frequencies
            }
            
            // Check if this new date already exists to prevent duplicates
            val newDueDate = cal.time
            val exists = _importantDates.any { existingDate -> 
                existingDate.title == pastDate.title && 
                isSameDate(existingDate.dueDate, newDueDate)
            }
            
            if (!exists) {
            // Create a new date with updated due date
            val newDate = pastDate.copy(
                id = UUID.randomUUID().toString(),
                    dueDate = newDueDate,
                isCompleted = false,
                createdAt = Date()
            )
            
            // Add to our collection of new dates
            newDates.add(newDate)
                
                Log.d("TaskViewModel", "Created next occurrence for date: ${pastDate.title} - New due date: ${newDueDate}")
            } else {
                Log.d("TaskViewModel", "Skipped creating duplicate date: ${pastDate.title} - Already exists for ${newDueDate}")
            }
        }
        
        // Add all the new dates at once
        if (newDates.isNotEmpty()) {
            Log.d("TaskViewModel", "Adding ${newDates.size} new recurring dates")
            
        newDates.forEach { newDate ->
            // Add to backend service
            taskService.addImportantDate(newDate)

            // Add to local list
            _importantDates.add(newDate)
            
            // Schedule notification
            notificationService.scheduleImportantDateReminder(newDate)
                
                // If the date has subtasks, create a corresponding task
                if (newDate.subtasks.isNotEmpty()) {
                    syncDateWithTask(newDate)
                }
            }
            
            // Clean up any duplicate tasks that might have been created
            cleanupDuplicateTasksForDates()
        }
    }
    
    fun loadBills() {
        viewModelScope.launch {
            // Simulate network call
            delay(800)
            
            // Load bills from service
            val loadedBills = billService.getBills()
            _bills.value = loadedBills
        }
    }
    
    fun loadLinkedEmails() {
        viewModelScope.launch {
            android.util.Log.d("TaskViewModel", "Loading linked emails from BillService")

            // Load linked emails from service
            val emails = billService.getLinkedEmails()

            android.util.Log.d("TaskViewModel", "Loaded ${emails.size} emails from BillService: $emails")
            android.util.Log.d("TaskViewModel", "Current _linkedEmails state before update: ${_linkedEmails.value}")

            _linkedEmails.value = emails

            android.util.Log.d("TaskViewModel", "Updated _linkedEmails state: ${_linkedEmails.value}")
        }
    }
    
    // Check for duplicate task
    fun isDuplicateTask(task: Task): Boolean {
        return _tasks.any { existingTask -> 
            existingTask.id != task.id && // Not the same task by ID
            existingTask.title == task.title && 
            (existingTask.description == task.description || (existingTask.description.isBlank() && task.description.isBlank())) &&
            isSameDate(existingTask.dueDate, task.dueDate) &&
            existingTask.frequency == task.frequency
        }
    }
    
    // Helper method to compare dates ignoring time components
    private fun isSameDate(date1: Date?, date2: Date?): Boolean {
        if (date1 == null && date2 == null) return true
        if (date1 == null || date2 == null) return false
        
        val cal1 = Calendar.getInstance().apply { time = date1 }
        val cal2 = Calendar.getInstance().apply { time = date2 }
        
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
               cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH)
    }
    
    // Remove duplicate tasks from the task list - enhanced version
    fun removeDuplicateTasks() {
        Log.d("TaskViewModel", "Running standard duplicate task cleanup")
        val uniqueTasks = mutableListOf<Task>()
        val tasksToRemove = mutableListOf<Task>()
        
        // Create a map to track tasks by their unique key
        val taskMap = mutableMapOf<String, Task>()
        
        // First pass: find duplicates that have exactly the same title and date
        _tasks.forEach { task ->
            // Create a unique key for this task based on title and date
            val taskKey = "${task.title.lowercase()}:${task.dueDate?.time ?: 0}"
            
            if (taskMap.containsKey(taskKey)) {
                // We already have a task with this key - keep the newer one
                val existingTask = taskMap[taskKey]!!
                
                if (task.createdAt.time > existingTask.createdAt.time) {
                    // This task is newer, replace the existing one
                    taskMap[taskKey] = task
                    tasksToRemove.add(existingTask)
                    Log.d("TaskViewModel", "Found newer duplicate: ${task.title}, removing older version")
            } else {
                    // This task is older, remove it
                tasksToRemove.add(task)
                    Log.d("TaskViewModel", "Found older duplicate: ${task.title}, keeping newer version")
            }
            } else {
                // First time seeing this task
                taskMap[taskKey] = task
        }
        }
        
        // Remove the identified duplicates
        if (tasksToRemove.isNotEmpty()) {
            Log.d("TaskViewModel", "Removing ${tasksToRemove.size} exact duplicate tasks")
            tasksToRemove.forEach { task ->
                _tasks.removeAll { it.id == task.id }
                taskService.deleteTask(task.id)
            }
        }
        
        // Special handling for date reminder tasks
        cleanupDuplicateTasksForDates()
        
        Log.d("TaskViewModel", "Completed standard duplicate task cleanup")
    }
    
    // Update addTask to prevent adding duplicates
    fun addTask(task: Task): Boolean {
        // Check if this task is a duplicate
        if (isDuplicateTask(task)) {
            println("Prevented adding duplicate task: ${task.title}")
            return false // Don't add duplicate task
        }

        // Check task limits before adding
        if (!checkTaskLimits()) {
            val incompleteTasks = _tasks.filter { !it.isCompleted }
            val scheduledTasks = incompleteTasks.filter { it.dueDate != null }
            val unplannedTasks = incompleteTasks.filter { it.dueDate == null }

            val limitMessage = if (task.dueDate != null && scheduledTasks.size >= 100) {
                "Cannot add more scheduled tasks. Maximum limit of 100 scheduled tasks reached."
            } else if (task.dueDate == null && unplannedTasks.size >= 100) {
                "Cannot add more unplanned tasks. Maximum limit of 100 unplanned tasks reached."
            } else {
                "Task limit reached."
            }

            Log.w("TaskViewModel", limitMessage)
            return false
        }
        
        viewModelScope.launch {
            // Add to backend service
            taskService.addTask(task)

            // Add to local list
            _tasks.add(task)

            Log.d("TaskViewModel", "Task added successfully: ${task.title}")

            // Debug log for notification scheduling
            Log.d("TaskViewModel", "Scheduling notification for newly added task: ${task.title}, Priority: ${task.priority}")

            // Register notification for the new task
            val notificationRegistrar = NotificationRegistrar(getApplication<Application>())
            notificationRegistrar.registerTaskNotification(task)

            // Schedule reminder if needed (keeping this for backward compatibility)
            if (task.reminderTime != null) {
                if (task.frequency == TaskFrequency.HOURLY ||
                   (task.frequency == TaskFrequency.CUSTOM && task.customFrequencyDays != null && task.customFrequencyDays < 24)) {
                    taskWorkerManager.scheduleTaskReminder(task)
                } else {
                    notificationService.scheduleTaskReminder(task)
                }
            } else if (task.priority == TaskPriority.HIGH) {
                // For high priority tasks without reminder time, add a default reminder for the due date
                Log.d("TaskViewModel", "Setting default reminder time for high priority task without reminder")
                val calendar = Calendar.getInstance()
                if (task.dueDate != null) {
                    calendar.time = task.dueDate
                } else {
                    // If no due date, set it for 5 minutes from now
                    calendar.add(Calendar.MINUTE, 5)
                }
                calendar.set(Calendar.SECOND, 0)

                val taskWithReminder = task.copy(
                    reminderTime = calendar.time
                )
                notificationService.scheduleTaskReminder(taskWithReminder)
            }

            // Trigger automatic backup when data changes
            triggerAutoBackup()
        }
        return true
    }
    
    // Helper function to check task limits
    private fun checkTaskLimits(): Boolean {
        val incompleteTasks = _tasks.filter { !it.isCompleted }
        val scheduledTasks = incompleteTasks.filter { it.dueDate != null }
        val unplannedTasks = incompleteTasks.filter { it.dueDate == null }

        return scheduledTasks.size < 100 && unplannedTasks.size < 100
    }

    // Helper function to get completed tasks limited to last 50
    fun getCompletedTasks(): List<Task> {
        return _tasks.filter { it.isCompleted }
            .sortedByDescending { it.completedAt ?: it.createdAt }
            .take(50)
    }

    // Helper function to calculate the color at time of completion
    private fun calculateTaskCompletionColor(task: Task): String {
        val todayCal = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val taskCal = Calendar.getInstance().apply {
            time = task.dueDate ?: todayCal.time
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        val daysDifference = ((taskCal.timeInMillis - todayCal.timeInMillis) / (24 * 60 * 60 * 1000))

        // Check if this is a date item (has "Reminder for" prefix)
        val isDate = task.title.startsWith("Reminder for")

        return if (isDate) {
            when {
                daysDifference < 0 -> "#FF0000"  // Red - Past date
                daysDifference == 0L -> "#FF6D00"  // Orange - Today
                daysDifference <= 3 -> "#FFB300"  // Amber - Within 3 days
                daysDifference <= 7 -> "#2196F3"  // Blue - Within a week
                else -> "#6200EE"  // Primary - Beyond a week
            }
        } else {
            when (task.priority) {
                TaskPriority.HIGH -> "#FF4081"  // Pink
                TaskPriority.MEDIUM -> "#AA66CC"  // Purple
                TaskPriority.LOW -> "#4CAF50"  // Green
            }
        }
    }

    fun toggleTaskCompletion(taskId: String) {
        viewModelScope.launch {
            // First try to find in regular tasks
            val taskIndex = _tasks.indexOfFirst { it.id == taskId }

            if (taskIndex != -1) {
                val task = _tasks[taskIndex]
                val isCompleting = !task.isCompleted

                Log.d("TaskViewModel", "Toggling task completion: ${task.title}, new state: $isCompleting")

                // Calculate completion color if completing
                val completionColor = if (isCompleting) {
                    calculateTaskCompletionColor(task)
                } else null

                // Create updated task with completion status + timestamp if completing
                val updatedTask = task.copy(
                    isCompleted = isCompleting,
                    completedAt = if (isCompleting) Date() else null,
                    completionColor = completionColor
                )
                
                // Update in local list first
                _tasks[taskIndex] = updatedTask
                
                // Update in backend
                val updateSuccess = taskService.updateTask(updatedTask)

                Log.d("TaskViewModel", "Task updated successfully: ${updatedTask.title}")
                
                while (!saveSuccess && saveAttempts <= 3) {
                    Log.d("TaskViewModel", "Force save attempt #$saveAttempts for task: ${task.title}")
                    taskService.forceSaveToDatabase()
                    
                    // Verify save worked by checking task state directly
                    val savedTask = taskService.getTask(taskId)
                    if (savedTask != null && savedTask.isCompleted == isCompleting) {
                        saveSuccess = true
                        Log.d("TaskViewModel", "Verified task completion state saved successfully")
                    } else {
                        Log.e("TaskViewModel", "Task completion state not saved correctly on attempt $saveAttempts")
                        saveAttempts++
                        if (saveAttempts <= 3) {
                            delay(100L * saveAttempts) // Add a small delay before retry
                        }
                    }
                }
                
                if (!saveSuccess) {
                    Log.e("TaskViewModel", "Failed to save task completion state after $saveAttempts attempts!")
                    // Try one more direct approach as last resort
                    taskService.updateTask(updatedTask)
                    taskService.forceSaveToDatabase()
                }
                
                Log.d("TaskViewModel", "Task update success: $updateSuccess, forced save executed for: ${task.title}")
                
                // If we're completing a recurring task, create the next occurrence
                if (isCompleting && updateSuccess && task.isRecurring()) {
                    handleRecurringTaskCompletion(task)
                }
                
                return@launch
            }
            
            // If not found in tasks, try to find in date tasks
            val dateIndex = _importantDates.indexOfFirst { it.id == taskId }
            if (dateIndex != -1) {
                val date = _importantDates[dateIndex]
                val isCompleting = !date.isCompleted
                
                Log.d("TaskViewModel", "Toggling date completion: ${date.title}, new state: $isCompleting")

                // Calculate completion color if completing
                val completionColor = if (isCompleting) {
                    calculateTaskCompletionColor(date)
                } else null

                // Create updated date with completion status + timestamp
                val updatedDate = date.copy(
                    isCompleted = isCompleting,
                    completedAt = if (isCompleting) Date() else null,
                    completionColor = completionColor
                )
                
                // Update in local list
                _importantDates[dateIndex] = updatedDate
                
                // Update in backend with multiple save attempts
                var updateSuccess = taskService.updateImportantDate(updatedDate)
                
                // Try up to 3 times to save the date change
                var saveAttempts = 1
                var saveSuccess = false
                
                while (!saveSuccess && saveAttempts <= 3) {
                    Log.d("TaskViewModel", "Force save attempt #$saveAttempts for date: ${date.title}")
                    taskService.forceSaveToDatabase()
                    
                    // Verify save worked
                    val savedDate = taskService.getImportantDate(taskId)
                    if (savedDate != null && savedDate.isCompleted == isCompleting) {
                        saveSuccess = true
                        Log.d("TaskViewModel", "Verified date completion state saved successfully")
                    } else {
                        Log.e("TaskViewModel", "Date completion state not saved correctly on attempt $saveAttempts")
                        saveAttempts++
                        if (saveAttempts <= 3) {
                            delay(100L * saveAttempts)
                        }
                    }
                }
                
                // Find and update the corresponding task if it exists
                val dateTaskTitle = "Reminder for ${date.title}"
                val dateTaskIndex = _tasks.indexOfFirst { 
                    it.title.equals(dateTaskTitle, ignoreCase = true) && 
                    isSameDate(it.dueDate, date.dueDate)
                }
                
                if (dateTaskIndex != -1) {
                    val dateTask = _tasks[dateTaskIndex]
                    val dateTaskCompletionColor = if (isCompleting) {
                        calculateTaskCompletionColor(dateTask)
                    } else null

                    val updatedDateTask = dateTask.copy(
                        isCompleted = isCompleting,
                        completedAt = if (isCompleting) Date() else null,
                        completionColor = dateTaskCompletionColor
                    )
                    
                    // Update in local list and backend
                    _tasks[dateTaskIndex] = updatedDateTask
                    taskService.updateTask(updatedDateTask)
                    
                    // Force immediate save
                    taskService.forceSaveToDatabase()
                    
                    Log.d("TaskViewModel", "Updated corresponding date task: ${updatedDateTask.title}")
                }
                
                return@launch
            }
            
            // Try to find in subtasks
            for (task in _tasks) {
                val subtaskIndex = task.subtasks.indexOfFirst { it.id == taskId }
                if (subtaskIndex != -1) {
                    toggleSubtaskCompletion(task.id, taskId)
                    return@launch
                }
            }
            
            for (date in _importantDates) {
                val subtaskIndex = date.subtasks.indexOfFirst { it.id == taskId }
                if (subtaskIndex != -1) {
                    toggleSubtaskCompletion(date.id, taskId)
                    return@launch
                }
            }
        }
    }
    
    // Helper function to create the next occurrence of a recurring task
    private fun createNextOccurrence(completedTask: Task): Task {
        val calendar = Calendar.getInstance()
        val baseDate = completedTask.dueDate ?: Date()
        val now = Date()

        // First set calendar to original due date
        calendar.time = baseDate
        
        // For past due tasks, we need to increment until we're in the future
        val newDueDate = when (completedTask.frequency) {
            TaskFrequency.DAILY -> {
                do {
                    calendar.add(Calendar.DAY_OF_YEAR, 1)
                } while (calendar.time.before(now))
                calendar.time
            }
            TaskFrequency.WEEKLY -> {
                do {
                    calendar.add(Calendar.WEEK_OF_YEAR, 1)
                } while (calendar.time.before(now))
                calendar.time
            }
            TaskFrequency.MONTHLY -> {
                do {
                    calendar.add(Calendar.MONTH, 1)
                } while (calendar.time.before(now))
                calendar.time
            }
            TaskFrequency.YEARLY -> {
                do {
                    calendar.add(Calendar.YEAR, 1)
                } while (calendar.time.before(now))
                calendar.time
            }
            TaskFrequency.HOURLY -> {
                // For hourly tasks, start from the original due date
                do {
                    calendar.add(Calendar.HOUR_OF_DAY, 1)
                } while (calendar.time.before(now))
                calendar.time
            }
            TaskFrequency.CUSTOM -> {
                val days = completedTask.customFrequencyDays ?: 0
                val hours = completedTask.customFrequencyHours ?: 0
                
                if (days > 0 || hours > 0) {
                    do {
                        // Add the days first
                        if (days > 0) {
                            calendar.add(Calendar.DAY_OF_YEAR, days)
                        }
                        
                        // Then add the hours
                        if (hours > 0) {
                            calendar.add(Calendar.HOUR_OF_DAY, hours)
                        }
                    } while (calendar.time.before(now))
                }
                calendar.time
            }
            else -> null
        }
        
        // Create a new task with same properties but new ID, reset completion status
        return completedTask.copy(
            id = UUID.randomUUID().toString(),
            isCompleted = false,
            dueDate = newDueDate,
            createdAt = Date(),
            // Reset subtasks completion status too
            subtasks = completedTask.subtasks.map { 
                it.copy(id = UUID.randomUUID().toString(), isCompleted = false) 
            }
        )
    }
    
    fun deleteTask(taskId: String) {
        viewModelScope.launch {
            // Remove from local list
            val taskIndex = _tasks.indexOfFirst { it.id == taskId }
            if (taskIndex != -1) {
                val task = _tasks[taskIndex]
                _tasks.removeAt(taskIndex)
                
                // If this is a date reminder task, record that it was manually deleted
                if (task.title.startsWith("Reminder for ", ignoreCase = true)) {
                    val dateKey = "${task.title.lowercase().trim()}:${task.dueDate?.time ?: 0}"
                    Log.d("TaskViewModel", "Recording manually deleted date task: $dateKey")
                    manuallyDeletedDateTasks.add(dateKey)
                    
                    // Also remove the subtasks from the corresponding date
                    val datePart = task.title.substringAfter("Reminder for ", "").trim()
                    val matchingDate = _importantDates.firstOrNull { date ->
                        date.title.equals(datePart, ignoreCase = true) && 
                        isSameDate(date.dueDate, task.dueDate)
                    }
                    
                    if (matchingDate != null && matchingDate.subtasks.isNotEmpty()) {
                        // Update the date to have no subtasks
                        val updatedDate = matchingDate.copy(subtasks = emptyList())
                        val dateIndex = _importantDates.indexOfFirst { it.id == matchingDate.id }
                        if (dateIndex != -1) {
                            _importantDates[dateIndex] = updatedDate
                            taskService.updateImportantDate(updatedDate)
                            Log.d("TaskViewModel", "Removed subtasks from date: ${matchingDate.title}")
                        }
                    }
                }
                
                // If this is a recurring task, record it to prevent future recurrences
                if (task.frequency != TaskFrequency.ONE_TIME) {
                    // Use a unique identifier based on task title and frequency
                    val recurringKey = "${task.title.lowercase().trim()}:${task.frequency.name}"
                    Log.d("TaskViewModel", "Recording manually deleted recurring task: $recurringKey")
                    manuallyDeletedRecurringTasks.add(recurringKey)
                }
                
                // Delete from backend (placeholder)
                taskService.deleteTask(taskId)
                
                // Cancel any scheduled notifications/workers
                if (task.frequency != TaskFrequency.ONE_TIME) {
                    taskWorkerManager.cancelTaskReminder(taskId)
                }

                // Trigger automatic backup when data changes
                triggerAutoBackup()
            }
        }
    }
    
    fun addImportantDate(importantDate: Task) {
        viewModelScope.launch {
            val dateKey = "${importantDate.title.lowercase()}:${importantDate.dueDate?.time ?: 0}"
            var needsCleanup = false
            var currentDate: Task? = null
            
            // Use a synchronization mechanism to prevent concurrent processing
            synchronized(addImportantDateLock) {
                if (dateProcessingSet.contains(dateKey)) {
                    Log.d("TaskViewModel", "BLOCKED duplicate date processing attempt: ${importantDate.title}")
                    return@launch
                }
                
                try {
                    // Mark as being processed
                    dateProcessingSet.add(dateKey)
                    _dateTaskCreationInProgress.add(dateKey)
                    
                    // CRITICAL: Check for duplicate dates first to prevent duplicates
                    val exactTitle = importantDate.title
                    val existingDate = _importantDates.firstOrNull { 
                        it.title.equals(exactTitle, ignoreCase = true) && 
                        isSameDate(it.dueDate, importantDate.dueDate)
                    }
                    
                    if (existingDate != null) {
                        Log.d("TaskViewModel", "Found EXISTING date: ${existingDate.title}, updating instead of adding")
                        
                        // Only update the necessary fields
                        val updatedDate = existingDate.copy(
                            description = importantDate.description,
                            frequency = importantDate.frequency,
                            priority = importantDate.priority,
                            customFrequencyDays = importantDate.customFrequencyDays,
                            subtasks = importantDate.subtasks
                        )
                        currentDate = updatedDate
                        
                        // Update in the local list
                        val index = _importantDates.indexOfFirst { it.id == existingDate.id }
                        if (index != -1) {
                            _importantDates[index] = updatedDate
                            
                            // Update in backend
                            val updateSuccess = taskService.updateImportantDate(updatedDate)
                            if (!updateSuccess) {
                                // Try a second time with force save
                                Log.w("TaskViewModel", "First attempt to update date failed, trying with force save")
                                taskService.forceSaveToDatabase()
                            }
                            
                            // Only create a task if the date has subtasks
                            if (updatedDate.subtasks.isNotEmpty()) {
                                // Use our sync method instead of findOrCreateTaskForDateSync
                                syncDateWithTask(updatedDate)
                            }
                            needsCleanup = true
                            
                            // Verify the date was actually updated in the list
                            val verifiedDate = _importantDates.firstOrNull { it.id == updatedDate.id }
                            if (verifiedDate == null) {
                                Log.e("TaskViewModel", "CRITICAL: Date update verification failed, attempting emergency recovery")
                                _importantDates.add(updatedDate) // Add it back if somehow missing
                                taskService.forceSaveToDatabase() // Force immediate save
                            }
                        }
                    } else {
                        // Double check again by doing a case-insensitive search
                        val similarDate = _importantDates.firstOrNull { 
                            it.title.equals(exactTitle, ignoreCase = true) && 
                            isSameDate(it.dueDate, importantDate.dueDate)
                        }
                        
                        if (similarDate != null) {
                            Log.d("TaskViewModel", "Found SIMILAR date: ${similarDate.title}, updating instead")
                            
                            // Update the existing date
                            val updatedDate = similarDate.copy(
                                title = importantDate.title, // Use the exact casing from the new date
                                description = importantDate.description,
                                frequency = importantDate.frequency,
                                priority = importantDate.priority,
                                customFrequencyDays = importantDate.customFrequencyDays,
                                subtasks = importantDate.subtasks
                            )
                            currentDate = updatedDate
                            
                            // Update in the local list
                            val index = _importantDates.indexOfFirst { it.id == similarDate.id }
                            if (index != -1) {
                                _importantDates[index] = updatedDate
                                val updateSuccess = taskService.updateImportantDate(updatedDate)
                                if (!updateSuccess) {
                                    // Try a second time with force save
                                    Log.w("TaskViewModel", "First attempt to update similar date failed, trying with force save")
                                    taskService.forceSaveToDatabase()
                                }
                                
                                if (updatedDate.subtasks.isNotEmpty()) {
                                    // Use syncDateWithTask instead of findOrCreateTaskForDateSync to prevent duplicates
                                    syncDateWithTask(updatedDate)
                                }
                                needsCleanup = true
                            }
                        } else {
                            // Create a mutable list for subtasks if it doesn't exist
                            val taskWithSubtasks = importantDate.copy(
                                subtasks = importantDate.subtasks.toMutableList()
                            )
                            currentDate = taskWithSubtasks
                            
                            Log.d("TaskViewModel", "Adding NEW important date: ${taskWithSubtasks.title}")
                            val addSuccess = taskService.addImportantDate(taskWithSubtasks)
                            
                            if (addSuccess) {
                                // Update the local list with the task that has subtasks support
                                _importantDates.add(taskWithSubtasks)
                                
                                // Verify the date was actually added to the list
                                val verifiedDate = _importantDates.firstOrNull { it.id == taskWithSubtasks.id }
                                if (verifiedDate == null) {
                                    Log.e("TaskViewModel", "CRITICAL: Date add verification failed, attempting emergency recovery")
                                    _importantDates.add(taskWithSubtasks) // Add it again if somehow missing
                                    taskService.forceSaveToDatabase() // Force immediate save
                                }
                                
                                // Schedule notification for important date
                                notificationService.scheduleImportantDateReminder(taskWithSubtasks)
                                
                                // Create a task for this date if it has subtasks
                                if (taskWithSubtasks.subtasks.isNotEmpty()) {
                                    // Use syncDateWithTask instead of findOrCreateTaskForDateSync
                                    syncDateWithTask(taskWithSubtasks)
                                }
                                needsCleanup = true
                            } else {
                                // First attempt failed, try again with force save
                                Log.w("TaskViewModel", "First attempt to add date failed, trying with forceSaveToDatabase")
                                _importantDates.add(taskWithSubtasks) // Add to in-memory list
                                taskService.forceSaveToDatabase() // Force immediate save
                                
                                // Schedule notification even on retry
                                notificationService.scheduleImportantDateReminder(taskWithSubtasks)
                                
                                if (taskWithSubtasks.subtasks.isNotEmpty()) {
                                    syncDateWithTask(taskWithSubtasks)
                                }
                                needsCleanup = true
                            }
                        }
                    }
                    
                    // CRITICAL: We signal that cleanup is needed, but don't do it in the synchronized block
                    
                } finally {
                    // Always clean up our locks
                    dateProcessingSet.remove(dateKey)
                    _dateTaskCreationInProgress.remove(dateKey)
                }
            }
            
            // Now perform cleanup outside the synchronized block if needed
            if (needsCleanup) {
                // Wait a bit to make sure all operations are complete
                delay(100)
                
                // Run aggressive cleanup after adding a date to eliminate any possible duplicates
                Log.d("TaskViewModel", "Running aggressive cleanup after adding date: ${currentDate?.title}")
                removeAllDuplicateTasks()
                cleanupDuplicateTasksForDates()
                removeDuplicateTasks()
                
                // Final force save to ensure persistence
                taskService.forceSaveToDatabase()
            }
        }
    }
    
    // Create a synchronous version of findOrCreateTaskForDate
    private fun findOrCreateTaskForDateSync(date: Task, subtasks: List<Subtask>): Task {
        val exactTitle = "Reminder for ${date.title}"
        val dateKey = "${exactTitle.lowercase().trim()}:${date.dueDate?.time ?: 0}"
        
        // CRITICAL FIX: Check if this date task was manually deleted by the user
        if (manuallyDeletedDateTasks.contains(dateKey)) {
            Log.d("TaskViewModel", "SYNC: Skipping creation of previously deleted date task: $dateKey")
            // Return a dummy task that won't be added to the list
            return Task(
                id = UUID.randomUUID().toString(),
                title = exactTitle,
                description = date.description,
                dueDate = date.dueDate,
                frequency = date.frequency, 
                priority = date.priority,
                isCompleted = false,
                userId = date.userId,
                createdAt = Date(),
                subtasks = subtasks
            )
        }
        
        Log.d("TaskViewModel", "SYNC: Finding or creating task for date: ${date.title}")
        
        // First AGGRESSIVELY check for existing tasks with EXACT title match
        val existingTasks = _tasks.filter { 
            it.title.equals(exactTitle, ignoreCase = true) && 
            isSameDate(it.dueDate, date.dueDate)
        }
        
        if (existingTasks.isNotEmpty()) {
            Log.d("TaskViewModel", "SYNC: Found ${existingTasks.size} existing tasks for date: ${date.title}")
            
            // If there are multiple, clean them up
            if (existingTasks.size > 1) {
                // Keep only the most recent task
                val mostRecent = existingTasks.maxByOrNull { it.createdAt.time } ?: existingTasks.first()
                
                // Remove all others
                for (duplicate in existingTasks.filter { it.id != mostRecent.id }) {
                    Log.d("TaskViewModel", "SYNC: Removing duplicate: ${duplicate.id}")
                    _tasks.removeAll { it.id == duplicate.id }
                    taskService.deleteTask(duplicate.id)
                }
                
                // Update the remaining task
                val updatedTask = mostRecent.copy(
                    subtasks = subtasks
                )
                
                // Find and update in the list
                val taskIndex = _tasks.indexOfFirst { it.id == mostRecent.id }
                if (taskIndex != -1) {
                    _tasks[taskIndex] = updatedTask
                    taskService.updateTask(updatedTask)
                    Log.d("TaskViewModel", "SYNC: Updated task after cleaning duplicates: ${updatedTask.title}")
                    return updatedTask
                }
                
                return mostRecent
            } else {
                // Just one existing task, update it
                val existingTask = existingTasks.first()
                val updatedTask = existingTask.copy(
                    subtasks = subtasks
                )
                
                // Find and update
                val taskIndex = _tasks.indexOfFirst { it.id == existingTask.id }
                if (taskIndex != -1) {
                    _tasks[taskIndex] = updatedTask
                    taskService.updateTask(updatedTask)
                    Log.d("TaskViewModel", "SYNC: Updated existing task: ${updatedTask.title}")
                    return updatedTask
                }
                
                return existingTask
            }
        }
        
        // If we get here, we need to create a new task - but only if not manually deleted
        Log.d("TaskViewModel", "SYNC: Creating new task for date: ${date.title}")
        
        // But first, one last check for similar tasks
        val similarTasks = _tasks.filter { 
            it.title.contains(date.title, ignoreCase = true) && 
            isSameDate(it.dueDate, date.dueDate)
        }
        
        if (similarTasks.isNotEmpty()) {
            Log.d("TaskViewModel", "SYNC: Found ${similarTasks.size} SIMILAR tasks, updating instead")
            
            // Use the most recent similar task
            val mostRecent = similarTasks.maxByOrNull { it.createdAt.time } ?: similarTasks.first()
            
            // Update it to match our expectation
            val updatedTask = mostRecent.copy(
                title = exactTitle,
                subtasks = subtasks
            )
            
            val taskIndex = _tasks.indexOfFirst { it.id == mostRecent.id }
            if (taskIndex != -1) {
                _tasks[taskIndex] = updatedTask
                taskService.updateTask(updatedTask)
                Log.d("TaskViewModel", "SYNC: Updated similar task: ${updatedTask.title}")
                return updatedTask
            }
            
            return mostRecent
        }
        
        // Create a new task for this date
        val newTask = Task(
            id = UUID.randomUUID().toString(),
            title = exactTitle,
            description = date.description,
            dueDate = date.dueDate,
            frequency = date.frequency,
            priority = date.priority,
            isCompleted = false,
            userId = date.userId,
            createdAt = Date(),
            completedAt = null,
            reminderTime = date.reminderTime,
            subtasks = subtasks
        )
        
        // Add to tasks list
        _tasks.add(newTask)
        taskService.addTask(newTask)
        
        Log.d("TaskViewModel", "SYNC: Created new task: ${newTask.title}")
        return newTask
    }
    
    fun deleteImportantDate(dateId: String) {
        viewModelScope.launch {
            // Remove from local list
            val dateIndex = _importantDates.indexOfFirst { it.id == dateId }
            if (dateIndex != -1) {
                _importantDates.removeAt(dateIndex)
                
                // Delete from backend (placeholder)
                taskService.deleteImportantDate(dateId)
                
                // Cancel any scheduled notifications
                notificationService.cancelImportantDateReminder(dateId)
            }
        }
    }
    
    // Bill related methods
    fun addBill(bill: Bill) {
        viewModelScope.launch {
            // Add bill to service
            billService.addBill(bill)

            // Update local state
            val currentBills = _bills.value.toMutableList()
            currentBills.add(bill)
            _bills.value = currentBills

            Log.d("TaskViewModel", "Bill added successfully: ${bill.title}")

            // Schedule notification for the bill
            notificationService.scheduleBillReminder(bill)

            // Trigger automatic backup when data changes
            triggerAutoBackup()
        }
    }
    
    fun deleteBill(billId: String) {
        viewModelScope.launch {
            // Delete from service
            billService.deleteBill(billId)

            // Update local state
            val currentBills = _bills.value.toMutableList()
            currentBills.removeIf { it.id == billId }
            _bills.value = currentBills
        }
    }
    
    fun markBillAsPaid(billId: String) {
        viewModelScope.launch {
            // Mark as paid in service
            billService.markBillAsPaid(billId)
            
            // Update local state
            val currentBills = _bills.value.toMutableList()
            val index = currentBills.indexOfFirst { it.id == billId }
            if (index != -1) {
                val bill = currentBills[index]
                val updatedBill = bill.copy(
                    isPaid = true,
                    status = BillStatus.PAID,
                    paymentDate = LocalDate.now(),
                    completedAt = Date() // Add completion timestamp
                )
                currentBills[index] = updatedBill
                _bills.value = currentBills
                
                // Cancel any scheduled notifications for this bill
                notificationService.cancelBillReminder(billId)
            }
        }
    }
    
    fun addLinkedEmail(email: String): Boolean {
        android.util.Log.d("TaskViewModel", "Adding linked email: $email")

        // Check if email is already linked
        val currentEmails = _linkedEmails.value
        if (currentEmails.contains(email)) {
            android.util.Log.d("TaskViewModel", "Email $email already linked")
            return false
        }

        // Add the email directly since Google Sign-In was successful
        val added = billService.addVerifiedEmail(email)

        if (added) {
            // Update local state immediately
            val updatedEmails = currentEmails.toMutableList()
            updatedEmails.add(email)
            _linkedEmails.value = updatedEmails

            android.util.Log.d("TaskViewModel", "Successfully added email: $email. Total linked emails: ${updatedEmails.size}")

            // Start periodic scanning if this is the first email linked and scanning is enabled
            if (updatedEmails.size == 1 && isPeriodicEmailScanningEnabled) {
                android.util.Log.d("TaskViewModel", "First email linked, starting periodic scanning")
                startPeriodicEmailScanning()
            }

            // Note: Email scanning will be triggered after 5 minutes delay from EmailManagementScreen
            android.util.Log.d("TaskViewModel", "Email linked successfully. Scanning will be triggered after 5 minutes delay.")

            return true
        } else {
            android.util.Log.e("TaskViewModel", "Failed to add email: $email")
            return false
        }
    }
    
    fun initiateEmailVerification(email: String, activity: android.app.Activity): Boolean {
        android.util.Log.d("TaskViewModel", "TaskViewModel: Starting email verification for: $email")

        // Start the OAuth verification process
        val verified = billService.initiateEmailVerification(email, activity)

        android.util.Log.d("TaskViewModel", "TaskViewModel: BillService verification result for $email: $verified")

        if (verified) {
            // Update local state immediately
            val currentEmails = _linkedEmails.value.toMutableList()
            if (!currentEmails.contains(email)) {
                currentEmails.add(email)
                _linkedEmails.value = currentEmails
                android.util.Log.d("TaskViewModel", "TaskViewModel: Added $email to linked emails. Total: ${currentEmails.size}")

                // Start periodic scanning if this is the first email linked and scanning is enabled
                if (currentEmails.size == 1 && isPeriodicEmailScanningEnabled) {
                    android.util.Log.d("TaskViewModel", "First email linked, starting periodic scanning")
                    startPeriodicEmailScanning()
                }
            } else {
                android.util.Log.d("TaskViewModel", "TaskViewModel: Email $email already in linked emails")
            }
        }

        return verified
    }
    
    fun addVerifiedEmail(email: String): Boolean {
        // Add verified email after OAuth authentication (in background to avoid blocking)
        viewModelScope.launch {
            try {
                val added = billService.addVerifiedEmail(email)

                if (added) {
                    // Update local state on main thread
                    withContext(Dispatchers.Main) {
                        val currentEmails = _linkedEmails.value.toMutableList()
                        if (!currentEmails.contains(email)) {
                            currentEmails.add(email)
                            _linkedEmails.value = currentEmails
                        }
                    }

                    // Note: Email scanning will be triggered after 5 minutes delay from EmailManagementScreen
                    android.util.Log.d("TaskViewModel", "Email verified and linked: $email. Scanning will be triggered after 5 minutes delay.")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "Error adding verified email: ${e.message}")
            }
        }

        return true // Return immediately to avoid blocking UI
    }
    
    fun removeLinkedEmail(email: String) {
        // Remove from service (in background to avoid blocking)
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskViewModel", "Starting removal of linked email: $email")

                // Call the async version of removeLinkedEmail
                val removed = billService.removeLinkedEmail(email)
                android.util.Log.d("TaskViewModel", "BillService.removeLinkedEmail result: $removed")

                // Update local state on main thread
                withContext(Dispatchers.Main) {
                    val currentEmails = _linkedEmails.value.toMutableList()
                    currentEmails.remove(email)
                    _linkedEmails.value = currentEmails
                    android.util.Log.d("TaskViewModel", "Updated local state. Remaining emails: ${currentEmails.size}")

                    // Stop periodic scanning if no emails are left
                    if (currentEmails.isEmpty()) {
                        android.util.Log.d("TaskViewModel", "No linked emails remaining, stopping periodic scanning")
                        stopPeriodicEmailScanning()
                    }
                }

                android.util.Log.d("TaskViewModel", "Successfully removed linked email: $email")
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "Error removing linked email: ${e.message}", e)
            }
        }
    }
    
    fun scanEmailsForBills() {
        viewModelScope.launch {
            isEmailScanningInProgress = true

            try {
                android.util.Log.d("TaskViewModel", "Starting email scan for bills")

                // Try to get real Gmail service from current auth state
                val gmailService = getCurrentGmailService()
                android.util.Log.d("TaskViewModel", "Gmail service available for scanning: ${gmailService != null}")

                val detectedBills = if (gmailService != null) {
                    android.util.Log.d("TaskViewModel", "Using real Gmail API for scanning")
                    android.util.Log.d("TaskViewModel", "Gmail service class: ${gmailService.javaClass.simpleName}")
                    // Use real Gmail API
                    val bills = gmailService.scanEmailsForBills()
                    android.util.Log.d("TaskViewModel", "Real Gmail API returned ${bills.size} bills")
                    bills
                } else {
                    android.util.Log.d("TaskViewModel", "No Gmail service available, using BillService async scanning")
                    // Use async scanning to avoid blocking
                    val bills = billService.scanEmailsAsync()
                    android.util.Log.d("TaskViewModel", "BillService async scan returned ${bills.size} bills")
                    bills
                }

                // Add detected bills to the list
                android.util.Log.d("TaskViewModel", "Processing ${detectedBills.size} detected bills")

                if (detectedBills.isNotEmpty()) {
                    val currentBills = _bills.value.toMutableList()
                    android.util.Log.d("TaskViewModel", "Current bills count before adding: ${currentBills.size}")

                    // Filter out auto-detected bills that might be duplicates
                    val existingBillTitles = currentBills.map { it.title }
                    android.util.Log.d("TaskViewModel", "Existing bill titles: $existingBillTitles")

                    val newBills: List<com.taskiq.app.model.Bill> = detectedBills.filter { !existingBillTitles.contains(it.title) }
                    android.util.Log.d("TaskViewModel", "New bills after duplicate filtering: ${newBills.size}")

                    newBills.forEach { bill ->
                        android.util.Log.d("TaskViewModel", "New bill: ${bill.title} - $${bill.amount} due ${bill.dueDate}")
                    }

                    if (newBills.isNotEmpty()) {
                        currentBills.addAll(newBills)
                        _bills.value = currentBills

                        // Also add to BillService for persistence
                        newBills.forEach { bill ->
                            billService.addBill(bill)
                        }

                        android.util.Log.d("TaskViewModel", "Successfully added ${newBills.size} new bills from email scan")
                        android.util.Log.d("TaskViewModel", "Total bills after adding: ${_bills.value.size}")
                    } else {
                        android.util.Log.d("TaskViewModel", "No new bills found (all were duplicates)")
                    }
                } else {
                    android.util.Log.w("TaskViewModel", "No bills detected from email scan")

                    // Debug: Check Gmail service availability
                    val localGmailService = gmailServiceInstance
                    val globalGmailService = com.taskiq.app.viewmodel.GmailAuthViewModel.getGlobalGmailService()

                    android.util.Log.d("TaskViewModel", "Debug - Local Gmail service: ${localGmailService != null}")
                    android.util.Log.d("TaskViewModel", "Debug - Global Gmail service: ${globalGmailService != null}")
                    android.util.Log.d("TaskViewModel", "Debug - Linked emails count: ${_linkedEmails.value.size}")
                    android.util.Log.d("TaskViewModel", "Debug - Linked emails: ${_linkedEmails.value}")

                    if (localGmailService == null && globalGmailService == null) {
                        android.util.Log.e("TaskViewModel", "ISSUE: No Gmail service available - OAuth may have failed")
                    } else if (_linkedEmails.value.isEmpty()) {
                        android.util.Log.e("TaskViewModel", "ISSUE: No linked emails found")
                    } else {
                        android.util.Log.w("TaskViewModel", "Gmail service available but no bills found - this could be normal if no bill emails exist")
                    }
                }
            } catch (e: Exception) {
                // Handle any errors
                android.util.Log.e("TaskViewModel", "Error scanning emails: ${e.message}", e)
            } finally {
                isEmailScanningInProgress = false
                android.util.Log.d("TaskViewModel", "Email scanning completed, isEmailScanningInProgress = false")
            }
        }
    }

    // Store Gmail service reference
    private var gmailServiceInstance: com.taskiq.app.service.GmailService? = null

    /**
     * Get current Gmail service if available
     */
    private fun getCurrentGmailService(): com.taskiq.app.service.GmailService? {
        return try {
            // First try the local instance
            var service = gmailServiceInstance

            // If local instance is null, try to get the global service
            if (service == null) {
                service = com.taskiq.app.viewmodel.GmailAuthViewModel.getGlobalGmailService()
                android.util.Log.d("TaskViewModel", "Retrieved global Gmail service: ${service != null}")
            }

            android.util.Log.d("TaskViewModel", "Getting Gmail service, available: ${service != null}")
            service
        } catch (e: Exception) {
            android.util.Log.e("TaskViewModel", "Error getting Gmail service: ${e.message}")
            null
        }
    }

    /**
     * Set Gmail service for real email scanning
     */
    fun setGmailService(gmailService: com.taskiq.app.service.GmailService?) {
        // Store Gmail service reference for real scanning
        gmailServiceInstance = gmailService
        billService.setGmailService(gmailService)
        android.util.Log.d("TaskViewModel", "Gmail service set for real email scanning: ${gmailService != null}")
    }
    
    fun syncWithGoogleCalendar() {
        viewModelScope.launch {
            isCalendarSyncing = true
            Log.d("TaskViewModel", "Starting Google Calendar sync...")

            try {
                val calendarService = GoogleCalendarService(context)

                // Check if user is signed in to Google
                if (!calendarService.isGoogleAccountSignedIn()) {
                    Log.e("TaskViewModel", "No Google account signed in")
                    return@launch
                }

                Log.d("TaskViewModel", "Google account is signed in, proceeding with sync")

                // Get only important personal dates from the user's calendar
                val importedPersonalDates = taskService.getImportedDatesFromCalendar()
                Log.d("TaskViewModel", "Imported ${importedPersonalDates.size} dates from calendar")

                // First, remove any previously synced calendar events to prevent duplicates
                val existingCalendarEvents = _importantDates.filter {
                    it.description?.contains("from your calendar") == true
                }
                Log.d("TaskViewModel", "Found ${existingCalendarEvents.size} existing calendar events to remove")

                // Remove existing calendar events
                existingCalendarEvents.forEach { event ->
                    _importantDates.removeAll { it.id == event.id }
                    taskService.deleteImportantDate(event.id)
                }

                // Only add the newly imported personal important dates if any were found
                if (importedPersonalDates.isNotEmpty()) {
                    Log.d("TaskViewModel", "Adding ${importedPersonalDates.size} new dates to important dates list")

                    // Add each date individually to ensure proper handling
                    importedPersonalDates.forEach { date ->
                        // Check if a similar date already exists (any source)
                        val existingSimilarDate = _importantDates.find { existing ->
                            existing.title.equals(date.title, ignoreCase = true) &&
                            isSameDate(existing.dueDate, date.dueDate)
                        }

                        if (existingSimilarDate == null) {
                            _importantDates.add(date)
                            taskService.addImportantDate(date)
                            Log.d("TaskViewModel", "Added new calendar date: ${date.title} (${date.dueDate})")
                        } else {
                            Log.d("TaskViewModel", "Skipped duplicate calendar date: ${date.title} (already exists)")
                        }
                    }

                    // Save changes to storage
                    Log.d("TaskViewModel", "Saving changes to storage")
                    taskService.forceSaveToDatabase()

                } else {
                    Log.d("TaskViewModel", "No new dates to import from calendar")
                }

                // Update sync timestamp
                _lastSyncTime.value = Date()
                Log.d("TaskViewModel", "Calendar sync completed successfully at ${_lastSyncTime.value}")

                // Schedule next sync (only if not using periodic sync)
                if (periodicSyncJob == null) {
                    scheduleNextSync()
                }

            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error syncing with Google Calendar: ${e.message}")
                e.printStackTrace()
            } finally {
                isCalendarSyncing = false
            }
        }
    }
    
    private fun scheduleNextSync() {
        if (isGoogleCalendarSyncEnabled) {
            // Schedule next sync in 30 minutes for more frequent updates
            viewModelScope.launch {
                delay(30 * 60 * 1000L) // 30 minutes in milliseconds
                syncWithGoogleCalendar()
            }
        }
    }

    // Start periodic sync every 1 hour when enabled
    private fun startPeriodicSync() {
        stopPeriodicSync() // Stop any existing sync job

        if (isGoogleCalendarSyncEnabled) {
            periodicSyncJob = viewModelScope.launch {
                while (isGoogleCalendarSyncEnabled) {
                    try {
                        delay(60 * 60 * 1000L) // 1 hour in milliseconds
                        if (isGoogleCalendarSyncEnabled) {
                            Log.d("TaskViewModel", "Running periodic Google Calendar sync")
                            syncWithGoogleCalendar()
                        }
                    } catch (e: Exception) {
                        Log.e("TaskViewModel", "Error in periodic sync: ${e.message}")
                        // Continue the loop even if one sync fails
                    }
                }
            }
            Log.d("TaskViewModel", "Started periodic Google Calendar sync (every 1 hour)")
        }
    }

    // Stop periodic sync
    private fun stopPeriodicSync() {
        periodicSyncJob?.cancel()
        periodicSyncJob = null
        Log.d("TaskViewModel", "Stopped periodic Google Calendar sync")
    }

    // Start periodic email scanning every 30 minutes when enabled
    private fun startPeriodicEmailScanning() {
        stopPeriodicEmailScanning() // Stop any existing scan job

        if (isPeriodicEmailScanningEnabled) {
            periodicEmailScanJob = viewModelScope.launch {
                while (isPeriodicEmailScanningEnabled) {
                    try {
                        delay(30 * 60 * 1000L) // 30 minutes in milliseconds
                        if (isPeriodicEmailScanningEnabled) {
                            // Only scan if we have linked emails
                            val currentLinkedEmails = _linkedEmails.value
                            if (currentLinkedEmails.isNotEmpty()) {
                                android.util.Log.d("TaskViewModel", "Running periodic email scan for ${currentLinkedEmails.size} linked emails")
                                scanEmailsForBillsAutomatically()
                            } else {
                                android.util.Log.d("TaskViewModel", "No linked emails found, skipping periodic scan")
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("TaskViewModel", "Error in periodic email scan: ${e.message}")
                        // Continue the loop even if one scan fails
                    }
                }
            }
            android.util.Log.d("TaskViewModel", "Started periodic email scanning (every 30 minutes)")
        }
    }

    // Stop periodic email scanning
    private fun stopPeriodicEmailScanning() {
        periodicEmailScanJob?.cancel()
        periodicEmailScanJob = null
        android.util.Log.d("TaskViewModel", "Stopped periodic email scanning")
    }

    // Automatic email scanning (without user interaction)
    private fun scanEmailsForBillsAutomatically() {
        viewModelScope.launch {
            try {
                android.util.Log.d("TaskViewModel", "Starting automatic email scan for bills")

                // Scan emails for bills using async method (no delay for automatic scanning)
                val detectedBills = billService.scanEmailsAsync()

                // Add detected bills to the list
                if (detectedBills.isNotEmpty()) {
                    val currentBills = _bills.value.toMutableList()

                    // Filter out auto-detected bills that might be duplicates
                    val existingBillTitles = currentBills.map { it.title }
                    val newBills = detectedBills.filter { !existingBillTitles.contains(it.title) }

                    if (newBills.isNotEmpty()) {
                        currentBills.addAll(newBills)
                        _bills.value = currentBills
                        android.util.Log.d("TaskViewModel", "Automatic scan found ${newBills.size} new bills")

                        // Schedule notifications for new bills
                        newBills.forEach { bill ->
                            notificationService.scheduleBillReminder(bill)
                        }
                    } else {
                        android.util.Log.d("TaskViewModel", "Automatic scan found ${detectedBills.size} bills but all were duplicates")
                    }
                } else {
                    android.util.Log.d("TaskViewModel", "Automatic scan found no new bills")
                }
            } catch (e: Exception) {
                android.util.Log.e("TaskViewModel", "Error in automatic email scanning: ${e.message}")
            }
        }
    }

    // Clean up when ViewModel is destroyed
    override fun onCleared() {
        super.onCleared()
        stopPeriodicSync()
        stopPeriodicEmailScanning()
    }
    
    fun updateTask(task: Task) {
        viewModelScope.launch {
            val taskIndex = _tasks.indexOfFirst { it.id == task.id }
            if (taskIndex != -1) {
                // Update local list
                _tasks[taskIndex] = task
                
                // Update in backend (placeholder)
                taskService.updateTask(task)
                
                // Update notifications if needed
                if (task.frequency != TaskFrequency.ONE_TIME) {
                    taskWorkerManager.cancelTaskReminder(task.id)
                    taskWorkerManager.scheduleTaskReminder(task)
                }

                // Trigger automatic backup when data changes
                triggerAutoBackup()
            } else {
                // Not found, add it as new
                addTask(task)
            }
        }
    }
    
    fun updateImportantDate(importantDate: Task) {
        viewModelScope.launch {
            // Update in backend with error handling
            val updateSuccess = taskService.updateImportantDate(importantDate)
            if (!updateSuccess) {
                Log.w("TaskViewModel", "First attempt to update date failed, trying with force save")
                // Try a second time with force save
                taskService.forceSaveToDatabase()
            }
            
            // Update local list
            val dateIndex = _importantDates.indexOfFirst { it.id == importantDate.id }
            if (dateIndex != -1) {
                _importantDates[dateIndex] = importantDate
                
                // Verify the date was actually updated in the list
                val verifiedDate = _importantDates.firstOrNull { it.id == importantDate.id }
                if (verifiedDate == null || verifiedDate != importantDate) {
                    Log.e("TaskViewModel", "CRITICAL: Date update verification failed, attempting emergency recovery")
                    // Remove and re-add to make sure it's properly added
                    _importantDates.removeIf { it.id == importantDate.id }
                    _importantDates.add(importantDate)
                    taskService.forceSaveToDatabase() // Force immediate save
                }
                
                // Update notification
                notificationService.cancelImportantDateReminder(importantDate.id)
                notificationService.scheduleImportantDateReminder(importantDate)
                
                // If the date has subtasks, ensure task synchronization
                if (importantDate.subtasks.isNotEmpty()) {
                    // Update corresponding reminder task if exists
                    syncDateWithTask(importantDate)
                }
                
                // Final force save to ensure persistence
                taskService.forceSaveToDatabase()
            } else {
                // If not found, add it as new (recovery mechanism)
                Log.w("TaskViewModel", "Date not found for update, adding as new instead: ${importantDate.title}")
                addImportantDate(importantDate)
            }
            
            // Wait a bit and run cleanup to prevent duplicates
            delay(100)
            cleanupDuplicateTasksForDates()
        }
    }
    
    fun updateBill(bill: Bill) {
        viewModelScope.launch {
            // Update in service
            billService.updateBill(bill)

            // Update local state
            val currentBills = _bills.value.toMutableList()
            val index = currentBills.indexOfFirst { it.id == bill.id }
            if (index != -1) {
                currentBills[index] = bill
                _bills.value = currentBills

                // Update notification
                notificationService.cancelBillReminder(bill.id)
                notificationService.scheduleBillReminder(bill)

                // Trigger automatic backup when data changes
                triggerAutoBackup()
            }
        }
    }
    
    // Get all recurring tasks by frequency
    fun getTasksByFrequency(frequency: TaskFrequency): List<Task> {
        return _tasks.filter { task -> 
            task.frequency == frequency && !task.isCompleted
        }
    }
    
    // Get monthly recurring tasks
    fun getMonthlyTasks(): List<Task> {
        return getTasksByFrequency(TaskFrequency.MONTHLY)
    }
    
    // Get yearly recurring tasks
    fun getYearlyTasks(): List<Task> {
        return getTasksByFrequency(TaskFrequency.YEARLY)
    }
    
    // Check if it's time to reset a recurring task
    fun shouldResetRecurringTask(task: Task): Boolean {
        val now = LocalDate.now()
        
        return when (task.frequency) {
            TaskFrequency.MONTHLY -> {
                // Reset on the first day of each month
                now.dayOfMonth == 1
            }
            TaskFrequency.YEARLY -> {
                // Reset on the first day of the year
                now.dayOfMonth == 1 && now.monthValue == 1
            }
            TaskFrequency.WEEKLY -> {
                // Reset on Monday
                now.dayOfWeek.value == 1
            }
            TaskFrequency.DAILY -> {
                // Reset every day
                true
            }
            TaskFrequency.HOURLY -> {
                // Reset every hour
                true
            }
            TaskFrequency.CUSTOM -> {
                // For custom frequency, check if the number of days has passed
                task.customFrequencyDays?.let { days ->
                    val lastReset = task.createdAt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    val daysSinceLastReset = ChronoUnit.DAYS.between(lastReset, now)
                    daysSinceLastReset >= days
                } ?: false
            }
            else -> false
        }
    }
    
    // Reset recurring tasks
    fun resetRecurringTasks() {
        viewModelScope.launch {
            val recurringTasks = _tasks.filter { 
                it.frequency != TaskFrequency.ONE_TIME && 
                it.isCompleted && 
                shouldResetRecurringTask(it)
            }
            
            recurringTasks.forEach { task ->
                val updatedTask = task.copy(isCompleted = false)
                updateTask(updatedTask)
            }
        }
    }
    
    private fun getScheduledTasks(tasks: List<Task>): List<Task> {
        val calendar = Calendar.getInstance()
        val today = calendar.time
        val scheduledTasks = mutableListOf<Task>()
        
        tasks.forEach { task ->
            if (task.dueDate != null && !task.isCompleted) {
                when (task.frequency) {
                    TaskFrequency.ONE_TIME -> {
                        if (task.dueDate.after(today)) {
                            scheduledTasks.add(task)
                        }
                    }
                    TaskFrequency.DAILY -> {
                        val nextOccurrence = getNextOccurrence(task.dueDate, today, 1)
                        if (nextOccurrence != null) {
                            scheduledTasks.add(task.copy(dueDate = nextOccurrence))
                        }
                    }
                    TaskFrequency.WEEKLY -> {
                        val nextOccurrence = getNextOccurrence(task.dueDate, today, 7)
                        if (nextOccurrence != null) {
                            scheduledTasks.add(task.copy(dueDate = nextOccurrence))
                        }
                    }
                    TaskFrequency.MONTHLY -> {
                        val nextOccurrence = getNextOccurrence(task.dueDate, today, 30)
                        if (nextOccurrence != null) {
                            scheduledTasks.add(task.copy(dueDate = nextOccurrence))
                        }
                    }
                    TaskFrequency.YEARLY -> {
                        val nextOccurrence = getNextOccurrence(task.dueDate, today, 365)
                        if (nextOccurrence != null) {
                            scheduledTasks.add(task.copy(dueDate = nextOccurrence))
                        }
                    }
                    TaskFrequency.CUSTOM -> {
                        val days = task.customFrequencyDays ?: 0
                        val hours = task.customFrequencyHours ?: 0
                        
                        if (days > 0 || hours > 0) {
                            // If we have hours only or both days and hours
                            val nextOccurrence = if (hours > 0) {
                                getNextOccurrenceWithHours(task.dueDate, today, days, hours)
                            } else {
                                getNextOccurrence(task.dueDate, today, days)
                            }
                            
                            if (nextOccurrence != null) {
                                scheduledTasks.add(task.copy(dueDate = nextOccurrence))
                            }
                        }
                    }
                    else -> {
                        if (task.dueDate.after(today)) {
                            scheduledTasks.add(task)
                        }
                    }
                }
            }
        }
        
        return scheduledTasks.sortedBy { it.dueDate }
    }
    
    private fun getNextOccurrence(originalDate: Date?, today: Date, daysToAdd: Int): Date? {
        if (originalDate == null) return null
        
        val calendar = Calendar.getInstance()
        calendar.time = originalDate
        
        // If the original date is in the past, calculate the next occurrence
        if (originalDate.before(today)) {
            while (calendar.time.before(today)) {
                calendar.add(Calendar.DAY_OF_MONTH, daysToAdd)
            }
        }
        
        return calendar.time
    }
    
    private fun getNextOccurrenceWithHours(originalDate: Date?, today: Date, daysToAdd: Int, hoursToAdd: Int): Date? {
        if (originalDate == null) return null
        
        val calendar = Calendar.getInstance()
        calendar.time = originalDate
        
        // If the original date is in the past, calculate the next occurrence
        if (originalDate.before(today)) {
            // Keep adding the interval until we get a future date
            while (calendar.time.before(today)) {
                if (daysToAdd > 0) {
                    calendar.add(Calendar.DAY_OF_MONTH, daysToAdd)
                }
                if (hoursToAdd > 0) {
                    calendar.add(Calendar.HOUR_OF_DAY, hoursToAdd)
                }
                
                // If both days and hours are zero, break to avoid infinite loop
                if (daysToAdd == 0 && hoursToAdd == 0) break
            }
        }
        
        return calendar.time
    }
    
    // Add a new synchronized method to find or create tasks for dates
    private fun findOrCreateTaskForDate(date: Task, subtasks: List<Subtask>): Task {
        Log.d("TaskViewModel", "Using synchronized version for findOrCreateTaskForDate")
        return findOrCreateTaskForDateSync(date, subtasks)
    }

    fun addSubtask(taskId: String, subtask: Subtask) {
        viewModelScope.launch {
            // Check for existing subtask with same content but different ID to prevent duplicates
            val existingTask = _tasks.firstOrNull { task -> task.id == taskId }
            if (existingTask != null) {
                // Check if we already have a similar subtask
                val similarSubtask = existingTask.subtasks.firstOrNull { 
                    it.title == subtask.title && it.id != subtask.id
                }
                
                if (similarSubtask != null) {
                    Log.d("TaskViewModel", "Found similar subtask in task ${existingTask.title}, updating instead of adding")
                    // Update the existing subtask instead of adding a new one
                    val updatedSubtasks = existingTask.subtasks.map { 
                        if (it.id == similarSubtask.id) subtask.copy(id = similarSubtask.id) else it 
                    }
                    
                    val updatedTask = existingTask.copy(subtasks = updatedSubtasks)
                    
                    // Update in the list
                    val taskIndex = _tasks.indexOfFirst { it.id == existingTask.id }
                    if (taskIndex != -1) {
                        _tasks[taskIndex] = updatedTask
                        taskService.updateTask(updatedTask)
                        
                        // Force immediate save to ensure persistence
                        taskService.forceSaveToDatabase()
                        
                        Log.d("TaskViewModel", "Updated similar subtask in task: ${updatedTask.title}")
                    }
                    
                    return@launch
                }
            }
            
            // First check in regular tasks
            val taskIndex = _tasks.indexOfFirst { it.id == taskId }
            if (taskIndex != -1) {
                val task = _tasks[taskIndex]
                val subtasks = task.subtasks.toMutableList()
                
                // Check if a subtask with this ID already exists
                val existingSubtaskIndex = subtasks.indexOfFirst { it.id == subtask.id }
                
                if (existingSubtaskIndex != -1) {
                    // Update existing subtask
                    subtasks[existingSubtaskIndex] = subtask
                } else {
                    // Add new subtask
                    subtasks.add(subtask)
                }
                    
                // Update the task with new subtasks list
                val updatedTask = task.copy(subtasks = subtasks)
                _tasks[taskIndex] = updatedTask
                
                // Update in backend
                val updateSuccess = taskService.updateTask(updatedTask)
                
                // Force immediate save to ensure persistence
                taskService.forceSaveToDatabase()
                
                Log.d("TaskViewModel", "Added/updated subtask '${subtask.title}' to task: ${updatedTask.title}")
            } else {
                // If not found in tasks, check in important dates
                val dateIndex = _importantDates.indexOfFirst { it.id == taskId }
                if (dateIndex != -1) {
                    val date = _importantDates[dateIndex]
                    val subtasks = date.subtasks.toMutableList()
                    
                    // Check if a subtask with this ID already exists
                    val existingSubtaskIndex = subtasks.indexOfFirst { it.id == subtask.id }
                    
                    if (existingSubtaskIndex != -1) {
                        // Update existing subtask
                        subtasks[existingSubtaskIndex] = subtask
                    } else {
                        // Add new subtask
                        subtasks.add(subtask)
                    }
                    
                    // Update the date with new subtasks list
                    val updatedDate = date.copy(subtasks = subtasks)
                    _importantDates[dateIndex] = updatedDate
                    
                    // Update in backend
                    taskService.updateImportantDate(updatedDate)
                    
                    // Force immediate save to ensure persistence
                    taskService.forceSaveToDatabase()
                    
                    Log.d("TaskViewModel", "Added/updated subtask '${subtask.title}' to date: ${updatedDate.title}")
                    
                    // Synchronize with the corresponding task (will create one if it doesn't exist)
                    syncDateWithTask(updatedDate)
                }
            }
        }
    }
    
    fun toggleSubtaskCompletion(taskId: String, subtaskId: String) {
        viewModelScope.launch {
            // Check in regular tasks first
            val taskIndex = _tasks.indexOfFirst { it.id == taskId }
            if (taskIndex != -1) {
                val task = _tasks[taskIndex]
                val subtasks = task.subtasks.toMutableList()
                val subtaskIndex = subtasks.indexOfFirst { it.id == subtaskId }
                
                if (subtaskIndex != -1) {
                    // Toggle the subtask completion
                    val subtask = subtasks[subtaskIndex]
                    val isCompleting = !subtask.isCompleted
                    subtasks[subtaskIndex] = subtask.copy(
                        isCompleted = isCompleting,
                        completedAt = if (isCompleting) Date() else null
                    )
                
                    // Update the task with new subtasks list
                    val updatedTask = task.copy(subtasks = subtasks)
                    _tasks[taskIndex] = updatedTask
                    
                    // Update in backend
                    val updateSuccess = taskService.updateTask(updatedTask)
                    
                    // Force immediate save to ensure persistence for subtask changes
                    taskService.forceSaveToDatabase()
                    
                    Log.d("TaskViewModel", "Subtask completion toggled: ${subtask.title}, new state: $isCompleting, task: ${updatedTask.title}")
                    
                    // If this is a date reminder task, also update the corresponding date
                    if (task.title.startsWith("Reminder for ", ignoreCase = true)) {
                        val dateTitlePart = task.title.substringAfter("Reminder for ", "").trim()
                        val matchingDateIndex = _importantDates.indexOfFirst { date ->
                            date.title.equals(dateTitlePart, ignoreCase = true) && 
                            isSameDate(date.dueDate, task.dueDate)
                        }
                        
                        if (matchingDateIndex != -1) {
                            val date = _importantDates[matchingDateIndex]
                            val dateSubtasks = date.subtasks.toMutableList()
                            
                            // Find matching subtask in the date
                            val dateSubtaskIndex = dateSubtasks.indexOfFirst { it.id == subtaskId }
                            if (dateSubtaskIndex != -1) {
                                // Update the subtask in the date
                                dateSubtasks[dateSubtaskIndex] = subtask.copy(
                                    isCompleted = isCompleting,
                                    completedAt = if (isCompleting) Date() else null
                                )
                                
                                // Update the date with modified subtasks
                                val updatedDate = date.copy(subtasks = dateSubtasks)
                                _importantDates[matchingDateIndex] = updatedDate
                                
                                // Update in backend
                                taskService.updateImportantDate(updatedDate)
                                // Force another save to ensure persistence of date changes
                                taskService.forceSaveToDatabase()
                                
                                Log.d("TaskViewModel", "Updated corresponding date: ${updatedDate.title}")
                            }
                        }
                    }
                }
            } else {
                // If not found in tasks, check in important dates
                val dateIndex = _importantDates.indexOfFirst { it.id == taskId }
                if (dateIndex != -1) {
                    val date = _importantDates[dateIndex]
                    
                    // Create a unique key for this date
                    val dateKey = "${date.title.lowercase()}:${date.dueDate?.time ?: 0}"
                    
                    // Skip if we're already processing this date
                    if (_dateTaskCreationInProgress.contains(dateKey)) {
                        return@launch
                    }
                    
                    try {
                        // Mark as in progress
                        _dateTaskCreationInProgress.add(dateKey)
                        
                        val subtasks = date.subtasks.toMutableList()
                        val subtaskIndex = subtasks.indexOfFirst { it.id == subtaskId }
                        
                        if (subtaskIndex != -1) {
                            // Toggle the subtask completion
                            val subtask = subtasks[subtaskIndex]
                            val isCompleting = !subtask.isCompleted
                            subtasks[subtaskIndex] = subtask.copy(
                                isCompleted = isCompleting,
                                completedAt = if (isCompleting) Date() else null
                            )
                    
                            // Update the date with new subtasks list
                            val updatedDate = date.copy(subtasks = subtasks)
                            _importantDates[dateIndex] = updatedDate
                            
                            // Update in backend
                            val updateSuccess = taskService.updateImportantDate(updatedDate)
                            
                            // Force immediate save to ensure persistence for date subtask changes
                            taskService.forceSaveToDatabase()
                            
                            Log.d("TaskViewModel", "Date subtask completion toggled: ${subtask.title}, new state: $isCompleting, date: ${updatedDate.title}")
                            
                            // Use our new sync method to create/update the task
                            syncDateWithTask(updatedDate)
                            
                            // Clean up any duplicate tasks that might have been created
                            cleanupDuplicateTasksForDates()
                        }
                    } finally {
                        // Always remove from in-progress set when done
                        _dateTaskCreationInProgress.remove(dateKey)
                    }
                }
            }
        }
    }
    
    fun getTemplates(): List<Task> {
        return _tasks.filter { it.isTemplate }
    }
    
    fun createTaskFromTemplate(templateId: String): Task? {
        val template = _tasks.find { it.id == templateId && it.isTemplate } ?: return null
        
        return Task(
            id = UUID.randomUUID().toString(),
            title = template.title,
            description = template.description,
            dueDate = null, // User will set this
            frequency = template.frequency,
            priority = template.priority,
            customFrequencyDays = template.customFrequencyDays,
            customFrequencyHours = template.customFrequencyHours,
            isCompleted = false,
            userId = template.userId,
            createdAt = Date(),
            completedAt = null,
            subtasks = template.subtasks.map { it.copy(id = UUID.randomUUID().toString(), isCompleted = false, completedAt = null) },
            isTemplate = false,
            templateId = template.id
        )
    }
    
    fun deleteTemplate(templateId: String) {
        viewModelScope.launch {
            val template = _tasks.find { it.id == templateId && it.isTemplate }
            if (template != null) {
                _tasks.remove(template)
                taskService.deleteTask(templateId)
            }
        }
    }
    

    
    private fun startPeriodicRecurringTasksCheck() {
        viewModelScope.launch {
            while (true) {
                // Check for recurring tasks every 15 minutes instead of every minute
                delay(900000) // 15 minutes in milliseconds
                processRecurringTasks()
            }
        }
    }
    
    // More aggressive removal of all duplicates
    private fun removeAllDuplicateTasks() {
        Log.d("TaskViewModel", "Starting ULTRA-aggressive duplicate task cleanup...")
        
        // CRITICAL FIX: Specifically target date reminder tasks first
        val dateReminderTasks = _tasks.filter { it.title.startsWith("Reminder for ", ignoreCase = true) }
        
        // Group by normalized key (title + date)
        val dateTaskGroups = dateReminderTasks.groupBy { task ->
            // Normalize the title and date to create a unique key
            "${task.title.lowercase().trim()}:${task.dueDate?.time ?: 0}"
        }
        
        val tasksToRemove = mutableListOf<Task>()
        
        // For each group with more than one task, keep only the best one
        dateTaskGroups.forEach { (key, tasks) ->
            if (tasks.size > 1) {
                Log.d("TaskViewModel", "CLEANUP: Found ${tasks.size} date reminder tasks for key: $key")
                
                // Find task with most subtasks/most recent - this is the one we'll keep
                val bestTask = tasks.maxByOrNull { t -> 
                    t.subtasks.size * 100 + (t.createdAt.time / 1000)
                } ?: tasks.first()
                
                // Mark all others for removal
                tasksToRemove.addAll(tasks.filter { it.id != bestTask.id })
                Log.d("TaskViewModel", "CLEANUP: Keeping task ID ${bestTask.id}, removing ${tasks.size - 1} duplicates")
            }
        }
        
        // Now do a general duplicate check for all tasks
        // Map to track unique tasks by their "identity"
        val uniqueTasks = mutableMapOf<String, Task>()
        
        // First pass: identify all duplicates based on title+date
        for (task in _tasks) {
            if (tasksToRemove.any { it.id == task.id }) {
                // Skip tasks already marked for removal
                continue
            }
            
            val taskKey = "${task.title.lowercase().trim()}:${task.dueDate?.time ?: 0}"
            
            if (uniqueTasks.containsKey(taskKey)) {
                // We've seen this task before
                val existingTask = uniqueTasks[taskKey]!!
                
                // Keep the newer/more complete version
                if (task.createdAt.time > existingTask.createdAt.time || 
                    task.subtasks.size > existingTask.subtasks.size) {
                    // This task is newer or has more subtasks, replace existing
                    tasksToRemove.add(existingTask)
                    uniqueTasks[taskKey] = task
                    Log.d("TaskViewModel", "Found duplicate task, keeping newer: ${task.title}")
                } else {
                    // Existing task is newer, mark this one for removal
                    tasksToRemove.add(task)
                    Log.d("TaskViewModel", "Found duplicate task, removing older: ${task.title}")
                }
            } else {
                // First time seeing this task
                uniqueTasks[taskKey] = task
            }
        }
        
        // Remove all identified duplicates
        if (tasksToRemove.isNotEmpty()) {
            Log.d("TaskViewModel", "Removing ${tasksToRemove.size} duplicate tasks")
            
            for (task in tasksToRemove) {
                Log.d("TaskViewModel", "Removing duplicate: ${task.title} (ID: ${task.id})")
                _tasks.removeAll { it.id == task.id }
                taskService.deleteTask(task.id)
            }
        }
        
        Log.d("TaskViewModel", "Completed ULTRA-aggressive duplicate task cleanup")
    }

    // Enhanced cleanup method to remove duplicate date-related tasks
    private fun cleanupDuplicateTasksForDates() {
        Log.d("TaskViewModel", "Starting cleanup of duplicate date tasks...")
        
        // First, ensure we have a clean representation of all date-task associations
        val dateTaskMap = mutableMapOf<String, Task>()
        
        // Process all tasks that appear to be date reminders
        val dateTasks = _tasks.filter { it.title.startsWith("Reminder for ", ignoreCase = true) }
        
        // Group tasks by their date identifiers
        val groupedTasks = dateTasks.groupBy { task -> 
            "${task.title.lowercase().trim()}:${task.dueDate?.time ?: 0}" 
        }
        
        // Process each group of tasks with the same title and date
        groupedTasks.forEach { (dateKey, tasksForDate) ->
            // Skip if this task was manually deleted
            if (manuallyDeletedDateTasks.contains(dateKey)) {
                Log.d("TaskViewModel", "Skipping manually deleted date task during cleanup: $dateKey")
                // Remove all these tasks since the user deleted them
                for (task in tasksForDate) {
                    _tasks.removeAll { it.id == task.id }
                    taskService.deleteTask(task.id)
                }
                return@forEach
            }
            
            if (tasksForDate.size > 1) {
                Log.d("TaskViewModel", "Found ${tasksForDate.size} duplicate tasks for key: $dateKey")
                
                // Keep only the most recent task with this date ID
                val mostRecent = tasksForDate.maxByOrNull { it.createdAt.time } ?: tasksForDate.first()
                dateTaskMap[dateKey] = mostRecent
                
                // Mark duplicates for removal
                val duplicates = tasksForDate.filter { it.id != mostRecent.id }
                if (duplicates.isNotEmpty()) {
                    Log.d("TaskViewModel", "Removing ${duplicates.size} duplicate tasks for key: $dateKey")
                    
                    // Remove the duplicates
                    for (duplicate in duplicates) {
                        _tasks.removeAll { it.id == duplicate.id }
                        taskService.deleteTask(duplicate.id)
                    }
                }
            } else if (tasksForDate.isNotEmpty()) {
                // Just one task for this date, keep it
                dateTaskMap[dateKey] = tasksForDate.first()
            }
        }
        
        // Now look for important dates with subtasks but no corresponding task
        _importantDates.filter { it.subtasks.isNotEmpty() }.forEach { date ->
            val exactTitle = "Reminder for ${date.title}"
            val dateKey = "${exactTitle.lowercase().trim()}:${date.dueDate?.time ?: 0}"
            
            // Skip if this task was manually deleted
            if (manuallyDeletedDateTasks.contains(dateKey)) {
                Log.d("TaskViewModel", "Skipping manually deleted date during cleanup: $dateKey")
                return@forEach
            }
            
            if (!dateTaskMap.containsKey(dateKey)) {
                // This date has subtasks but no task representation - create one
                val newTask = Task(
                    id = UUID.randomUUID().toString(),
                    title = exactTitle,
                    description = date.description,
                    dueDate = date.dueDate,
                    frequency = date.frequency,
                    priority = date.priority,
                    isCompleted = date.isCompleted,
                    userId = date.userId,
                    createdAt = Date(),
                    completedAt = date.completedAt,
                    reminderTime = date.reminderTime,
                    subtasks = date.subtasks
                )
                
                _tasks.add(newTask)
                taskService.addTask(newTask)
                dateTaskMap[dateKey] = newTask
                Log.d("TaskViewModel", "Created missing task for date with subtasks: ${date.title}")
            }
        }
        
        Log.d("TaskViewModel", "Finished cleanup of duplicate date tasks")
    }
    
    // Public method to force full cleanup of duplicates - can be called from UI
    fun forceCleanupAllDuplicates() {
        viewModelScope.launch {
            Log.d("TaskViewModel", "Starting comprehensive duplicate cleanup")
            
            // First, ensure proper date-task relationships
            synchronizeDateTaskPairs()
            
            // Then clean up standard duplicates
            removeAllDuplicateTasks()
            
            // Then clean up date-specific duplicates
            cleanupDuplicateTasksForDates()
            
            // Finally, do a standard cleanup
            removeDuplicateTasks()
            
            Log.d("TaskViewModel", "Completed comprehensive duplicate cleanup")
        }
    }
    
    // Start a periodic cleanup of duplicates 
    private fun startPeriodicDuplicateCleanup() {
        viewModelScope.launch {
            while (true) {
                // Check for duplicate tasks every 30 minutes
                delay(1800000) // 30 minutes
                Log.d("TaskViewModel", "Running periodic duplicate cleanup")
                forceCleanupAllDuplicates()
            }
        }
    }
    
    // Add a public emergency cleanup method that can be called from UI in worst-case scenarios
    fun emergencyCleanupDuplicateTasks() {
        viewModelScope.launch {
            Log.d("TaskViewModel", "*** EMERGENCY CLEANUP INITIATED ***")
            
            // Get all date-related tasks
            val dateReminderTasks = _tasks.filter { it.title.startsWith("Reminder for ", ignoreCase = true) }
            Log.d("TaskViewModel", "Found ${dateReminderTasks.size} date reminder tasks")
            
            // Group by exact title+date
            val taskGroups = dateReminderTasks.groupBy { task ->
                "${task.title.lowercase().trim()}:${task.dueDate?.time ?: 0}"
            }
            
            // Count duplicates
            var duplicateCount = 0
            taskGroups.forEach { (key, tasks) ->
                if (tasks.size > 1) {
                    duplicateCount += tasks.size - 1
                    Log.d("TaskViewModel", "Group '$key' has ${tasks.size} duplicates")
                }
            }
            
            Log.d("TaskViewModel", "Total duplicate groups: ${taskGroups.count { it.value.size > 1 }}")
            Log.d("TaskViewModel", "Total duplicate tasks: $duplicateCount")
            
            // Reset the task list completely (radical approach)
            if (duplicateCount > 0) {
                // Run a full synchronization
                synchronizeDateTaskPairs()
                delay(500)
                removeAllDuplicateTasks()
                delay(500)
                cleanupDuplicateTasksForDates()
                
                Log.d("TaskViewModel", "*** EMERGENCY CLEANUP COMPLETED ***")
                
                // Force reload of tasks
                loadTasks()
                loadImportantDates()
            }
        }
    }
    
    // Public method for emergency fixing of Test Date duplicates
    fun fixTestDateDuplicates() {
        viewModelScope.launch {
            Log.d("TaskViewModel", "👀 DIRECTLY FIXING TEST DATE DUPLICATES 👀")
            
            // Find all tasks with "Reminder for Test Date" title
            val testDateTasks = _tasks.filter { 
                it.title.equals("Reminder for Test Date", ignoreCase = true) 
            }
            
            if (testDateTasks.size > 1) {
                Log.d("TaskViewModel", "Found ${testDateTasks.size} duplicate Test Date tasks!")
                
                // Keep the best one, remove all others
                val bestTask = testDateTasks.maxByOrNull { t -> 
                    t.subtasks.size * 100 + (t.createdAt.time / 1000)
                } ?: testDateTasks.first()
                
                // Remove all except the best one
                for (task in testDateTasks) {
                    if (task.id != bestTask.id) {
                        Log.d("TaskViewModel", "🔥 Removing duplicate Test Date task ID: ${task.id}")
                        _tasks.removeAll { it.id == task.id }
                        taskService.deleteTask(task.id)
                        taskService.deleteTaskDirectly(task.id)
                    }
                }
                
                // Force a reload
                delay(500)
                loadTasks()
                
                Log.d("TaskViewModel", "✅ Completed Test Date duplicate fix!")
            } else {
                Log.d("TaskViewModel", "No Test Date duplicates found")
            }
            
            // Also fix any Test Date 2 duplicates
            val testDate2Tasks = _tasks.filter { 
                it.title.equals("Reminder for Test Date 2", ignoreCase = true) 
            }
            
            if (testDate2Tasks.size > 1) {
                Log.d("TaskViewModel", "Found ${testDate2Tasks.size} duplicate Test Date 2 tasks!")
                
                // Keep the best one, remove all others
                val bestTask = testDate2Tasks.maxByOrNull { t -> 
                    t.subtasks.size * 100 + (t.createdAt.time / 1000)
                } ?: testDate2Tasks.first()
                
                // Remove all except the best one
                for (task in testDate2Tasks) {
                    if (task.id != bestTask.id) {
                        Log.d("TaskViewModel", "🔥 Removing duplicate Test Date 2 task ID: ${task.id}")
                        _tasks.removeAll { it.id == task.id }
                        taskService.deleteTask(task.id)
                        taskService.deleteTaskDirectly(task.id)
                    }
                }
                
                // Force a reload
                delay(500)
                loadTasks()
                
                Log.d("TaskViewModel", "✅ Completed Test Date 2 duplicate fix!")
            }
            
            // Finally, run a general cleanup
            forceCleanupAllDuplicates()
        }
    }

    // Add a method to add subtasks to a date without creating duplicates
    fun syncDateWithTask(date: Task) {
        if (date.subtasks.isEmpty()) return
        
        // Only create a task if it doesn't already exist
        val exactTitle = "Reminder for ${date.title}"
        val dateKey = "${exactTitle.lowercase().trim()}:${date.dueDate?.time ?: 0}"
        
        // Skip if this task was manually deleted
        if (manuallyDeletedDateTasks.contains(dateKey)) {
            Log.d("TaskViewModel", "Skipping manually deleted date task during sync: $dateKey")
            return
        }
        
        // Find existing task
        val existingTask = _tasks.firstOrNull { task ->
            task.title.equals(exactTitle, ignoreCase = true) && 
            isSameDate(task.dueDate, date.dueDate)
        }
        
        if (existingTask != null) {
            // Update existing task with new subtasks and matching completion status
            val updatedTask = existingTask.copy(
                subtasks = date.subtasks,
                isCompleted = date.isCompleted,
                completedAt = date.completedAt
            )
            val taskIndex = _tasks.indexOfFirst { it.id == existingTask.id }
            if (taskIndex != -1) {
                _tasks[taskIndex] = updatedTask
                taskService.updateTask(updatedTask)
                Log.d("TaskViewModel", "Updated existing task for date: ${date.title}")
            }
        } else {
            // Create new task for this date
            val newTask = Task(
                id = UUID.randomUUID().toString(),
                title = exactTitle,
                description = date.description,
                dueDate = date.dueDate,
                frequency = date.frequency,
                priority = date.priority,
                isCompleted = date.isCompleted,
                userId = date.userId,
                createdAt = Date(),
                completedAt = date.completedAt,
                reminderTime = date.reminderTime,
                subtasks = date.subtasks
            )
            
            _tasks.add(newTask)
            taskService.addTask(newTask)
            Log.d("TaskViewModel", "Created new task for date: ${date.title}")
        }
    }

    // Add this method at the end of the class
    private fun ensurePersistenceWithRetry(task: Task, isCompleted: Boolean) {
        // Run the first attempt immediately
        viewModelScope.launch {
            Log.d("TaskViewModel", "CRITICAL: Ensuring persistence for task: ${task.title}, isCompleted=$isCompleted")
            
            try {
                // Force immediate save
                taskService.forceSaveToDatabase()
                
                // Start a series of retry attempts with increasing delays
                for (i in 1..3) {
                    delay(i * 500L) // Increasing delay: 500ms, 1000ms, 1500ms
                    Log.d("TaskViewModel", "Persistence retry #$i for task: ${task.title}")
                    taskService.forceSaveToDatabase()
                }
            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error in persistence retry: ${e.message}", e)
            }
        }
    }
    
    // Handle rescheduling of overdue recurring tasks
    fun rescheduleTask(task: Task) {
        if (!task.isOverdueRecurring()) {
            return // Only handle overdue recurring tasks
        }
        
        viewModelScope.launch {
            Log.d("TaskViewModel", "Rescheduling overdue recurring task: ${task.title}")
            
            // Generate the next occurrence using the Task model method
            val nextOccurrence = task.generateNextOccurrence()
            
            // Add the new occurrence
            if (addTask(nextOccurrence)) {
                Log.d("TaskViewModel", "Added next occurrence for: ${task.title} with new due date: ${nextOccurrence.dueDate}")
                
                // Ensure notification is registered for the next occurrence
                val notificationRegistrar = NotificationRegistrar(getApplication<Application>())
                notificationRegistrar.registerTaskNotification(nextOccurrence)
                
                // Mark the original task as completed
                toggleTaskCompletion(task.id)
            }
        }
    }
    
    // Handle completion of recurring tasks by generating the next occurrence
    private fun handleRecurringTaskCompletion(task: Task) {
        if (task.isRecurring()) {
            val nextOccurrence = task.generateNextOccurrence()
            
            // Add the next occurrence task
            if (addTask(nextOccurrence)) {
                Log.d("TaskViewModel", "Created next occurrence for recurring task: ${task.title}")
                
                // Register notification for the new task occurrence
                val notificationRegistrar = NotificationRegistrar(getApplication<Application>())
                notificationRegistrar.registerTaskNotification(nextOccurrence)
                
                // Schedule a notification for the next occurrence (keeping for backward compatibility)
                if (nextOccurrence.dueDate != null) {
                    // Use notificationService to schedule a reminder
                    notificationService.scheduleImportantDateReminder(nextOccurrence)
                }
            }
        }
    }

    // Public method for forcing complete save of all app data
    fun forceSaveAllData() {
        viewModelScope.launch {
            Log.d("TaskViewModel", "Force saving ALL app data")
            
            // First force save through the TaskService
            taskService.forceSaveToDatabase()
            
            // Then try a direct save with the most critical data
            val sharedPrefs = getApplication<Application>().getSharedPreferences("task_reminder_data", Context.MODE_PRIVATE)
            val datesJson = gson.toJson(_importantDates.toList())
            val tasksJson = gson.toJson(_tasks.toList())
            
            // Try individual saves as a backup
            try {
                val editor = sharedPrefs.edit()
                
                // First save tasks - use TaskService's constants for consistency
                editor.putString(TaskService.TASKS_KEY, tasksJson)
                val tasksSaved = editor.commit()
                
                // Then save dates separately - use TaskService's constants for consistency
                val editor2 = sharedPrefs.edit()
                editor2.putString(TaskService.IMPORTANT_DATES_KEY, datesJson)
                val datesSaved = editor2.commit()
                
                // Log results
                Log.d("TaskViewModel", "Direct save results - Tasks: $tasksSaved, Dates: $datesSaved")
                
                // Verify that dates were actually saved
                val verifyDatesJson = sharedPrefs.getString(TaskService.IMPORTANT_DATES_KEY, null)
                if (verifyDatesJson != null) {
                    Log.d("TaskViewModel", "Verification successful - dates JSON exists in SharedPreferences")
                } else {
                    Log.e("TaskViewModel", "CRITICAL: Dates JSON not found after save, doing emergency extra save")
                    // One more emergency save attempt
                    sharedPrefs.edit()
                        .putString("important_dates", datesJson) // Try both keys to be safe
                        .putString("important_dates_emergency", datesJson)
                        .commit()
                }
                
                // Also save individual date files as additional backup
                for (date in _importantDates) {
                    try {
                        val dateJson = gson.toJson(date)
                        val file = File(getApplication<Application>().filesDir, "date_${date.id}.json")
                        file.writeText(dateJson)
                        
                        // Add a complete dates backup as well
                        if (date == _importantDates.firstOrNull()) {
                            val allDatesJson = gson.toJson(_importantDates.toList())
                            val allDatesFile = File(getApplication<Application>().filesDir, "all_dates_backup.json")
                            allDatesFile.writeText(allDatesJson)
                            Log.d("TaskViewModel", "Saved complete dates backup to file")
                        }
                    } catch (e: Exception) {
                        Log.e("TaskViewModel", "Error saving individual date file: ${e.message}")
                    }
                }
                
                // Force another save through service after individual backups
                taskService.forceSaveToDatabase()
                
                Log.d("TaskViewModel", "Completed force saving ALL app data")
            } catch (e: Exception) {
                Log.e("TaskViewModel", "Error during forceSaveAllData: ${e.message}")
                // Fall back to service save again
                taskService.forceSaveToDatabase()
            }
        }
    }
}
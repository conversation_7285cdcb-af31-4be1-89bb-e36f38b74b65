{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2778,2890", "endColumns": "111,112", "endOffsets": "2885,2998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1ba429aa6de3be7de3bb6e415af4e51\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4981", "endColumns": "147", "endOffsets": "5124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,15269", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,15344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,88", "endOffsets": "125,215,304"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3003,16085,16175", "endColumns": "74,89,88", "endOffsets": "3073,16170,16259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61468bcbedfc450e580f06e70db30d2d\\transformed\\play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3987,4094,4251,4378,4488,4629,4754,4877,5129,5277,5385,5547,5675,5829,5985,6051,6114", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4089,4246,4373,4483,4624,4749,4872,4976,5272,5380,5542,5670,5824,5980,6046,6109,6188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6302,6771,6871,6984", "endColumns": "99,99,112,97", "endOffsets": "6397,6866,6979,7077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14833", "endColumns": "89", "endOffsets": "14918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3078,3173,3275,3373,3472,3580,3685,15719", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3168,3270,3368,3467,3575,3680,3801,15815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8559,8698,8831,8938,9070,9186,9282,9395,9539,9663,9818,9903,10002,10092,10186,10300,10422,10526,10659,10786,10921,11093,11221,11339,11465,11585,11676,11774,11892,12031,12127,12235,12338,12471,12614,12720,12817,12897,12995,13087,13203,13287,13372,13473,13553,13638,13737,13837,13932,14032,14119,14223,14324,14428,14550,14630,14734", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "8693,8826,8933,9065,9181,9277,9390,9534,9658,9813,9898,9997,10087,10181,10295,10417,10521,10654,10781,10916,11088,11216,11334,11460,11580,11671,11769,11887,12026,12122,12230,12333,12466,12609,12715,12812,12892,12990,13082,13198,13282,13367,13468,13548,13633,13732,13832,13927,14027,14114,14218,14319,14423,14545,14625,14729,14828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,274,362,458,557,645,721,809,898,979,1065,1155,1224,1310,1384,1455,1525,1603,1670", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "269,357,453,552,640,716,804,893,974,1060,1150,1219,1305,1379,1450,1520,1598,1665,1785"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3806,3899,6402,6584,6683,8395,8471,14923,15012,15093,15179,15349,15418,15504,15578,15649,15820,15898,15965", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "3894,3982,6493,6678,6766,8466,8554,15007,15088,15174,15264,15413,15499,15573,15644,15714,15893,15960,16080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6193,6498,7082,7203,7342,7472,7594,7723,7859,7964,8116,8268", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "6297,6579,7198,7337,7467,7589,7718,7854,7959,8111,8263,8390"}}]}]}
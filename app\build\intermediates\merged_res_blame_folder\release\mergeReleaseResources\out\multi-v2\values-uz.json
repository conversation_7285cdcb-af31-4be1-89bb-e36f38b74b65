{"logs": [{"outputFile": "com.taskiq.app-mergeReleaseResources-79:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5007", "endColumns": "149", "endOffsets": "5152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4599,4687,4773,4874,4954,5038,5138,5242,5338,5437,5525,5633,5733,5836,5975,6055,6171", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4594,4682,4768,4869,4949,5033,5133,5237,5333,5432,5520,5628,5728,5831,5970,6050,6166,6269"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8698,8815,8930,9044,9158,9255,9353,9468,9601,9710,9852,9936,10040,10134,10232,10346,10467,10576,10701,10824,10954,11122,11247,11368,11492,11613,11708,11806,11923,12049,12153,12263,12370,12493,12621,12734,12838,12922,13018,13112,13242,13330,13416,13517,13597,13681,13781,13885,13981,14080,14168,14276,14376,14479,14618,14698,14814", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "8810,8925,9039,9153,9250,9348,9463,9596,9705,9847,9931,10035,10129,10227,10341,10462,10571,10696,10819,10949,11117,11242,11363,11487,11608,11703,11801,11918,12044,12148,12258,12365,12488,12616,12729,12833,12917,13013,13107,13237,13325,13411,13512,13592,13676,13776,13880,13976,14075,14163,14271,14371,14474,14613,14693,14809,14912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6251,6587,7205,7325,7456,7584,7731,7863,8008,8105,8244,8384", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "6360,6672,7320,7451,7579,7726,7858,8003,8100,8239,8379,8520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,375,479,586,682,765,855,948,1031,1112,1195,1269,1354,1430,1505,1578,1661,1729", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "285,370,474,581,677,760,850,943,1026,1107,1190,1264,1349,1425,1500,1573,1656,1724,1841"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3826,3925,6483,6677,6784,8525,8608,15006,15099,15182,15263,15430,15504,15589,15665,15740,15914,15997,16065", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "3920,4005,6582,6779,6875,8603,8693,15094,15177,15258,15341,15499,15584,15660,15735,15808,15992,16060,16177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,251", "endColumns": "84,110,100", "endOffsets": "135,246,347"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3005,16182,16293", "endColumns": "84,110,100", "endOffsets": "3085,16288,16389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4010,4115,4265,4394,4503,4648,4781,4901,5157,5329,5437,5596,5728,5882,6044,6110,6171", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "4110,4260,4389,4498,4643,4776,4896,5002,5324,5432,5591,5723,5877,6039,6105,6166,6246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14917", "endColumns": "88", "endOffsets": "15001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,15346", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,15425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3090,3192,3294,3395,3495,3603,3707,15813", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3187,3289,3390,3490,3598,3702,3821,15909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0aa768bf2c77d0f2a11c0fea0a786\\transformed\\browser-1.8.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6365,6880,6989,7099", "endColumns": "117,108,109,105", "endOffsets": "6478,6984,7094,7200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,119", "endOffsets": "159,279"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2776,2885", "endColumns": "108,119", "endOffsets": "2880,3000"}}]}]}
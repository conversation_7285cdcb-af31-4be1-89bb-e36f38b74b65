{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-77:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\75e92b145e13fc673dc76999f901f30d\\transformed\\credentials-1.5.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,111", "endOffsets": "159,271"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2817,2926", "endColumns": "108,111", "endOffsets": "2921,3033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72b42ec2c0a6db09976b88668f84c08b\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,284,369,478,583,660,737,830,920,1003,1086,1173,1245,1329,1405,1483,1559,1641,1709", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "279,364,473,578,655,732,825,915,998,1081,1168,1240,1324,1400,1478,1554,1636,1704,1824"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3858,3953,6494,6693,6798,8508,8585,15017,15107,15190,15273,15443,15515,15599,15675,15753,15930,16012,16080", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "3948,4033,6598,6793,6870,8580,8673,15102,15185,15268,15355,15510,15594,15670,15748,15824,16007,16075,16195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e866c3f6718b50411e7e0aa30260699c\\transformed\\play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4038,4142,4310,4432,4542,4693,4818,4929,5168,5339,5448,5623,5751,5910,6071,6140,6206", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4137,4305,4427,4537,4688,4813,4924,5023,5334,5443,5618,5746,5905,6066,6135,6201,6285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dff1d73b7cef632e787de4d59cae0ad8\\transformed\\biometric-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6290,6603,7193,7318,7456,7609,7736,7864,8011,8111,8245,8384", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "6389,6688,7313,7451,7604,7731,7859,8006,8106,8240,8379,8503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55690b61ef7b0489a32a7ec346714862\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3121,3218,3320,3418,3517,3631,3736,15829", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3213,3315,3413,3512,3626,3731,3853,15925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\396ff811c0e00aef5e8ac116f6d99809\\transformed\\material-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14929", "endColumns": "87", "endOffsets": "15012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4dba44ce16815e024cb356c378b89b2b\\transformed\\appcompat-1.2.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,15360", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,15438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2989e2c91f6d0a9894b01384862e89f4\\transformed\\play-services-basement-18.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5028", "endColumns": "139", "endOffsets": "5163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\718231422f48010e85ca6edf2e677575\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,226", "endColumns": "82,87,87", "endOffsets": "133,221,309"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3038,16200,16288", "endColumns": "82,87,87", "endOffsets": "3116,16283,16371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11771762b22044a889273e9ed7a93127\\transformed\\material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8678,8793,8910,9032,9147,9247,9346,9462,9600,9722,9864,9948,10047,10139,10235,10352,10476,10580,10720,10856,11000,11161,11293,11414,11539,11660,11753,11853,11973,12097,12196,12300,12406,12547,12694,12805,12904,12978,13073,13169,13273,13360,13447,13559,13639,13726,13821,13926,14017,14126,14214,14320,14421,14531,14649,14729,14832", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "8788,8905,9027,9142,9242,9341,9457,9595,9717,9859,9943,10042,10134,10230,10347,10471,10575,10715,10851,10995,11156,11288,11409,11534,11655,11748,11848,11968,12092,12191,12295,12401,12542,12689,12800,12899,12973,13068,13164,13268,13355,13442,13554,13634,13721,13816,13921,14012,14121,14209,14315,14416,14526,14644,14724,14827,14924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45f9a3db26e33e80ab043af5a0e43024\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6394,6875,6974,7089", "endColumns": "99,98,114,103", "endOffsets": "6489,6969,7084,7188"}}]}]}
package com.taskiq.app.model

import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Supabase data models for cloud storage
 * These models represent the database schema structure
 */

@Serializable
data class SupabaseUser(
    val id: String? = null,
    val email: String,
    val firstName: String? = null,
    val lastName: String? = null,
    val createdAt: String? = null,
    val updatedAt: String? = null
)

@Serializable
data class SupabaseTask(
    val id: String,
    val userId: String,
    val title: String,
    val description: String? = null,
    val isCompleted: Boolean = false,
    val priority: String, // HIGH, MEDIUM, LOW
    val category: String? = null,
    val dueDate: String? = null, // ISO string format
    val hasNoDueDate: Boolean = false,
    val subtasks: List<String> = emptyList(), // JSON array of subtask strings
    val createdAt: String? = null,
    val updatedAt: String? = null
)

@Serializable
data class SupabaseBill(
    val id: String,
    val userId: String,
    val title: String,
    val amount: Double,
    val dueDate: String, // ISO string format
    val status: String, // PENDING, PAID, OVERDUE
    val type: String, // CREDIT_CARD, LOAN_EMI, UTILITY, OTHER
    val description: String? = null,
    val createdAt: String? = null,
    val updatedAt: String? = null
)

@Serializable
data class SupabaseImportantDate(
    val id: String,
    val userId: String,
    val title: String,
    val date: String, // ISO string format
    val description: String? = null,
    val isRecurring: Boolean = false,
    val recurringType: String? = null, // YEARLY, MONTHLY, WEEKLY
    val subtasks: List<String> = emptyList(), // JSON array of subtask strings
    val createdAt: String? = null,
    val updatedAt: String? = null
)

@Serializable
data class SupabaseLinkedEmail(
    val id: String? = null,
    val userId: String,
    val email: String,
    val isActive: Boolean = true,
    val createdAt: String? = null,
    val updatedAt: String? = null
)

/**
 * Extension functions to convert between app models and Supabase models
 */

fun Task.toSupabaseTask(userId: String): SupabaseTask {
    return SupabaseTask(
        id = this.id,
        userId = userId,
        title = this.title,
        description = this.description,
        isCompleted = this.isCompleted,
        priority = this.priority.name,
        category = this.category,
        dueDate = this.dueDate?.toString(),
        hasNoDueDate = this.hasNoDueDate,
        subtasks = this.subtasks
    )
}

fun SupabaseTask.toTask(): Task {
    return Task(
        id = this.id,
        title = this.title,
        description = this.description ?: "",
        isCompleted = this.isCompleted,
        priority = Priority.valueOf(this.priority),
        category = this.category,
        dueDate = this.dueDate?.let { LocalDate.parse(it) },
        hasNoDueDate = this.hasNoDueDate,
        subtasks = this.subtasks.toMutableList()
    )
}

fun Bill.toSupabaseBill(userId: String): SupabaseBill {
    return SupabaseBill(
        id = this.id,
        userId = userId,
        title = this.title,
        amount = this.amount,
        dueDate = this.dueDate.toString(),
        status = this.status.name,
        type = this.type.name,
        description = this.description
    )
}

fun SupabaseBill.toBill(): Bill {
    return Bill(
        id = this.id,
        title = this.title,
        amount = this.amount,
        dueDate = LocalDate.parse(this.dueDate),
        status = BillStatus.valueOf(this.status),
        type = BillType.valueOf(this.type),
        description = this.description ?: ""
    )
}

fun Task.toSupabaseImportantDate(userId: String): SupabaseImportantDate {
    return SupabaseImportantDate(
        id = this.id,
        userId = userId,
        title = this.title,
        date = this.dueDate?.toString() ?: LocalDate.now().toString(),
        description = this.description,
        isRecurring = false, // Can be enhanced later
        subtasks = this.subtasks
    )
}

fun SupabaseImportantDate.toTask(): Task {
    return Task(
        id = this.id,
        title = this.title,
        description = this.description ?: "",
        isCompleted = false,
        priority = Priority.MEDIUM,
        dueDate = LocalDate.parse(this.date),
        subtasks = this.subtasks.toMutableList()
    )
}

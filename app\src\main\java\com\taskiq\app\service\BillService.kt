package com.taskiq.app.service

import android.content.Context
import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.model.BillType
import java.time.LocalDate
import java.util.UUID
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import java.lang.reflect.Type
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.runBlocking

/**
 * Service to manage bills in the application.
 * Uses Supabase for cloud storage with local fallback.
 */
class BillService(private val context: Context) {

    // Authentication service
    private val authService = AuthService(context)

    // Supabase data service with fallback
    private val supabaseHttpService = try {
        SupabaseHttpService(context)
    } catch (e: Exception) {
        Log.w(TAG, "Supabase not available, using local storage fallback: ${e.message}")
        null
    }

    // In-memory storage for caching
    private val bills = mutableListOf<Bill>()
    private val linkedEmails = mutableListOf<String>()

    // Email authentication service for secure email access
    private val emailAuthService = EmailAuthService(context)

    // Gmail service for real email scanning
    private var gmailService: com.taskiq.app.service.GmailService? = null

    // Keep SharedPreferences for backward compatibility and offline caching
    private val sharedPreferences = context.getSharedPreferences("bill_data", Context.MODE_PRIVATE)
    
    // Custom serializer/deserializer for LocalDate
    private val gson = GsonBuilder()
        .registerTypeAdapter(LocalDate::class.java, object : JsonSerializer<LocalDate> {
            override fun serialize(src: LocalDate, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
                return JsonPrimitive(src.format(DateTimeFormatter.ISO_LOCAL_DATE))
            }
        })
        .registerTypeAdapter(LocalDate::class.java, object : JsonDeserializer<LocalDate> {
            override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): LocalDate {
                return LocalDate.parse(json.asString, DateTimeFormatter.ISO_LOCAL_DATE)
            }
        })
        .create()
    
    companion object {
        private const val TAG = "BillService"
        private const val KEY_BILLS = "bills"
        private const val KEY_EMAILS = "linked_emails"
    }
    
    init {
        // Load cached data from SharedPreferences first for immediate UI display
        loadBillsFromPrefs()

        Log.d(TAG, "BillService initialized with ${bills.size} cached bills and ${linkedEmails.size} linked emails")
    }

    /**
     * Sync bills from cloud storage (placeholder for future Supabase integration)
     */
    fun syncFromCloud(): Boolean {
        return try {
            Log.d(TAG, "Cloud sync not yet implemented - using local storage")
            // For now, just return true as we're using local storage
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error in sync placeholder: ${e.message}", e)
            false
        }
    }
    
    private fun loadBillsFromPrefs() {
        val billsJson = sharedPreferences.getString(KEY_BILLS, null)
        val emailsJson = sharedPreferences.getString(KEY_EMAILS, null)
        
        if (billsJson != null) {
            try {
                val type = object : TypeToken<List<Bill>>() {}.type
                val loadedBills = gson.fromJson<List<Bill>>(billsJson, type)
                bills.clear()
                bills.addAll(loadedBills)
                Log.d(TAG, "Loaded ${bills.size} bills from preferences")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading bills: ${e.message}")
            }
        }
        
        if (emailsJson != null) {
            try {
                val type = object : TypeToken<List<String>>() {}.type
                val loadedEmails = gson.fromJson<List<String>>(emailsJson, type)
                linkedEmails.clear()
                linkedEmails.addAll(loadedEmails)
                Log.d(TAG, "Loaded ${linkedEmails.size} emails from preferences")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading emails: ${e.message}")
            }
        }
    }
    
    private fun saveBillsToPrefs() {
        try {
            val billsJson = gson.toJson(bills)
            val emailsJson = gson.toJson(linkedEmails)

            sharedPreferences.edit()
                .putString(KEY_BILLS, billsJson)
                .putString(KEY_EMAILS, emailsJson)
                .commit() // Use commit() for immediate write

            Log.d(TAG, "Saved ${bills.size} bills and ${linkedEmails.size} emails to preferences")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving bills: ${e.message}")
        }
    }
    
    fun getBills(): List<Bill> {
        return bills.toList()
    }
    
    fun getBill(billId: String): Bill? {
        return bills.find { it.id == billId }
    }
    
    fun addBill(bill: Bill): Boolean {
        return try {
            Log.d("BillService", "Adding new bill: ${bill.title}")

            // Add to in-memory storage first
            bills.add(bill)

            // Save to local cache immediately
            saveBillsToPrefs()

            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            supabaseHttpService.addBill(bill)
                            Log.d("BillService", "Bill synced to Supabase: ${bill.title}")
                        } catch (e: Exception) {
                            Log.w("BillService", "Failed to sync bill to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w("BillService", "Could not start Supabase sync: ${e.message}")
                }
            }

            true

        } catch (e: Exception) {
            Log.e("BillService", "Error adding bill: ${e.message}", e)
            false
        }
    }
    
    fun updateBill(bill: Bill): Boolean {
        return try {
            val index = bills.indexOfFirst { it.id == bill.id }
            if (index != -1) {
                Log.d("BillService", "Updating bill: ${bill.title}")

                // Update in-memory storage
                bills[index] = bill

                // Save to local cache immediately
                saveBillsToPrefs()

                // Try to sync with Supabase in background
                if (supabaseHttpService != null) {
                    try {
                        GlobalScope.launch {
                            try {
                                supabaseHttpService.updateBill(bill)
                                Log.d("BillService", "Bill update synced to Supabase: ${bill.title}")
                            } catch (e: Exception) {
                                Log.w("BillService", "Failed to sync bill update to Supabase: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.w("BillService", "Could not start Supabase sync: ${e.message}")
                    }
                }

                true
            } else {
                Log.w("BillService", "Bill not found for update: ${bill.id}")
                false
            }

        } catch (e: Exception) {
            Log.e("BillService", "Error updating bill: ${e.message}", e)
            false
        }
    }
    
    fun deleteBill(billId: String): Boolean {
        return try {
            val billToDelete = bills.find { it.id == billId }
            if (billToDelete == null) {
                Log.w("BillService", "Could not find bill with ID $billId for deletion")
                return false
            }

            Log.d("BillService", "Deleting bill: ${billToDelete.title}")

            // Remove from in-memory list
            bills.removeIf { it.id == billId }

            // Save to local cache immediately
            saveBillsToPrefs()

            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            supabaseHttpService.deleteBill(billId)
                            Log.d("BillService", "Bill deletion synced to Supabase: ${billToDelete.title}")
                        } catch (e: Exception) {
                            Log.w("BillService", "Failed to sync bill deletion to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w("BillService", "Could not start Supabase sync: ${e.message}")
                }
            }

            true

        } catch (e: Exception) {
            Log.e("BillService", "Error deleting bill: ${e.message}", e)
            false
        }
    }
    
    fun markBillAsPaid(billId: String): Boolean {
        val index = bills.indexOfFirst { it.id == billId }
        if (index != -1) {
            val bill = bills[index]
            bills[index] = bill.copy(
                isPaid = true,
                status = BillStatus.PAID,
                paymentDate = LocalDate.now()
            )
            runBlocking { saveBillsToPrefs() }
        }
        return true
    }
    
    fun getLinkedEmails(): List<String> {
        Log.d(TAG, "Getting linked emails. Local list: ${linkedEmails.size}, EmailAuthService: ${emailAuthService.getVerifiedEmails().size}")

        // Return the local linkedEmails list which is persisted to SharedPreferences
        // This ensures consistency with what we actually store
        return linkedEmails.toList()
    }
    
    fun addLinkedEmail(email: String): Boolean {
        Log.d(TAG, "Checking if email can be linked: $email")
        Log.d(TAG, "Current linkedEmails: $linkedEmails")

        val isAlreadyLinked = linkedEmails.contains(email)
        Log.d(TAG, "Email $email already linked: $isAlreadyLinked")

        // Return true if email can be added (i.e., not already linked)
        return !isAlreadyLinked
    }
    
    fun initiateEmailVerification(email: String, activity: android.app.Activity): Boolean {
        Log.d(TAG, "BillService: Starting email verification for: $email")

        // Start the OAuth verification process
        val verified = emailAuthService.initiateEmailVerification(email, activity)

        Log.d(TAG, "BillService: EmailAuthService verification result for $email: $verified")

        if (verified) {
            // Add to our linked emails list
            val added = addVerifiedEmail(email)
            Log.d(TAG, "BillService: Email verification successful and added to linked emails: $email, added: $added")
        } else {
            Log.d(TAG, "BillService: Email verification failed: $email")
        }

        return verified
    }
    
    fun addVerifiedEmail(email: String): Boolean {
        Log.d(TAG, "Adding verified email: $email")
        Log.d(TAG, "Current linkedEmails before adding: $linkedEmails")

        // Check if already in local list
        if (linkedEmails.contains(email)) {
            Log.d(TAG, "Email $email already in local linkedEmails list")
            return false
        }

        // Add to local list and save immediately
        linkedEmails.add(email)
        runBlocking { saveBillsToPrefs() }

        Log.d(TAG, "Successfully added $email to linkedEmails. New list: $linkedEmails")
        Log.d(TAG, "Total linked emails: ${linkedEmails.size}")

        return true
    }
    
    fun removeLinkedEmail(email: String): Boolean {
        // Remove from both local list and auth service
        linkedEmails.remove(email)
        saveBillsToPrefs()
        return emailAuthService.removeVerifiedEmail(email)
    }
    
    suspend fun scanEmailsAsync(): List<Bill> {
        // Use the local linkedEmails list instead of EmailAuthService
        Log.d(TAG, "=== BILLSERVICE ASYNC SCAN STARTED ===")
        Log.d(TAG, "Scanning emails for bills. Linked emails: ${linkedEmails.size}")
        Log.d(TAG, "Current linked emails: $linkedEmails")

        // Only use real Gmail API - no fallback simulation
        Log.d(TAG, "Calling tryRealGmailScanAsync()...")
        val realBills = tryRealGmailScanAsync()
        Log.d(TAG, "tryRealGmailScanAsync() returned ${realBills.size} bills")

        if (realBills.isNotEmpty()) {
            Log.d(TAG, "Real Gmail API scan successful: ${realBills.size} bills found")

            // Add the detected bills to the bills list and save them
            realBills.forEach { bill ->
                // Check if bill already exists to avoid duplicates
                val existingBill = bills.find { it.title == bill.title && it.amount == bill.amount && it.dueDate == bill.dueDate }
                if (existingBill == null) {
                    bills.add(bill)
                    Log.d(TAG, "Added new bill: ${bill.title} - $${bill.amount}")
                } else {
                    Log.d(TAG, "Bill already exists, skipping: ${bill.title}")
                }
            }

            // Save the updated bills to preferences
            saveBillsToPrefs()

            return realBills
        } else {
            Log.w(TAG, "Real Gmail API scan returned no bills")

            if (linkedEmails.isEmpty()) {
                Log.w(TAG, "No linked emails found")
                return emptyList()
            }

            // Check if Gmail service is available
            val hasGmailService = (gmailService != null) ||
                (com.taskiq.app.viewmodel.GmailAuthViewModel.getGlobalGmailService() != null)

            if (!hasGmailService) {
                Log.e(TAG, "Gmail service not available. Please ensure:")
                Log.e(TAG, "1. OAuth consent screen is properly configured")
                Log.e(TAG, "2. Gmail API is enabled in Google Cloud Console")
                Log.e(TAG, "3. User has granted Gmail permissions during sign-in")
                return emptyList()
            } else {
                Log.w(TAG, "Gmail service is available but no bills were found in the email scan")
                Log.w(TAG, "This could mean:")
                Log.w(TAG, "1. No bill-related emails in the last 7 days")
                Log.w(TAG, "2. Bills are in a format not recognized by the scanner")
                Log.w(TAG, "3. Bills are in spam/promotions folders (not currently scanned)")
                return emptyList()
            }
        }
    }

    // Keep the synchronous version for backward compatibility but make it non-blocking
    fun scanEmails(): List<Bill> {
        Log.d(TAG, "Synchronous scanEmails called - returning empty list to avoid blocking")
        return emptyList()
    }

    /**
     * Try to use real Gmail service for scanning if available (async version)
     */
    private suspend fun tryRealGmailScanAsync(): List<Bill> {
        return try {
            Log.d(TAG, "=== TRYING REAL GMAIL SCAN ===")

            // First try the local Gmail service
            var service = gmailService
            Log.d(TAG, "Local Gmail service: ${service != null}")

            // If local service is null, try to get the global service
            if (service == null) {
                service = com.taskiq.app.viewmodel.GmailAuthViewModel.getGlobalGmailService()
                Log.d(TAG, "Retrieved global Gmail service: ${service != null}")
            }

            service?.let { gmailSvc ->
                Log.d(TAG, "Gmail service found! Starting real Gmail API scan...")
                Log.d(TAG, "Gmail service class: ${gmailSvc.javaClass.simpleName}")

                // Call suspend function directly without blocking
                val bills = gmailSvc.scanEmailsForBills()
                Log.d(TAG, "Real Gmail API scan completed: ${bills.size} bills found")
                bills
            } ?: run {
                Log.w(TAG, "No Gmail service available (local or global)")
                Log.w(TAG, "This means OAuth authentication may have failed or Gmail service was not properly initialized")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "=== ERROR WITH REAL GMAIL SCAN ===")
            Log.e(TAG, "Error with real Gmail scan: ${e.message}")
            Log.e(TAG, "Stack trace: ${e.stackTrace.joinToString("\n")}")
            emptyList()
        }
    }

    /**
     * Set Gmail service for real email scanning
     */
    fun setGmailService(gmailService: com.taskiq.app.service.GmailService?) {
        this.gmailService = gmailService
        Log.d(TAG, "Gmail service ${if (gmailService != null) "set" else "cleared"} for real email scanning")
    }

    /**
     * Clear all bills (used to remove mock data)
     */
    private fun clearAllBills() {
        bills.clear()
        sharedPreferences.edit().clear().apply()
        Log.d(TAG, "Cleared all existing bills and preferences")
    }

    /**
     * Force save data to storage with multiple retries
     */
    fun forceSaveToDatabase() {
        Log.d(TAG, "Forcing save of all bills to database - CRITICAL OPERATION")

        // Multiple attempts with exponential backoff approach
        var attempt = 1
        var success = false

        while (!success && attempt <= 3) {
            Log.d(TAG, "Save attempt #$attempt of 3")

            try {
                // Ensure we synchronize to prevent any race conditions
                synchronized(this) {
                    val billsJson = gson.toJson(bills)
                    val emailsJson = gson.toJson(linkedEmails)

                    success = sharedPreferences.edit()
                        .putString(KEY_BILLS, billsJson)
                        .putString(KEY_EMAILS, emailsJson)
                        .commit() // Use commit() for immediate write
                }

                if (success) {
                    Log.d(TAG, "Force save successful on attempt #$attempt")
                    Log.d(TAG, "Saved ${bills.size} bills and ${linkedEmails.size} emails")
                } else {
                    Log.w(TAG, "Force save failed on attempt #$attempt")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Critical error during save attempt: ${e.message}", e)
                success = false
            }

            if (!success) {
                // Backoff delay before retry
                val delayMs = 100L * (1 shl (attempt - 1)) // 100ms, 200ms, 400ms
                Thread.sleep(delayMs)
                attempt++
            }
        }

        // Final fallback
        if (!success) {
            Log.w(TAG, "All immediate save attempts failed, falling back to regular save")
            runBlocking { saveBillsToPrefs() }
        }

        Log.d(TAG, "After forceSaveToDatabase, bills saved: ${bills.size}, emails saved: ${linkedEmails.size}")
    }

}
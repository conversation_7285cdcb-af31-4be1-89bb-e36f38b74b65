package com.taskiq.app.ui.theme

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * UNIFORM LAYOUT STYLING SYSTEM - All layout styling standardized across the app
 * Based on Dashboard screen as the reference standard
 *
 * STANDARD LAYOUT PATTERNS:
 * 1. Screen Content: Use uniformContentBelowHeaderModifier() for main content area
 * 2. List Content: Use uniformContentPadding() for LazyColumn contentPadding
 * 3. List Item Spacing: Use uniformItemSpacing() for verticalArrangement spacing (8.sdp)
 * 4. Initial Spacing: Use uniformInitialPadding() for first item spacing (4.sdp)
 * 5. Section Spacing: Use uniformSectionSpacing() between sections (4.sdp)
 * 6. Item Padding: Use uniformHorizontalPadding() for horizontal padding
 * 7. Empty State: Use uniformEmptyStatePadding() for empty state padding
 * 8. Bottom Spacing: Use uniformBottomSpacing() for FAB clearance (36.sdp)
 */

/**
 * Standard horizontal padding for all screen content
 */
@Composable
fun uniformHorizontalPadding(): Dp = responsiveHorizontalPadding()

/**
 * Standard vertical padding for all screen content
 */
@Composable
fun uniformVerticalPadding(): Dp = responsiveSpacing()

/**
 * Standard initial padding (top padding for first content)
 */
@Composable
fun uniformInitialPadding(): Dp = responsiveSpacing()

/**
 * Standard content padding for all screens
 */
@Composable
fun uniformContentPadding(): PaddingValues = PaddingValues(
    horizontal = uniformHorizontalPadding(),
    vertical = uniformVerticalPadding()
)

/**
 * Standard modifier for screen content with uniform padding
 */
@Composable
fun uniformContentModifier(): Modifier = Modifier
    .fillMaxSize()
    .padding(horizontal = uniformHorizontalPadding())

/**
 * Standard modifier for full width content with uniform padding
 */
@Composable
fun uniformFullWidthContentModifier(): Modifier = Modifier
    .fillMaxWidth()
    .padding(horizontal = uniformHorizontalPadding())

/**
 * Modifier for the root container of a screen (without padding)
 */
val rootContainerModifier = Modifier.fillMaxSize()

/**
 * Modifier for content areas below headers with uniform padding
 */
@Composable
fun uniformContentBelowHeaderModifier(): Modifier = Modifier
    .fillMaxWidth()
    .padding(horizontal = uniformHorizontalPadding())

/**
 * Standard spacing for sections within screens
 */
@Composable
fun uniformSectionSpacing(): Dp = responsiveSpacing()

/**
 * Standard spacing between list items/cards (better visual separation)
 */
@Composable
fun uniformItemSpacing(): Dp = responsiveLargeSpacing()

/**
 * Standard spacing for small gaps (between related elements)
 */
@Composable
fun uniformSmallSpacing(): Dp = responsiveSmallSpacing()

/**
 * Standard spacing for large gaps (between unrelated sections)
 */
@Composable
fun uniformLargeSpacing(): Dp = responsiveLargeSpacing()

/**
 * Standard padding for empty states
 */
@Composable
fun uniformEmptyStatePadding(): Dp = uniformHorizontalPadding() * 4

/**
 * Standard bottom spacing for FAB clearance
 */
@Composable
fun uniformBottomSpacing(): Dp = 36.sdp()

// Legacy functions for backward compatibility - redirect to uniform functions
@Composable
fun standardHorizontalPadding(): Dp = uniformHorizontalPadding()

@Composable
fun contentModifier(): Modifier = uniformContentModifier()

@Composable
fun fullWidthContentModifier(): Modifier = uniformFullWidthContentModifier()

@Composable
fun standardContentPadding(): PaddingValues = uniformContentPadding()

@Composable
fun contentBelowHeaderModifier(): Modifier = uniformContentBelowHeaderModifier()
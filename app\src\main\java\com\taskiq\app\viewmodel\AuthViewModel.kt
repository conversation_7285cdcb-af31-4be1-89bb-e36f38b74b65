package com.taskiq.app.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.ViewModelProvider
import com.taskiq.app.model.User
import com.taskiq.app.service.AuthService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class AuthViewModel(context: Context) : ViewModel() {
    
    enum class AuthState {
        IDLE,
        LOADING,
        SUCCESS,
        ERROR
    }
    
    private val authService = AuthService(context)
    
    private val _user = MutableStateFlow<User?>(null)
    val user: StateFlow<User?> = _user
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error
    
    private val _loginState = MutableStateFlow(AuthState.IDLE)
    val loginState: StateFlow<AuthState> = _loginState
    
    private val _registerState = MutableStateFlow(AuthState.IDLE)
    val registerState: StateFlow<AuthState> = _registerState
    
    private val _updateProfileState = MutableStateFlow(AuthState.IDLE)
    val updateProfileState: StateFlow<AuthState> = _updateProfileState
    
    private val _updatePasswordState = MutableStateFlow(AuthState.IDLE)
    val updatePasswordState: StateFlow<AuthState> = _updatePasswordState
    
    private val _deleteAccountState = MutableStateFlow(AuthState.IDLE)
    val deleteAccountState: StateFlow<AuthState> = _deleteAccountState

    private val _resetPasswordState = MutableStateFlow(AuthState.IDLE)
    val resetPasswordState: StateFlow<AuthState> = _resetPasswordState
    
    // Dark mode state
    private val _isDarkMode = MutableStateFlow(false)
    val isDarkMode: StateFlow<Boolean> = _isDarkMode
    
    init {
        checkCurrentUser()
    }
    
    private fun checkCurrentUser() {
        viewModelScope.launch {
            _user.value = authService.getCurrentUser()
        }
    }
    
    fun register(email: String, password: String) {
        viewModelScope.launch {
            try {
                _registerState.value = AuthState.LOADING
                _error.value = null
                val user = authService.signUp("", "", email, password)
                _user.value = user
                _registerState.value = AuthState.SUCCESS
            } catch (e: Exception) {
                _error.value = e.message
                _registerState.value = AuthState.ERROR
            }
        }
    }
    
    fun signUp(firstName: String, lastName: String, email: String, password: String) {
        viewModelScope.launch {
            try {
                _registerState.value = AuthState.LOADING
                _error.value = null
                val user = authService.signUp(firstName, lastName, email, password)
                _user.value = user
                _registerState.value = AuthState.SUCCESS
            } catch (e: Exception) {
                _error.value = e.message
                _registerState.value = AuthState.ERROR
            }
        }
    }
    
    fun login(email: String, password: String) {
        viewModelScope.launch {
            try {
                _loginState.value = AuthState.LOADING
                _error.value = null
                val user = authService.login(email, password)
                _user.value = user
                _loginState.value = AuthState.SUCCESS
            } catch (e: Exception) {
                _error.value = e.message
                _loginState.value = AuthState.ERROR
            }
        }
    }
    
    fun logout() {
        viewModelScope.launch {
            authService.logout()
            _user.value = null
            _loginState.value = AuthState.IDLE
            _registerState.value = AuthState.IDLE
        }
    }
    
    fun clearError() {
        _error.value = null
        _loginState.value = AuthState.IDLE
        _registerState.value = AuthState.IDLE
        _updateProfileState.value = AuthState.IDLE
        _updatePasswordState.value = AuthState.IDLE
        _deleteAccountState.value = AuthState.IDLE
        _resetPasswordState.value = AuthState.IDLE
    }
    
    fun updateUserProfile(name: String, email: String, gender: String) {
        viewModelScope.launch {
            try {
                _updateProfileState.value = AuthState.LOADING
                _error.value = null
                val updatedUser = authService.updateUserProfile(name, email, gender)
                _user.value = updatedUser
                _updateProfileState.value = AuthState.SUCCESS
            } catch (e: Exception) {
                _error.value = e.message
                _updateProfileState.value = AuthState.ERROR
            }
        }
    }
    
    fun updatePassword(currentPassword: String, newPassword: String) {
        viewModelScope.launch {
            try {
                _updatePasswordState.value = AuthState.LOADING
                _error.value = null
                val success = authService.updatePassword(currentPassword, newPassword)
                if (success) {
                    _updatePasswordState.value = AuthState.SUCCESS
                } else {
                    _error.value = "Failed to update password"
                    _updatePasswordState.value = AuthState.ERROR
                }
            } catch (e: Exception) {
                _error.value = e.message
                _updatePasswordState.value = AuthState.ERROR
            }
        }
    }
    
    fun deleteAccount() {
        viewModelScope.launch {
            try {
                _deleteAccountState.value = AuthState.LOADING
                _error.value = null
                val success = authService.deleteAccount()
                if (success) {
                    _user.value = null
                    _deleteAccountState.value = AuthState.SUCCESS
                } else {
                    _error.value = "Failed to delete account"
                    _deleteAccountState.value = AuthState.ERROR
                }
            } catch (e: Exception) {
                _error.value = e.message
                _deleteAccountState.value = AuthState.ERROR
            }
        }
    }

    fun resetPassword(email: String) {
        viewModelScope.launch {
            try {
                _resetPasswordState.value = AuthState.LOADING
                _error.value = null
                val success = authService.resetPassword(email)
                if (success) {
                    _resetPasswordState.value = AuthState.SUCCESS
                } else {
                    _error.value = "Failed to send password reset email"
                    _resetPasswordState.value = AuthState.ERROR
                }
            } catch (e: Exception) {
                _error.value = e.message
                _resetPasswordState.value = AuthState.ERROR
            }
        }
    }
    
    fun toggleDarkMode() {
        _isDarkMode.value = !_isDarkMode.value
    }
    
    class Factory(private val context: Context) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(AuthViewModel::class.java)) {
                return AuthViewModel(context) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
} 
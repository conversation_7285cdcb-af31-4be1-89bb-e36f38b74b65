package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.config.SupabaseConfig
import com.taskiq.app.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Date

/**
 * HTTP-based Supabase service for TaskIQ app
 * Uses direct HTTP calls to Supabase REST API
 */
class SupabaseHttpService(private val context: Context) {

    companion object {
        private const val TAG = "SupabaseHttpService"
    }

    private val json = Json { ignoreUnknownKeys = true }
    private val client = OkHttpClient()

    @Serializable
    data class SupabaseTask(
        val id: String,
        val user_id: String,
        val title: String,
        val description: String? = null,
        val is_completed: Boolean = false,
        val priority: String = "MEDIUM",
        val due_date: String? = null,
        val subtasks: String = "[]",
        val created_at: String? = null,
        val updated_at: String? = null
    )

    @Serializable
    data class SupabaseBill(
        val id: String,
        val user_id: String,
        val title: String,
        val amount: Double,
        val due_date: String,
        val type: String,
        val description: String? = null,
        val status: String = "PENDING",
        val created_at: String? = null,
        val updated_at: String? = null
    )

    @Serializable
    data class SupabaseUser(
        val id: String,
        val email: String,
        val first_name: String? = null,
        val last_name: String? = null,
        val created_at: String? = null,
        val updated_at: String? = null
    )

    @Serializable
    data class AuthResponse(
        val access_token: String? = null,
        val user: AuthUser? = null,
        val error: String? = null
    )

    @Serializable
    data class AuthUser(
        val id: String,
        val email: String
    )

    // Task operations
    suspend fun addTask(task: Task): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val supabaseTask = SupabaseTask(
                    id = task.id,
                    user_id = userId,
                    title = task.title,
                    description = task.description,
                    is_completed = task.isCompleted,
                    priority = task.priority.name,
                    due_date = task.dueDate?.toString(),
                    subtasks = Json.encodeToString(task.subtasks.map { it.title })
                )

                val response = makeHttpRequest(
                    endpoint = "tasks",
                    method = "POST",
                    body = json.encodeToString(supabaseTask)
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to add task: ${e.message}", e)
                false
            }
        }
    }

    suspend fun getTasks(): List<Task> {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext emptyList()
                }

                val response = makeHttpRequest(
                    endpoint = "tasks?user_id=eq.$userId",
                    method = "GET"
                )

                if (response != null) {
                    val supabaseTasks = json.decodeFromString<List<SupabaseTask>>(response)
                    supabaseTasks.map { supabaseTask ->
                        Task(
                            id = supabaseTask.id,
                            title = supabaseTask.title,
                            description = supabaseTask.description ?: "",
                            isCompleted = supabaseTask.is_completed,
                            priority = try { TaskPriority.valueOf(supabaseTask.priority) } catch (e: Exception) { TaskPriority.MEDIUM },
                            dueDate = supabaseTask.due_date?.let {
                                try {
                                    val localDate = LocalDate.parse(it)
                                    Date.from(localDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant())
                                } catch (e: Exception) { null }
                            },
                            subtasks = try {
                                json.decodeFromString<List<String>>(supabaseTask.subtasks).map {
                                    Subtask(title = it, isCompleted = false)
                                }
                            } catch (e: Exception) { emptyList() },
                            userId = supabaseTask.user_id
                        )
                    }
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get tasks: ${e.message}", e)
                emptyList()
            }
        }
    }

    suspend fun updateTask(task: Task): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val supabaseTask = SupabaseTask(
                    id = task.id,
                    user_id = userId,
                    title = task.title,
                    description = task.description,
                    is_completed = task.isCompleted,
                    priority = task.priority.name,
                    due_date = task.dueDate?.toString(),
                    subtasks = Json.encodeToString(task.subtasks.map { it.title })
                )

                val response = makeHttpRequest(
                    endpoint = "tasks?id=eq.${task.id}&user_id=eq.$userId",
                    method = "PUT",
                    body = json.encodeToString(supabaseTask)
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update task: ${e.message}", e)
                false
            }
        }
    }

    suspend fun deleteTask(taskId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val response = makeHttpRequest(
                    endpoint = "tasks?id=eq.$taskId&user_id=eq.$userId",
                    method = "DELETE"
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete task: ${e.message}", e)
                false
            }
        }
    }

    // Bill operations
    suspend fun addBill(bill: Bill): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val supabaseBill = SupabaseBill(
                    id = bill.id,
                    user_id = userId,
                    title = bill.title,
                    amount = bill.amount,
                    due_date = bill.dueDate.toString(),
                    type = bill.type.name,
                    description = bill.description,
                    status = bill.status.name
                )

                val response = makeHttpRequest(
                    endpoint = "bills",
                    method = "POST",
                    body = json.encodeToString(supabaseBill)
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to add bill: ${e.message}", e)
                false
            }
        }
    }

    suspend fun getBills(): List<Bill> {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext emptyList()
                }

                val response = makeHttpRequest(
                    endpoint = "bills?user_id=eq.$userId",
                    method = "GET"
                )

                if (response != null) {
                    val supabaseBills = json.decodeFromString<List<SupabaseBill>>(response)
                    supabaseBills.map { supabaseBill ->
                        Bill(
                            id = supabaseBill.id,
                            title = supabaseBill.title,
                            amount = supabaseBill.amount,
                            dueDate = LocalDate.parse(supabaseBill.due_date),
                            type = try { BillType.valueOf(supabaseBill.type) } catch (e: Exception) { BillType.OTHER },
                            description = supabaseBill.description ?: "",
                            status = try { BillStatus.valueOf(supabaseBill.status) } catch (e: Exception) { BillStatus.PENDING },
                            userId = supabaseBill.user_id,
                            createdAt = LocalDate.parse(supabaseBill.due_date)
                        )
                    }
                } else {
                    emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get bills: ${e.message}", e)
                emptyList()
            }
        }
    }

    suspend fun updateBill(bill: Bill): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val supabaseBill = SupabaseBill(
                    id = bill.id,
                    user_id = userId,
                    title = bill.title,
                    amount = bill.amount,
                    due_date = bill.dueDate.toString(),
                    type = bill.type.name,
                    description = bill.description,
                    status = bill.status.name
                )

                val response = makeHttpRequest(
                    endpoint = "bills?id=eq.${bill.id}&user_id=eq.$userId",
                    method = "PUT",
                    body = json.encodeToString(supabaseBill)
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update bill: ${e.message}", e)
                false
            }
        }
    }

    suspend fun deleteBill(billId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val userId = getCurrentUserId()
                if (userId == null) {
                    Log.e(TAG, "User not authenticated")
                    return@withContext false
                }

                val response = makeHttpRequest(
                    endpoint = "bills?id=eq.$billId&user_id=eq.$userId",
                    method = "DELETE"
                )

                response != null
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete bill: ${e.message}", e)
                false
            }
        }
    }

    // Authentication operations
    suspend fun signUp(firstName: String, lastName: String, email: String, password: String): User? {
        return withContext(Dispatchers.IO) {
            try {
                val authData = mapOf(
                    "email" to email,
                    "password" to password
                )

                val response = makeHttpRequest(
                    endpoint = "auth/v1/signup",
                    method = "POST",
                    body = json.encodeToString(authData),
                    isAuth = true
                )

                if (response != null) {
                    val authResponse = json.decodeFromString<AuthResponse>(response)
                    if (authResponse.user != null) {
                        // Create user profile
                        val userProfile = SupabaseUser(
                            id = authResponse.user.id,
                            email = email,
                            first_name = firstName,
                            last_name = lastName
                        )
                        
                        makeHttpRequest(
                            endpoint = "users",
                            method = "POST",
                            body = json.encodeToString(userProfile)
                        )

                        User(
                            firstName = firstName,
                            lastName = lastName,
                            email = email,
                            isLoggedIn = true
                        )
                    } else {
                        null
                    }
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to sign up: ${e.message}", e)
                null
            }
        }
    }

    suspend fun login(email: String, password: String): User? {
        return withContext(Dispatchers.IO) {
            try {
                val authData = mapOf(
                    "email" to email,
                    "password" to password
                )

                val response = makeHttpRequest(
                    endpoint = "auth/v1/token?grant_type=password",
                    method = "POST",
                    body = json.encodeToString(authData),
                    isAuth = true
                )

                if (response != null) {
                    val authResponse = json.decodeFromString<AuthResponse>(response)
                    if (authResponse.user != null) {
                        // Get user profile
                        val profileResponse = makeHttpRequest(
                            endpoint = "users?id=eq.${authResponse.user.id}",
                            method = "GET"
                        )
                        
                        if (profileResponse != null) {
                            val userProfiles = json.decodeFromString<List<SupabaseUser>>(profileResponse)
                            val userProfile = userProfiles.firstOrNull()
                            
                            User(
                                firstName = userProfile?.first_name ?: "",
                                lastName = userProfile?.last_name ?: "",
                                email = email,
                                isLoggedIn = true
                            )
                        } else {
                            User(
                                firstName = "",
                                lastName = "",
                                email = email,
                                isLoggedIn = true
                            )
                        }
                    } else {
                        null
                    }
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to login: ${e.message}", e)
                null
            }
        }
    }

    // Helper methods
    private suspend fun getCurrentUserId(): String? {
        // For now, return a stub user ID
        // In a real implementation, this would get the current authenticated user ID
        return "stub_user_id"
    }

    private suspend fun makeHttpRequest(
        endpoint: String,
        method: String,
        body: String? = null,
        isAuth: Boolean = false
    ): String? {
        return withContext(Dispatchers.IO) {
            try {
                val baseUrl = if (isAuth) {
                    SupabaseConfig.SUPABASE_URL
                } else {
                    "${SupabaseConfig.SUPABASE_URL}/rest/v1"
                }

                val requestBuilder = Request.Builder()
                    .url("$baseUrl/$endpoint")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("apikey", SupabaseConfig.SUPABASE_ANON_KEY)
                    .addHeader("Authorization", "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}")

                when (method.uppercase()) {
                    "GET" -> requestBuilder.get()
                    "POST" -> {
                        val requestBody = body?.toRequestBody("application/json".toMediaType())
                            ?: "".toRequestBody("application/json".toMediaType())
                        requestBuilder.post(requestBody)
                    }
                    "PUT" -> {
                        val requestBody = body?.toRequestBody("application/json".toMediaType())
                            ?: "".toRequestBody("application/json".toMediaType())
                        requestBuilder.put(requestBody)
                    }
                    "DELETE" -> requestBuilder.delete()
                }

                val request = requestBuilder.build()
                val response = client.newCall(request).execute()

                if (response.isSuccessful) {
                    response.body?.string()
                } else {
                    Log.e(TAG, "HTTP request failed with code: ${response.code}")
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "HTTP request failed: ${e.message}", e)
                null
            }
        }
    }
}

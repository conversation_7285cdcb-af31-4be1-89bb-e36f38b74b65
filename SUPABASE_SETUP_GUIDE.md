# Supabase Database Setup Guide for TaskIQ

## Step 1: Create Tables in Supabase

1. **Open your Supabase project dashboard**
2. **Go to SQL Editor** (in the left sidebar)
3. **Copy and paste the following SQL commands** to create all required tables:

**Note**: This SQL is safe to run multiple times - it will drop and recreate policies/triggers if they already exist.

```sql
-- Enable Row Level Security (RLS) for all tables
-- This ensures users can only access their own data

-- 1. Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    gender TEXT CHECK (gender IN ('Male', 'Female', 'Other', 'Select')) DEFAULT 'Select',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Users can only see and modify their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 2. Tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    is_completed BOOLEAN DEFAULT FALSE,
    priority TEXT CHECK (priority IN ('HIGH', 'MEDIUM', 'LOW')) DEFAULT 'MEDIUM',
    category TEXT,
    due_date DATE,
    has_no_due_date BOOLEAN DEFAULT FALSE,
    subtasks JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on tasks table
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Users can only access their own tasks
CREATE POLICY "Users can view own tasks" ON public.tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tasks" ON public.tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks" ON public.tasks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks" ON public.tasks
    FOR DELETE USING (auth.uid() = user_id);

-- 3. Bills table
CREATE TABLE IF NOT EXISTS public.bills (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    status TEXT CHECK (status IN ('PENDING', 'PAID', 'OVERDUE')) DEFAULT 'PENDING',
    type TEXT CHECK (type IN ('CREDIT_CARD', 'LOAN_EMI', 'UTILITY', 'OTHER')) DEFAULT 'OTHER',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on bills table
ALTER TABLE public.bills ENABLE ROW LEVEL SECURITY;

-- Users can only access their own bills
CREATE POLICY "Users can view own bills" ON public.bills
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own bills" ON public.bills
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own bills" ON public.bills
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own bills" ON public.bills
    FOR DELETE USING (auth.uid() = user_id);

-- 4. Important dates table
CREATE TABLE IF NOT EXISTS public.important_dates (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_type TEXT CHECK (recurring_type IN ('YEARLY', 'MONTHLY', 'WEEKLY')),
    subtasks JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on important_dates table
ALTER TABLE public.important_dates ENABLE ROW LEVEL SECURITY;

-- Users can only access their own important dates
CREATE POLICY "Users can view own important dates" ON public.important_dates
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own important dates" ON public.important_dates
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own important dates" ON public.important_dates
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own important dates" ON public.important_dates
    FOR DELETE USING (auth.uid() = user_id);

-- 5. Linked emails table
CREATE TABLE IF NOT EXISTS public.linked_emails (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    email TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, email)
);

-- Enable RLS on linked_emails table
ALTER TABLE public.linked_emails ENABLE ROW LEVEL SECURITY;

-- Users can only access their own linked emails
CREATE POLICY "Users can view own linked emails" ON public.linked_emails
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own linked emails" ON public.linked_emails
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own linked emails" ON public.linked_emails
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own linked emails" ON public.linked_emails
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON public.tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON public.tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_is_completed ON public.tasks(is_completed);

CREATE INDEX IF NOT EXISTS idx_bills_user_id ON public.bills(user_id);
CREATE INDEX IF NOT EXISTS idx_bills_due_date ON public.bills(due_date);
CREATE INDEX IF NOT EXISTS idx_bills_status ON public.bills(status);

CREATE INDEX IF NOT EXISTS idx_important_dates_user_id ON public.important_dates(user_id);
CREATE INDEX IF NOT EXISTS idx_important_dates_date ON public.important_dates(date);

CREATE INDEX IF NOT EXISTS idx_linked_emails_user_id ON public.linked_emails(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bills_updated_at BEFORE UPDATE ON public.bills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_important_dates_updated_at BEFORE UPDATE ON public.important_dates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_linked_emails_updated_at BEFORE UPDATE ON public.linked_emails
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

4. **Click "Run" to execute the SQL**

## Step 2: Verify Tables Created

1. Go to **Table Editor** in your Supabase dashboard
2. You should see these tables:
   - `users` (with gender column)
   - `tasks`
   - `bills`
   - `important_dates`
   - `linked_emails`

## Step 3: Test the App

1. **Uninstall and reinstall the app** to test fresh installation
2. **Create a new account** or **login with existing account**
3. **Add some tasks, bills, and important dates**
4. **Update your gender** in the profile screen
5. **Reinstall the app again** - your data should now sync from Supabase

## What's Fixed

✅ **Gender Column**: Added to users table with proper constraints
✅ **Different Gender Icons**: 
   - Male: Person icon
   - Female: AccountBox icon  
   - Other: PersonAdd icon
✅ **Data Sync**: App now properly loads data from Supabase after reinstall
✅ **Important Dates**: Now stored in dedicated `important_dates` table
✅ **Cross-Device Sync**: All data syncs across devices via Supabase

## Troubleshooting

If data still doesn't sync:
1. Check Supabase logs in your dashboard
2. Ensure all tables were created successfully
3. Verify your Supabase credentials are correct
4. Check app logs for any error messages

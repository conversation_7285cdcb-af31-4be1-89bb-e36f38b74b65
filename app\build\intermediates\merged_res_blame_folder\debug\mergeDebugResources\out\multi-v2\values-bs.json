{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3153,3251,3353,3451,3555,3659,3761,15863", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3246,3348,3446,3550,3654,3756,3873,15959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,15379", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,15461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2830,2941", "endColumns": "110,121", "endOffsets": "2936,3058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4067,4173,4333,4459,4569,4719,4845,4957,5200,5354,5461,5622,5749,5899,6045,6113,6175", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "4168,4328,4454,4564,4714,4840,4952,5054,5349,5456,5617,5744,5894,6040,6108,6170,6255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8598,8717,8837,8958,9078,9177,9275,9390,9535,9655,9793,9878,9978,10071,10169,10286,10413,10518,10653,10787,10928,11098,11233,11356,11483,11611,11705,11803,11924,12052,12149,12252,12361,12500,12645,12754,12854,12939,13032,13127,13254,13348,13439,13548,13636,13719,13816,13920,14013,14110,14198,14306,14403,14505,14643,14733,14841", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "8712,8832,8953,9073,9172,9270,9385,9530,9650,9788,9873,9973,10066,10164,10281,10408,10513,10648,10782,10923,11093,11228,11351,11478,11606,11700,11798,11919,12047,12144,12247,12356,12495,12640,12749,12849,12934,13027,13122,13249,13343,13434,13543,13631,13714,13811,13915,14008,14105,14193,14301,14398,14500,14638,14728,14836,14935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5059", "endColumns": "140", "endOffsets": "5195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cdb44a9a24015f2bfec4b4de89ce351f\\transformed\\browser-1.4.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6369,6845,6945,7059", "endColumns": "103,99,113,99", "endOffsets": "6468,6940,7054,7154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,380,512,643,771,901,1035,1136,1271,1404", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "159,252,375,507,638,766,896,1030,1131,1266,1399,1522"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6260,6567,7159,7282,7414,7545,7673,7803,7937,8038,8173,8306", "endColumns": "108,92,122,131,130,127,129,133,100,134,132,122", "endOffsets": "6364,6655,7277,7409,7540,7668,7798,7932,8033,8168,8301,8424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3063,16232,16322", "endColumns": "89,89,91", "endOffsets": "3148,16317,16409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,297,385,479,578,664,741,833,925,1010,1091,1177,1250,1341,1418,1497,1574,1654,1724", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "292,380,474,573,659,736,828,920,1005,1086,1172,1245,1336,1413,1492,1569,1649,1719,1837"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3878,3979,6473,6660,6759,8429,8506,15035,15127,15212,15293,15466,15539,15630,15707,15786,15964,16044,16114", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "3974,4062,6562,6754,6840,8501,8593,15122,15207,15288,15374,15534,15625,15702,15781,15858,16039,16109,16227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14940", "endColumns": "94", "endOffsets": "15030"}}]}]}
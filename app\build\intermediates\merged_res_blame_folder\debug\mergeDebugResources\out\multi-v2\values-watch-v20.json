{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-watch-v20/values-watch-v20.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f1cd8a7007882d810a07036b3356013\\transformed\\play-services-auth-21.3.0\\res\\values-watch-v20\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11", "startColumns": "0,0,0,0,0,0,0,0", "startOffsets": "173,248,324,399,474,561,638,725", "endColumns": "74,75,74,74,86,76,86,75", "endOffsets": "247,323,398,473,560,637,724,800"}, "to": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,134,214,293,372,463,544,635", "endColumns": "78,79,78,78,90,80,90,79", "endOffsets": "129,209,288,367,458,539,630,710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}, "to": {"startLines": "10,13,16", "startColumns": "4,4,4", "startOffsets": "715,874,1045", "endLines": "12,15,18", "endColumns": "12,12,12", "endOffsets": "869,1040,1213"}}]}]}
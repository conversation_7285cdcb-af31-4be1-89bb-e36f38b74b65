{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,87", "endOffsets": "137,225,313"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3103,16436,16524", "endColumns": "86,87,87", "endOffsets": "3185,16519,16607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,286,370,468,573,668,745,836,923,1007,1093,1181,1256,1344,1421,1498,1573,1653,1736", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,87,76,76,74,79,82,121", "endOffsets": "281,365,463,568,663,740,831,918,1002,1088,1176,1251,1339,1416,1493,1568,1648,1731,1853"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3941,4034,6601,6791,6896,8638,8715,15229,15316,15400,15486,15658,15733,15821,15898,15975,16151,16231,16314", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,87,76,76,74,79,82,121", "endOffsets": "4029,4113,6694,6891,6986,8710,8801,15311,15395,15481,15569,15728,15816,15893,15970,16045,16226,16309,16431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1ba429aa6de3be7de3bb6e415af4e51\\transformed\\play-services-basement-18.4.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5124", "endColumns": "158", "endOffsets": "5278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6384,6699,7316,7442,7583,7722,7852,7984,8121,8218,8373,8516", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "6491,6786,7437,7578,7717,7847,7979,8116,8213,8368,8511,8633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3190,3288,3398,3497,3600,3711,3821,16050", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3283,3393,3492,3595,3706,3816,3936,16146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4709,4797,4887,4998,5078,5165,5265,5374,5470,5569,5657,5768,5864,5964,6102,6186,6289", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4704,4792,4882,4993,5073,5160,5260,5369,5465,5564,5652,5763,5859,5959,6097,6181,6284,6381"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8806,8925,9045,9162,9280,9381,9475,9586,9718,9834,9978,10062,10161,10257,10356,10481,10599,10703,10842,10977,11116,11312,11442,11560,11686,11813,11910,12011,12133,12262,12360,12463,12570,12708,12856,12965,13069,13153,13249,13345,13460,13548,13638,13749,13829,13916,14016,14125,14221,14320,14408,14519,14615,14715,14853,14937,15040", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "8920,9040,9157,9275,9376,9470,9581,9713,9829,9973,10057,10156,10252,10351,10476,10594,10698,10837,10972,11111,11307,11437,11555,11681,11808,11905,12006,12128,12257,12355,12458,12565,12703,12851,12960,13064,13148,13244,13340,13455,13543,13633,13744,13824,13911,14011,14120,14216,14315,14403,14514,14610,14710,14848,14932,15035,15132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61468bcbedfc450e580f06e70db30d2d\\transformed\\play-services-base-18.5.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4118,4225,4377,4509,4616,4769,4899,5018,5283,5449,5558,5723,5857,6010,6160,6228,6294", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "4220,4372,4504,4611,4764,4894,5013,5119,5444,5553,5718,5852,6005,6155,6223,6289,6379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "15137", "endColumns": "91", "endOffsets": "15224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,15574", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,15653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,113", "endOffsets": "165,279"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2874,2989", "endColumns": "114,113", "endOffsets": "2984,3098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6496,6991,7096,7210", "endColumns": "104,104,113,105", "endOffsets": "6596,7091,7205,7311"}}]}]}
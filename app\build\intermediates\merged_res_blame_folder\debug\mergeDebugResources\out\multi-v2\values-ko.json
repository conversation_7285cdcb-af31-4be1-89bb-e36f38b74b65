{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,13863", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,13937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3807,3911,4050,4166,4268,4383,4500,4607,4823,4968,5071,5207,5325,5443,5562,5621,5679", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "3906,4045,4161,4263,4378,4495,4602,4697,4963,5066,5202,5320,5438,5557,5616,5674,5749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4143,4226,4308,4403,4483,4565,4662,4757,4852,4949,5032,5128,5222,5320,5437,5517,5611", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4138,4221,4303,4398,4478,4560,4657,4752,4847,4944,5027,5123,5217,5315,5432,5512,5606,5697"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7824,7929,8033,8139,8243,8335,8424,8530,8649,8759,8881,8963,9060,9145,9235,9344,9458,9560,9673,9784,9896,10029,10138,10242,10349,10458,10544,10639,10748,10857,10948,11046,11143,11257,11376,11475,11567,11641,11730,11818,11912,11995,12077,12172,12252,12334,12431,12526,12621,12718,12801,12897,12991,13089,13206,13286,13380", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "7924,8028,8134,8238,8330,8419,8525,8644,8754,8876,8958,9055,9140,9230,9339,9453,9555,9668,9779,9891,10024,10133,10237,10344,10453,10539,10634,10743,10852,10943,11041,11138,11252,11371,11470,11562,11636,11725,11813,11907,11990,12072,12167,12247,12329,12426,12521,12616,12713,12796,12892,12986,13084,13201,13281,13375,13466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,109", "endOffsets": "158,268"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2685,2793", "endColumns": "107,109", "endOffsets": "2788,2898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,242,346,463,571,687,793,906,1000,1120,1234", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "151,237,341,458,566,682,788,901,995,1115,1229,1332"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5754,6029,6575,6679,6796,6904,7020,7126,7239,7333,7453,7567", "endColumns": "100,85,103,116,107,115,105,112,93,119,113,102", "endOffsets": "5850,6110,6674,6791,6899,7015,7121,7234,7328,7448,7562,7665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0aa768bf2c77d0f2a11c0fea0a786\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "5855,6285,6378,6481", "endColumns": "85,92,102,93", "endOffsets": "5936,6373,6476,6570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4702", "endColumns": "120", "endOffsets": "4818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2975,3067,3167,3261,3358,3454,3552,14311", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3062,3162,3256,3353,3449,3547,3647,14407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "13471", "endColumns": "84", "endOffsets": "13551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,209", "endColumns": "71,81,78", "endOffsets": "122,204,283"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "2903,14664,14746", "endColumns": "71,81,78", "endOffsets": "2970,14741,14820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,257,333,421,511,591,666,745,824,903,976,1052,1120,1201,1277,1351,1421,1495,1559", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "252,328,416,506,586,661,740,819,898,971,1047,1115,1196,1272,1346,1416,1490,1554,1668"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3652,3731,5941,6115,6205,7670,7745,13556,13635,13714,13787,13942,14010,14091,14167,14241,14412,14486,14550", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "3726,3802,6024,6200,6280,7740,7819,13630,13709,13782,13858,14005,14086,14162,14236,14306,14481,14545,14659"}}]}]}
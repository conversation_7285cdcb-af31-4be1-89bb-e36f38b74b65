package com.taskiq.app.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Camera
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PhotoCamera
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.PopupProperties
import androidx.navigation.NavController
import com.taskiq.app.ui.components.AuthButton
import com.taskiq.app.ui.components.AuthTextField
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalLightBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.viewmodel.AuthViewModel
import kotlinx.coroutines.launch
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.HorizontalDivider
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.ui.window.Dialog
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.theme.contentModifier
import com.taskiq.app.ui.theme.fullWidthContentModifier
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.contentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing
import com.taskiq.app.ui.theme.responsiveTextSize
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveButtonHeight
import com.taskiq.app.ui.theme.responsiveCornerRadius
import com.taskiq.app.ui.theme.textFieldColors
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyProfileScreen(
    navController: NavController,
    viewModel: AuthViewModel
) {
    val currentUser by viewModel.user.collectAsState()
    val updateProfileState by viewModel.updateProfileState.collectAsState()
    val error by viewModel.error.collectAsState()
    
    // Form states
    var firstName by remember { mutableStateOf(currentUser?.firstName ?: "") }
    var lastName by remember { mutableStateOf(currentUser?.lastName ?: "") }
    var name by remember { mutableStateOf("${currentUser?.firstName ?: ""} ${currentUser?.lastName ?: ""}".trim()) }
    var gender by remember { mutableStateOf(currentUser?.gender ?: "Select") }
    var email by remember { mutableStateOf(currentUser?.email ?: "") }
    var password by remember { mutableStateOf("••••••••") } // Placeholder for password
    
    // UI state
    var passwordVisible by remember { mutableStateOf(false) }
    var genderExpanded by remember { mutableStateOf(false) }
    
    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    
    // Show success message
    LaunchedEffect(updateProfileState) {
        if (updateProfileState == AuthViewModel.AuthState.SUCCESS) {
            snackbarHostState.showSnackbar("Profile updated successfully")
            viewModel.clearError()
        }
    }
    
    Scaffold(
        snackbarHost = {
            SnackbarHost(
                hostState = snackbarHostState,
                modifier = Modifier
                    .navigationBarsPadding()
                    .padding(horizontal = uniformHorizontalPadding())
                    .padding(bottom = uniformSectionSpacing())
            ) { snackbarData ->
                // Custom transparent snackbar with center-aligned text
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.sdp()),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    ),
                    border = BorderStroke(1.sdp(), ProfessionalBlue.copy(alpha = 0.3f))
                ) {
                    Text(
                        text = snackbarData.visuals.message,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(uniformSectionSpacing()),
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Medium,
                        fontSize = responsiveTextSize(),
                        color = ProfessionalBlue
                    )
                }
            }
        },
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "My Profile",
                        color = ProfessionalWhite,
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.ssp()
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier.padding(end = uniformSmallSpacing())
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = ProfessionalWhite
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = uniformHorizontalPadding())
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Profile icon at the top
            Box(
                modifier = Modifier
                    .padding(top = uniformLargeSpacing(), bottom = uniformSectionSpacing()),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(responsiveIconSize() * 5)
                        .clip(CircleShape)
                        .background(ProfessionalLightBlue.copy(alpha = 0.3f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Person,
                        contentDescription = "Profile (${gender})",
                        modifier = Modifier.size(60.sdp()),
                        tint = when (gender) {
                            "Male" -> ProfessionalBlue
                            "Female" -> Color(0xFFE91E63) // Pink for female
                            "Other" -> Color(0xFF9C27B0) // Purple for other
                            else -> ProfessionalBlue
                        }
                    )
                }
            }
            
            // Form fields without card background
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = uniformSectionSpacing()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Name field
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSmallSpacing()),
                    label = { Text("Full Name") },
                    leadingIcon = { 
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Name",
                            tint = ProfessionalBlue
                        )
                    },
                    singleLine = true,
                    shape = RoundedCornerShape(8.sdp()),
                    colors = textFieldColors()
                )
                
                // Gender field with dropdown
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSmallSpacing())
                ) {
                    OutlinedTextField(
                        value = gender,
                        onValueChange = {},
                        readOnly = true,
                        modifier = Modifier.fillMaxWidth(),
                        label = { Text("Gender") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Gender",
                                tint = ProfessionalBlue
                            )
                        },
                        trailingIcon = { 
                            IconButton(onClick = { genderExpanded = !genderExpanded }) {
                                Icon(
                                    imageVector = if (genderExpanded) 
                                        Icons.Filled.KeyboardArrowUp 
                                    else 
                                        Icons.Filled.KeyboardArrowDown,
                                    contentDescription = "Toggle Gender Dropdown",
                                    tint = ProfessionalBlue
                                )
                            }
                        },
                        shape = RoundedCornerShape(8.sdp()),
                        colors = textFieldColors()
                    )
                    
                    // Floating dropdown menu
                    DropdownMenu(
                        expanded = genderExpanded,
                        onDismissRequest = { genderExpanded = false },
                        modifier = Modifier
                            .width(IntrinsicSize.Min)
                            .background(MaterialTheme.colorScheme.surface)
                            .align(Alignment.BottomStart)
                    ) {
                        listOf("Male", "Female", "Other").forEach { option ->
                            DropdownMenuItem(
                                text = { Text(text = option) },
                                onClick = {
                                    gender = option
                                    genderExpanded = false
                                }
                            )
                            
                            if (option != "Other") {
                                HorizontalDivider(color = Color.LightGray, thickness = 1.sdp())
                            }
                        }
                    }
                }
                
                // Email field
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSmallSpacing()),
                    label = { Text("Email Address") },
                    leadingIcon = { 
                        Icon(
                            imageVector = Icons.Default.Email,
                            contentDescription = "Email",
                            tint = ProfessionalBlue
                        )
                    },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Email
                    ),
                    shape = RoundedCornerShape(8.sdp()),
                    colors = textFieldColors()
                )
                
                // Password field (read-only, just for display)
                OutlinedTextField(
                    value = password,
                    onValueChange = { },
                    readOnly = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSmallSpacing()),
                    label = { Text("Password") },
                    leadingIcon = { 
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Password",
                            tint = ProfessionalBlue
                        )
                    },
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) 
                                    Icons.Default.VisibilityOff 
                                else 
                                    Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "Hide Password" else "Show Password",
                                tint = ProfessionalBlue
                            )
                        }
                    },
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    singleLine = true,
                    shape = RoundedCornerShape(8.sdp()),
                    colors = textFieldColors()
                )
                
                // Save button
                Button(
                    onClick = {
                        viewModel.updateUserProfile(name, email, gender)
                    },
                    enabled = name.isNotEmpty() && email.isNotEmpty() && 
                             updateProfileState != AuthViewModel.AuthState.LOADING,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(responsiveButtonHeight())
                        .padding(vertical = uniformSmallSpacing()),
                    shape = RoundedCornerShape(responsiveCornerRadius()),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ProfessionalBlue
                    )
                ) {
                    Text(
                        text = if (updateProfileState == AuthViewModel.AuthState.LOADING) 
                            "Updating..." 
                        else 
                            "Save Changes",
                        fontSize = 16.sp
                    )
                }
            }
            
            // Error message
            if (error != null) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(uniformSectionSpacing()),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    ),
                    shape = RoundedCornerShape(responsiveCornerRadius())
                ) {
                    Text(
                        text = error ?: "",
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(uniformSectionSpacing())
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(uniformLargeSpacing()))
        }
    }
} 

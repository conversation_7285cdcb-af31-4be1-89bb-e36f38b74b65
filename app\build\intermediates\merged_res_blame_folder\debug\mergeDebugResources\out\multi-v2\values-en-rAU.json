{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,355,449,548,635,717,806,895,979,1057,1139,1212,1296,1372,1444,1514,1591,1657", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "268,350,444,543,630,712,801,890,974,1052,1134,1207,1291,1367,1439,1509,1586,1652,1773"}, "to": {"startLines": "39,40,43,45,46,60,61,120,121,122,123,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3782,3874,4160,4344,4443,6082,6164,12455,12544,12628,12706,12871,12944,13028,13104,13176,13347,13424,13490", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,83,75,71,69,76,65,120", "endOffsets": "3869,3951,4249,4438,4525,6159,6248,12539,12623,12701,12783,12939,13023,13099,13171,13241,13419,13485,13606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "12367", "endColumns": "87", "endOffsets": "12450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,84", "endOffsets": "125,211,296"}, "to": {"startLines": "31,134,135", "startColumns": "4,4,4", "startOffsets": "2988,13611,13697", "endColumns": "74,85,84", "endOffsets": "3058,13692,13777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4617,4704,4806,4888,4972,5073,5174,5274,5373,5461,5567,5668,5772,5892,5974,6074", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4612,4699,4801,4883,4967,5068,5169,5269,5368,5456,5562,5663,5767,5887,5969,6069,6164"}, "to": {"startLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6253,6371,6487,6598,6712,6811,6906,7018,7154,7270,7406,7490,7589,7680,7777,7896,8021,8125,8252,8375,8503,8664,8785,8901,9024,9149,9241,9339,9456,9580,9677,9779,9881,10011,10150,10256,10355,10433,10529,10623,10728,10815,10902,11004,11086,11170,11271,11372,11472,11571,11659,11765,11866,11970,12090,12172,12272", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "6366,6482,6593,6707,6806,6901,7013,7149,7265,7401,7485,7584,7675,7772,7891,8016,8120,8247,8370,8498,8659,8780,8896,9019,9144,9236,9334,9451,9575,9672,9774,9876,10006,10145,10251,10350,10428,10524,10618,10723,10810,10897,10999,11081,11165,11266,11367,11467,11566,11654,11760,11861,11965,12085,12167,12267,12362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,12788", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,12866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "32,33,34,35,36,37,38,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3063,3159,3261,3360,3459,3563,3666,13246", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3154,3256,3355,3454,3558,3661,3777,13342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "42,47,48,49", "startColumns": "4,4,4,4", "startOffsets": "4062,4530,4627,4736", "endColumns": "97,96,108,98", "endOffsets": "4155,4622,4731,4830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2762,2874", "endColumns": "111,113", "endOffsets": "2869,2983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "41,44,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3956,4254,4835,4954,5083,5221,5348,5466,5597,5697,5823,5962", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4057,4339,4949,5078,5216,5343,5461,5592,5692,5818,5957,6077"}}]}]}
{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-77:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\718231422f48010e85ca6edf2e677575\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,232", "endColumns": "86,89,89", "endOffsets": "137,227,317"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3004,16067,16157", "endColumns": "86,89,89", "endOffsets": "3086,16152,16242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45f9a3db26e33e80ab043af5a0e43024\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6284,6769,6870,6979", "endColumns": "102,100,108,98", "endOffsets": "6382,6865,6974,7073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2989e2c91f6d0a9894b01384862e89f4\\transformed\\play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4997", "endColumns": "149", "endOffsets": "5142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11771762b22044a889273e9ed7a93127\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8537,8653,8766,8875,8989,9086,9187,9305,9442,9564,9716,9806,9902,10000,10102,10220,10343,10444,10576,10708,10837,11004,11126,11250,11377,11499,11598,11697,11818,11939,12042,12153,12261,12400,12544,12652,12758,12841,12939,13036,13149,13233,13318,13418,13498,13583,13680,13783,13880,13985,14075,14183,14286,14396,14514,14594,14699", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "8648,8761,8870,8984,9081,9182,9300,9437,9559,9711,9801,9897,9995,10097,10215,10338,10439,10571,10703,10832,10999,11121,11245,11372,11494,11593,11692,11813,11934,12037,12148,12256,12395,12539,12647,12753,12836,12934,13031,13144,13228,13313,13413,13493,13578,13675,13778,13875,13980,14070,14178,14281,14391,14509,14589,14694,14793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\75e92b145e13fc673dc76999f901f30d\\transformed\\credentials-1.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2775,2887", "endColumns": "111,116", "endOffsets": "2882,2999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55690b61ef7b0489a32a7ec346714862\\transformed\\core-1.16.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3091,3187,3289,3387,3492,3597,3709,15695", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3182,3284,3382,3487,3592,3704,3820,15791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72b42ec2c0a6db09976b88668f84c08b\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,287,372,471,572,661,738,831,922,1004,1085,1167,1239,1326,1402,1482,1556,1633,1705", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "282,367,466,567,656,733,826,917,999,1080,1162,1234,1321,1397,1477,1551,1628,1700,1822"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3825,3919,6387,6579,6680,8367,8444,14889,14980,15062,15143,15306,15378,15465,15541,15621,15796,15873,15945", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "3914,3999,6481,6675,6764,8439,8532,14975,15057,15138,15220,15373,15460,15536,15616,15690,15868,15940,16062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e866c3f6718b50411e7e0aa30260699c\\transformed\\play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4004,4115,4268,4399,4505,4648,4774,4890,5147,5288,5394,5543,5669,5817,5956,6022,6092", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "4110,4263,4394,4500,4643,4769,4885,4992,5283,5389,5538,5664,5812,5951,6017,6087,6170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\396ff811c0e00aef5e8ac116f6d99809\\transformed\\material-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14798", "endColumns": "90", "endOffsets": "14884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4dba44ce16815e024cb356c378b89b2b\\transformed\\appcompat-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,15225", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,15301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dff1d73b7cef632e787de4d59cae0ad8\\transformed\\biometric-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,257,378,515,648,797,918,1048,1148,1291,1429", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "159,252,373,510,643,792,913,1043,1143,1286,1424,1541"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6175,6486,7078,7199,7336,7469,7618,7739,7869,7969,8112,8250", "endColumns": "108,92,120,136,132,148,120,129,99,142,137,116", "endOffsets": "6279,6574,7194,7331,7464,7613,7734,7864,7964,8107,8245,8362"}}]}]}
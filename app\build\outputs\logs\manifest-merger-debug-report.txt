-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:2:1-76:12
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:2:1-76:12
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:2:1-76:12
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:2:1-76:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e79e31f8dad322d68c1a9d1a3b6f09f\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c51bdc67d0f2ca29b6af4c4c8d8827e\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c731d929a795de99145107971c186ce\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4239f854065c6cb76db75da03d84d7\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.36.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52dd6af57e481b458fdd606e969027e0\transformed\accompanist-swiperefresh-0.36.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\904e1630c563f33d0f5478f7716fee6a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed92ea3220a5bbd75c2f84d4c18dcd5\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be1cd8404036620e7b9e129b051e87f\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\432509cb614287948c66b4268de1f615\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0791b56ebd6f18e3246ab658967e42f\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99a8174784df102df178925cfbe08e23\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90efc6c90b6408b7c8e7c8fd5b36363\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34dc7db67f4194251fe043b8afec8c6\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\378fa87e62cc8a7e1cdd9fa9ffc7eb39\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa0ae4f33dca1ad6614307f9df736fe8\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77db0d61998be1d5683e5d85c3e051\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a391acb752c9da73f1f81df43e0170f\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c31efc59dea433690b1e4c284538cc\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:17:1-52:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e51290920b98c8dbaf60055e9a6f558\transformed\googleid-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764023a455144f62a86bfbce3d6749a4\transformed\credentials-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fb265d384d30d2dee5543d95a6ec4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd84777daae1699cab565fc4ecfceb0\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6511e9096ee7467b892ec3c75d51b348\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ffcdd7f913c99ccf89fb28bd20c182\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f54a6e2c946881b13764e54e3f3b18cc\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2d6103a37e79a7c1ef7d07d8a118ba\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764d0f7915ef8de4cba308e0c460a04a\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0372b3478d6e1886c1f6221833fcdd5c\transformed\play-services-fido-21.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9c9b0f6db1266a3ca39e2581fbabfa7\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb8529be0a3862c1b80f1622c3473de\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf0915ad8af68c9848003015b54f1279\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d95ba4be76e166e463f44c8187510faa\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2773c54afb7f0bd722fed5a6c4cddc\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb44a9a24015f2bfec4b4de89ce351f\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54b06ce0d85c848048ae92416efcb7f\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab01b5e3b8e2accd167ccfeb3d776bdb\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\699a73af6967cfd4c447542bec97d435\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0e1f6e3e1bcbf1b7ce8eeb43d6494c7\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4019a3ae73594c7825a7e94eaf189b16\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a9cc2c4f9d54affe434e92f0a3b5b93\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc68c58374a0e10bd9125599a8d8599b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a5cde69cfda992ab7d6b7ae4fa52b2\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea3ff6759af670f7ea5cd0e3229adf6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1333512a63fd7d24f46c7f9cc4f79c87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c0b4d92b3f43b1cae454b2d885a0d17\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\046fd92d85ea3b574c144dd221a5e727\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e2dd86a0ca09120e9dca087d303f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\370d43f945ed4b15ae2820bd61637a4e\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9014300a966b65571e483b4a895d94bf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\648d48d7a66ae26cff95c1357b99d181\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71ea35a3095c69e9eeeedffeeb9c50\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3281e5b192404400046aa197cab4882\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8580dcaca1b9cac1c3de7029e62def\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49aceb762336c9b20fffe88cba7687c4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c973c95ede6af205f7463ef434fee5c1\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc20826b07a3eca4be881e833dc2878\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\600f4ab1d85b7d3602fcaabdb66e1174\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5f033abbfe9f88fc8a846d0eb0fb83\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d9d1b63fdb25a5f9458cda713e57734\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8a65ac085891ef11657c59e001ed0cf\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a9706dab6c4eaa74acdde856f1912d\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1de57881c2537fefd04aeed1ab691f5a\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc4d4549228ea2b094f042592c6c45\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e19f2f90b02d9084bfe9d12f3eee99b8\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\909e192009fb7f337399eefac7faaf77\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2304a6600df2f5973a51ac1eea9544a\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bd519d2e243fd54142c626db14966a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e785f14f9db042ce793d07dfd6d648ed\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c84a4761139e2c94c4fb1f2712d07a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63697056f69d193aacd905c2ec4f8045\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3442f64d4112f819ba6cca64e4543a80\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\069925cd988b867290d3af19d695fe7f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cc6b6bc07239fc23c0902000437baea\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14eaf830e287797f1d03e4a3dbaedd4a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c278c9459f2d385329fbed6652307e\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabcd5ff92bff42a04134037b2e17511\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\723e37b713ef8fccee6943e50ec7b4c9\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dba852a1967de123ff2bff1abb41126a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36baa5a1a3568e4e888880ae9f77c7d9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b0f19fe26d54cc04e910eadffc49d8\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9810c06671ee72d2867c6f8db2672ddf\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78351a9202ab955e6373511c2847685c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4597693c94534caf5d822fe283bb9a0\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0d20b21e1a9ec0752fda4a68b5b5f8\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f965f7972ec3c08b2726cef08d0245\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a770640834ed1359b8c48c6a63bc6b1\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f38e0fe19a2db3f4a293435fa37a1c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fef0f6a477bc1bf2debe298d53188bbb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baee743243569936044c57955d951281\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\870192ab2f97875bbb966198050a5174\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51046fa3d79b35e15d8e0b0c0d2b7ca9\transformed\sdp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a337fbe199d836b436436c01fe4e1d7\transformed\ssp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:22-74
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:22-71
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:5-68
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.GET_ACCOUNTS
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.READ_CALENDAR
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:22-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:22-78
application
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:23:5-50:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fb265d384d30d2dee5543d95a6ec4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fb265d384d30d2dee5543d95a6ec4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd84777daae1699cab565fc4ecfceb0\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd84777daae1699cab565fc4ecfceb0\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f54a6e2c946881b13764e54e3f3b18cc\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f54a6e2c946881b13764e54e3f3b18cc\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2d6103a37e79a7c1ef7d07d8a118ba\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2d6103a37e79a7c1ef7d07d8a118ba\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764d0f7915ef8de4cba308e0c460a04a\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764d0f7915ef8de4cba308e0c460a04a\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0372b3478d6e1886c1f6221833fcdd5c\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0372b3478d6e1886c1f6221833fcdd5c\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1de57881c2537fefd04aeed1ab691f5a\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1de57881c2537fefd04aeed1ab691f5a\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78351a9202ab955e6373511c2847685c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78351a9202ab955e6373511c2847685c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:24:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:22:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:20:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:23:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:26:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:21:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:25:9-44
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:19:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:17:9-42
activity#com.taskiq.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:27:9-48:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:31:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:30:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:32:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:33:13-37:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:oauth2redirect+data:scheme:taskiq
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:40:13-47:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
data
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
	android:host
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
receiver#com.taskiq.app.service.NotificationReceiver
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:51:9-60:20
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:53:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:54:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:52:13-57
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
receiver#com.taskiq.app.service.BootReceiver
ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:63:9-72:20
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:66:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:64:13-49
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e79e31f8dad322d68c1a9d1a3b6f09f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e79e31f8dad322d68c1a9d1a3b6f09f\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c51bdc67d0f2ca29b6af4c4c8d8827e\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c51bdc67d0f2ca29b6af4c4c8d8827e\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c731d929a795de99145107971c186ce\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c731d929a795de99145107971c186ce\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4239f854065c6cb76db75da03d84d7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa4239f854065c6cb76db75da03d84d7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.36.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52dd6af57e481b458fdd606e969027e0\transformed\accompanist-swiperefresh-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-swiperefresh:0.36.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52dd6af57e481b458fdd606e969027e0\transformed\accompanist-swiperefresh-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\904e1630c563f33d0f5478f7716fee6a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\904e1630c563f33d0f5478f7716fee6a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed92ea3220a5bbd75c2f84d4c18dcd5\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1ed92ea3220a5bbd75c2f84d4c18dcd5\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be1cd8404036620e7b9e129b051e87f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be1cd8404036620e7b9e129b051e87f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\432509cb614287948c66b4268de1f615\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\432509cb614287948c66b4268de1f615\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0791b56ebd6f18e3246ab658967e42f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0791b56ebd6f18e3246ab658967e42f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99a8174784df102df178925cfbe08e23\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99a8174784df102df178925cfbe08e23\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90efc6c90b6408b7c8e7c8fd5b36363\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90efc6c90b6408b7c8e7c8fd5b36363\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34dc7db67f4194251fe043b8afec8c6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f34dc7db67f4194251fe043b8afec8c6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\378fa87e62cc8a7e1cdd9fa9ffc7eb39\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\378fa87e62cc8a7e1cdd9fa9ffc7eb39\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa0ae4f33dca1ad6614307f9df736fe8\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa0ae4f33dca1ad6614307f9df736fe8\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77db0d61998be1d5683e5d85c3e051\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb77db0d61998be1d5683e5d85c3e051\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a391acb752c9da73f1f81df43e0170f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a391acb752c9da73f1f81df43e0170f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c31efc59dea433690b1e4c284538cc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88c31efc59dea433690b1e4c284538cc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e51290920b98c8dbaf60055e9a6f558\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e51290920b98c8dbaf60055e9a6f558\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764023a455144f62a86bfbce3d6749a4\transformed\credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764023a455144f62a86bfbce3d6749a4\transformed\credentials-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fb265d384d30d2dee5543d95a6ec4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fb265d384d30d2dee5543d95a6ec4\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd84777daae1699cab565fc4ecfceb0\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bbd84777daae1699cab565fc4ecfceb0\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6511e9096ee7467b892ec3c75d51b348\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6511e9096ee7467b892ec3c75d51b348\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ffcdd7f913c99ccf89fb28bd20c182\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\77ffcdd7f913c99ccf89fb28bd20c182\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f54a6e2c946881b13764e54e3f3b18cc\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f54a6e2c946881b13764e54e3f3b18cc\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2d6103a37e79a7c1ef7d07d8a118ba\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe2d6103a37e79a7c1ef7d07d8a118ba\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764d0f7915ef8de4cba308e0c460a04a\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-blockstore:16.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\764d0f7915ef8de4cba308e0c460a04a\transformed\play-services-auth-blockstore-16.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0372b3478d6e1886c1f6221833fcdd5c\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0372b3478d6e1886c1f6221833fcdd5c\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9c9b0f6db1266a3ca39e2581fbabfa7\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity-credentials:16.0.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9c9b0f6db1266a3ca39e2581fbabfa7\transformed\play-services-identity-credentials-16.0.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb8529be0a3862c1b80f1622c3473de\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb8529be0a3862c1b80f1622c3473de\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf0915ad8af68c9848003015b54f1279\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\bf0915ad8af68c9848003015b54f1279\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d95ba4be76e166e463f44c8187510faa\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d95ba4be76e166e463f44c8187510faa\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2773c54afb7f0bd722fed5a6c4cddc\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2773c54afb7f0bd722fed5a6c4cddc\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb44a9a24015f2bfec4b4de89ce351f\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cdb44a9a24015f2bfec4b4de89ce351f\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54b06ce0d85c848048ae92416efcb7f\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54b06ce0d85c848048ae92416efcb7f\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab01b5e3b8e2accd167ccfeb3d776bdb\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab01b5e3b8e2accd167ccfeb3d776bdb\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\699a73af6967cfd4c447542bec97d435\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\699a73af6967cfd4c447542bec97d435\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0e1f6e3e1bcbf1b7ce8eeb43d6494c7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0e1f6e3e1bcbf1b7ce8eeb43d6494c7\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4019a3ae73594c7825a7e94eaf189b16\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4019a3ae73594c7825a7e94eaf189b16\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a9cc2c4f9d54affe434e92f0a3b5b93\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a9cc2c4f9d54affe434e92f0a3b5b93\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc68c58374a0e10bd9125599a8d8599b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc68c58374a0e10bd9125599a8d8599b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a5cde69cfda992ab7d6b7ae4fa52b2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49a5cde69cfda992ab7d6b7ae4fa52b2\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea3ff6759af670f7ea5cd0e3229adf6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea3ff6759af670f7ea5cd0e3229adf6f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1333512a63fd7d24f46c7f9cc4f79c87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1333512a63fd7d24f46c7f9cc4f79c87\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c0b4d92b3f43b1cae454b2d885a0d17\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c0b4d92b3f43b1cae454b2d885a0d17\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\046fd92d85ea3b574c144dd221a5e727\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\046fd92d85ea3b574c144dd221a5e727\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e2dd86a0ca09120e9dca087d303f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\076e2dd86a0ca09120e9dca087d303f9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\370d43f945ed4b15ae2820bd61637a4e\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\370d43f945ed4b15ae2820bd61637a4e\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9014300a966b65571e483b4a895d94bf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9014300a966b65571e483b4a895d94bf\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\648d48d7a66ae26cff95c1357b99d181\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\648d48d7a66ae26cff95c1357b99d181\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71ea35a3095c69e9eeeedffeeb9c50\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec71ea35a3095c69e9eeeedffeeb9c50\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3281e5b192404400046aa197cab4882\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3281e5b192404400046aa197cab4882\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8580dcaca1b9cac1c3de7029e62def\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad8580dcaca1b9cac1c3de7029e62def\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49aceb762336c9b20fffe88cba7687c4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49aceb762336c9b20fffe88cba7687c4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c973c95ede6af205f7463ef434fee5c1\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c973c95ede6af205f7463ef434fee5c1\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc20826b07a3eca4be881e833dc2878\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddc20826b07a3eca4be881e833dc2878\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\600f4ab1d85b7d3602fcaabdb66e1174\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\600f4ab1d85b7d3602fcaabdb66e1174\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5f033abbfe9f88fc8a846d0eb0fb83\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5f033abbfe9f88fc8a846d0eb0fb83\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d9d1b63fdb25a5f9458cda713e57734\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d9d1b63fdb25a5f9458cda713e57734\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8a65ac085891ef11657c59e001ed0cf\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8a65ac085891ef11657c59e001ed0cf\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a9706dab6c4eaa74acdde856f1912d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a9706dab6c4eaa74acdde856f1912d\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1de57881c2537fefd04aeed1ab691f5a\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1de57881c2537fefd04aeed1ab691f5a\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc4d4549228ea2b094f042592c6c45\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\97cc4d4549228ea2b094f042592c6c45\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e19f2f90b02d9084bfe9d12f3eee99b8\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e19f2f90b02d9084bfe9d12f3eee99b8\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\909e192009fb7f337399eefac7faaf77\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\909e192009fb7f337399eefac7faaf77\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2304a6600df2f5973a51ac1eea9544a\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2304a6600df2f5973a51ac1eea9544a\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bd519d2e243fd54142c626db14966a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\60bd519d2e243fd54142c626db14966a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e785f14f9db042ce793d07dfd6d648ed\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e785f14f9db042ce793d07dfd6d648ed\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c84a4761139e2c94c4fb1f2712d07a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c84a4761139e2c94c4fb1f2712d07a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63697056f69d193aacd905c2ec4f8045\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\63697056f69d193aacd905c2ec4f8045\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3442f64d4112f819ba6cca64e4543a80\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3442f64d4112f819ba6cca64e4543a80\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\069925cd988b867290d3af19d695fe7f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\069925cd988b867290d3af19d695fe7f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cc6b6bc07239fc23c0902000437baea\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cc6b6bc07239fc23c0902000437baea\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14eaf830e287797f1d03e4a3dbaedd4a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14eaf830e287797f1d03e4a3dbaedd4a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c278c9459f2d385329fbed6652307e\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67c278c9459f2d385329fbed6652307e\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabcd5ff92bff42a04134037b2e17511\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabcd5ff92bff42a04134037b2e17511\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\723e37b713ef8fccee6943e50ec7b4c9\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\723e37b713ef8fccee6943e50ec7b4c9\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dba852a1967de123ff2bff1abb41126a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dba852a1967de123ff2bff1abb41126a\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36baa5a1a3568e4e888880ae9f77c7d9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\36baa5a1a3568e4e888880ae9f77c7d9\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b0f19fe26d54cc04e910eadffc49d8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\08b0f19fe26d54cc04e910eadffc49d8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9810c06671ee72d2867c6f8db2672ddf\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9810c06671ee72d2867c6f8db2672ddf\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78351a9202ab955e6373511c2847685c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78351a9202ab955e6373511c2847685c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4597693c94534caf5d822fe283bb9a0\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4597693c94534caf5d822fe283bb9a0\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0d20b21e1a9ec0752fda4a68b5b5f8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c0d20b21e1a9ec0752fda4a68b5b5f8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f965f7972ec3c08b2726cef08d0245\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5f965f7972ec3c08b2726cef08d0245\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a770640834ed1359b8c48c6a63bc6b1\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a770640834ed1359b8c48c6a63bc6b1\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f38e0fe19a2db3f4a293435fa37a1c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07f38e0fe19a2db3f4a293435fa37a1c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fef0f6a477bc1bf2debe298d53188bbb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fef0f6a477bc1bf2debe298d53188bbb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baee743243569936044c57955d951281\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baee743243569936044c57955d951281\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\870192ab2f97875bbb966198050a5174\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\870192ab2f97875bbb966198050a5174\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51046fa3d79b35e15d8e0b0c0d2b7ca9\transformed\sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51046fa3d79b35e15d8e0b0c0d2b7ca9\transformed\sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a337fbe199d836b436436c01fe4e1d7\transformed\ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a337fbe199d836b436436c01fe4e1d7\transformed\ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
activity#androidx.credentials.playservices.IdentityCredentialApiHiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e2d77c892e841ad97de1af34942a8ee\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93

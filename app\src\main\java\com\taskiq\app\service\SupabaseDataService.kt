package com.taskiq.app.service

import android.util.Log
import com.taskiq.app.config.SupabaseConfig
import com.taskiq.app.model.*
import io.github.jan.supabase.postgrest.from
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Supabase data service for cloud database operations
 * Handles CRUD operations for tasks, bills, important dates, and linked emails
 */
class SupabaseDataService {
    
    private val supabase = SupabaseConfig.client
    
    companion object {
        private const val TAG = "SupabaseDataService"
    }
    
    // ==================== TASKS ====================
    
    /**
     * Get all tasks for the current user
     */
    suspend fun getTasks(userId: String): List<Task> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Fetching tasks for user: $userId")
                
                val supabaseTasks = supabase.from(SupabaseConfig.Tables.TASKS)
                    .select()
                    .decodeList<SupabaseTask>()
                    .filter { it.userId == userId }
                
                val tasks = supabaseTasks.map { it.toTask() }
                Log.d(TAG, "Fetched ${tasks.size} tasks")
                
                tasks
                
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching tasks: ${e.message}", e)
                emptyList()
            }
        }
    }
    
    /**
     * Add a new task
     */
    suspend fun addTask(task: Task, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Adding task: ${task.title}")
                
                val supabaseTask = task.toSupabaseTask(userId)
                
                supabase.from(SupabaseConfig.Tables.TASKS).insert(supabaseTask)
                
                Log.d(TAG, "Task added successfully: ${task.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error adding task: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Update an existing task
     */
    suspend fun updateTask(task: Task, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating task: ${task.title}")
                
                val supabaseTask = task.toSupabaseTask(userId)
                
                supabase.from(SupabaseConfig.Tables.TASKS)
                    .update(
                        mapOf(
                            "title" to supabaseTask.title,
                            "description" to supabaseTask.description,
                            "isCompleted" to supabaseTask.isCompleted,
                            "priority" to supabaseTask.priority,
                            "category" to supabaseTask.category,
                            "dueDate" to supabaseTask.dueDate,
                            "hasNoDueDate" to supabaseTask.hasNoDueDate,
                            "subtasks" to supabaseTask.subtasks,
                            "updatedAt" to java.time.Instant.now().toString()
                        )
                    ) {
                        filter {
                            eq("id", task.id)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Task updated successfully: ${task.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error updating task: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Delete a task
     */
    suspend fun deleteTask(taskId: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Deleting task: $taskId")
                
                supabase.from(SupabaseConfig.Tables.TASKS)
                    .delete {
                        filter {
                            eq("id", taskId)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Task deleted successfully: $taskId")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting task: ${e.message}", e)
                false
            }
        }
    }
    
    // ==================== BILLS ====================
    
    /**
     * Get all bills for the current user
     */
    suspend fun getBills(userId: String): List<Bill> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Fetching bills for user: $userId")
                
                val supabaseBills = supabase.from(SupabaseConfig.Tables.BILLS)
                    .select()
                    .decodeList<SupabaseBill>()
                    .filter { it.userId == userId }
                
                val bills = supabaseBills.map { it.toBill() }
                Log.d(TAG, "Fetched ${bills.size} bills")
                
                bills
                
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching bills: ${e.message}", e)
                emptyList()
            }
        }
    }
    
    /**
     * Add a new bill
     */
    suspend fun addBill(bill: Bill, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Adding bill: ${bill.title}")
                
                val supabaseBill = bill.toSupabaseBill(userId)
                
                supabase.from(SupabaseConfig.Tables.BILLS).insert(supabaseBill)
                
                Log.d(TAG, "Bill added successfully: ${bill.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error adding bill: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Update an existing bill
     */
    suspend fun updateBill(bill: Bill, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating bill: ${bill.title}")
                
                val supabaseBill = bill.toSupabaseBill(userId)
                
                supabase.from(SupabaseConfig.Tables.BILLS)
                    .update(
                        mapOf(
                            "title" to supabaseBill.title,
                            "amount" to supabaseBill.amount,
                            "dueDate" to supabaseBill.dueDate,
                            "status" to supabaseBill.status,
                            "type" to supabaseBill.type,
                            "description" to supabaseBill.description,
                            "updatedAt" to java.time.Instant.now().toString()
                        )
                    ) {
                        filter {
                            eq("id", bill.id)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Bill updated successfully: ${bill.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error updating bill: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Delete a bill
     */
    suspend fun deleteBill(billId: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Deleting bill: $billId")
                
                supabase.from(SupabaseConfig.Tables.BILLS)
                    .delete {
                        filter {
                            eq("id", billId)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Bill deleted successfully: $billId")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting bill: ${e.message}", e)
                false
            }
        }
    }
    
    // ==================== IMPORTANT DATES ====================
    
    /**
     * Get all important dates for the current user
     */
    suspend fun getImportantDates(userId: String): List<Task> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Fetching important dates for user: $userId")
                
                val supabaseDates = supabase.from(SupabaseConfig.Tables.IMPORTANT_DATES)
                    .select()
                    .decodeList<SupabaseImportantDate>()
                    .filter { it.userId == userId }
                
                val dates = supabaseDates.map { it.toTask() }
                Log.d(TAG, "Fetched ${dates.size} important dates")
                
                dates
                
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching important dates: ${e.message}", e)
                emptyList()
            }
        }
    }
    
    /**
     * Add a new important date
     */
    suspend fun addImportantDate(date: Task, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Adding important date: ${date.title}")
                
                val supabaseDate = date.toSupabaseImportantDate(userId)
                
                supabase.from(SupabaseConfig.Tables.IMPORTANT_DATES).insert(supabaseDate)
                
                Log.d(TAG, "Important date added successfully: ${date.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error adding important date: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Update an existing important date
     */
    suspend fun updateImportantDate(date: Task, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating important date: ${date.title}")
                
                val supabaseDate = date.toSupabaseImportantDate(userId)
                
                supabase.from(SupabaseConfig.Tables.IMPORTANT_DATES)
                    .update(
                        mapOf(
                            "title" to supabaseDate.title,
                            "date" to supabaseDate.date,
                            "description" to supabaseDate.description,
                            "isRecurring" to supabaseDate.isRecurring,
                            "recurringType" to supabaseDate.recurringType,
                            "subtasks" to supabaseDate.subtasks,
                            "updatedAt" to java.time.Instant.now().toString()
                        )
                    ) {
                        filter {
                            eq("id", date.id)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Important date updated successfully: ${date.title}")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error updating important date: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Delete an important date
     */
    suspend fun deleteImportantDate(dateId: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Deleting important date: $dateId")
                
                supabase.from(SupabaseConfig.Tables.IMPORTANT_DATES)
                    .delete {
                        filter {
                            eq("id", dateId)
                            eq("userId", userId)
                        }
                    }
                
                Log.d(TAG, "Important date deleted successfully: $dateId")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting important date: ${e.message}", e)
                false
            }
        }
    }

    // ==================== LINKED EMAILS ====================

    /**
     * Get all linked emails for the current user
     */
    suspend fun getLinkedEmails(userId: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Fetching linked emails for user: $userId")

                val supabaseEmails = supabase.from(SupabaseConfig.Tables.LINKED_EMAILS)
                    .select()
                    .decodeList<SupabaseLinkedEmail>()
                    .filter { it.userId == userId && it.isActive }

                val emails = supabaseEmails.map { it.email }
                Log.d(TAG, "Fetched ${emails.size} linked emails")

                emails

            } catch (e: Exception) {
                Log.e(TAG, "Error fetching linked emails: ${e.message}", e)
                emptyList()
            }
        }
    }

    /**
     * Add a new linked email
     */
    suspend fun addLinkedEmail(email: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Adding linked email: $email")

                val supabaseEmail = SupabaseLinkedEmail(
                    userId = userId,
                    email = email,
                    isActive = true
                )

                supabase.from(SupabaseConfig.Tables.LINKED_EMAILS).insert(supabaseEmail)

                Log.d(TAG, "Linked email added successfully: $email")
                true

            } catch (e: Exception) {
                Log.e(TAG, "Error adding linked email: ${e.message}", e)
                false
            }
        }
    }

    /**
     * Remove a linked email
     */
    suspend fun removeLinkedEmail(email: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Removing linked email: $email")

                supabase.from(SupabaseConfig.Tables.LINKED_EMAILS)
                    .delete {
                        filter {
                            eq("email", email)
                            eq("userId", userId)
                        }
                    }

                Log.d(TAG, "Linked email removed successfully: $email")
                true

            } catch (e: Exception) {
                Log.e(TAG, "Error removing linked email: ${e.message}", e)
                false
            }
        }
    }

    // ==================== SYNC OPERATIONS ====================

    /**
     * Sync all user data from cloud
     */
    suspend fun syncAllData(userId: String): SyncResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Syncing all data for user: $userId")

                val tasks = getTasks(userId)
                val bills = getBills(userId)
                val importantDates = getImportantDates(userId)
                val linkedEmails = getLinkedEmails(userId)

                Log.d(TAG, "Sync completed: ${tasks.size} tasks, ${bills.size} bills, ${importantDates.size} dates, ${linkedEmails.size} emails")

                SyncResult.Success(
                    tasks = tasks,
                    bills = bills,
                    importantDates = importantDates,
                    linkedEmails = linkedEmails
                )

            } catch (e: Exception) {
                Log.e(TAG, "Error syncing data: ${e.message}", e)
                SyncResult.Error(e.message ?: "Unknown error")
            }
        }
    }
}

/**
 * Result class for sync operations
 */
sealed class SyncResult {
    data class Success(
        val tasks: List<Task>,
        val bills: List<Bill>,
        val importantDates: List<Task>,
        val linkedEmails: List<String>
    ) : SyncResult()

    data class Error(val message: String) : SyncResult()
}

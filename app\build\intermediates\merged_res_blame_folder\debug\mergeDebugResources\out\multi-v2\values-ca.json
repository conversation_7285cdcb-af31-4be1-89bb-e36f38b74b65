{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "15176", "endColumns": "88", "endOffsets": "15260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6431,6933,7036,7147", "endColumns": "112,102,110,108", "endOffsets": "6539,7031,7142,7251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61468bcbedfc450e580f06e70db30d2d\\transformed\\play-services-base-18.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4066,4171,4323,4450,4559,4709,4836,4959,5202,5373,5482,5641,5772,5936,6094,6159,6227", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "4166,4318,4445,4554,4704,4831,4954,5062,5368,5477,5636,5767,5931,6089,6154,6222,6309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1ba429aa6de3be7de3bb6e415af4e51\\transformed\\play-services-basement-18.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5067", "endColumns": "134", "endOffsets": "5197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,265,380,532,677,804,937,1084,1188,1328,1480", "endColumns": "116,92,114,151,144,126,132,146,103,139,151,126", "endOffsets": "167,260,375,527,672,799,932,1079,1183,1323,1475,1602"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6314,6648,7256,7371,7523,7668,7795,7928,8075,8179,8319,8471", "endColumns": "116,92,114,151,144,126,132,146,103,139,151,126", "endOffsets": "6426,6736,7366,7518,7663,7790,7923,8070,8174,8314,8466,8593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3154,3250,3352,3451,3548,3654,3759,16091", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3245,3347,3446,3543,3649,3754,3880,16187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,242", "endColumns": "88,97,101", "endOffsets": "139,237,339"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3065,16465,16563", "endColumns": "88,97,101", "endOffsets": "3149,16558,16660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,292,376,480,583,672,750,841,932,1018,1104,1195,1271,1356,1431,1510,1585,1667,1738", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "287,371,475,578,667,745,836,927,1013,1099,1190,1266,1351,1426,1505,1580,1662,1733,1853"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3885,3982,6544,6741,6844,8598,8676,15265,15356,15442,15528,15701,15777,15862,15937,16016,16192,16274,16345", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "3977,4061,6643,6839,6928,8671,8762,15351,15437,15523,15614,15772,15857,15932,16011,16086,16269,16340,16460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,15619", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,15696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8767,8888,9007,9126,9246,9346,9444,9559,9701,9816,9975,10059,10157,10255,10356,10473,10602,10705,10846,10986,11127,11293,11426,11543,11664,11793,11892,11989,12110,12255,12361,12474,12588,12727,12872,12981,13088,13174,13275,13376,13487,13573,13659,13770,13850,13934,14035,14143,14242,14346,14433,14546,14646,14753,14872,14952,15069", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "8883,9002,9121,9241,9341,9439,9554,9696,9811,9970,10054,10152,10250,10351,10468,10597,10700,10841,10981,11122,11288,11421,11538,11659,11788,11887,11984,12105,12250,12356,12469,12583,12722,12867,12976,13083,13169,13270,13371,13482,13568,13654,13765,13845,13929,14030,14138,14237,14341,14428,14541,14641,14748,14867,14947,15064,15171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,119", "endOffsets": "165,285"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2830,2945", "endColumns": "114,119", "endOffsets": "2940,3060"}}]}]}
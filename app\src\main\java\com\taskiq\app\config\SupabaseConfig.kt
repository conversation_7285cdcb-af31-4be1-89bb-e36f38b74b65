package com.taskiq.app.config

import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.GoTrue
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.realtime.Realtime

/**
 * Supabase configuration for TaskIQ app
 * Provides cloud database and authentication services
 */
object SupabaseConfig {
    
    // Supabase project configuration
    // IMPORTANT: Replace these with your actual Supabase project credentials
    // Get these from: Supabase Dashboard > Settings > API
    private const val SUPABASE_URL = "https://your-project-ref.supabase.co"  // Replace with your Project URL
    private const val SUPABASE_ANON_KEY = "your-anon-key-here"  // Replace with your anon/public key
    
    /**
     * Supabase client instance with required modules
     */
    val client = createSupabaseClient(
        supabaseUrl = SUPABASE_URL,
        supabaseKey = SUPABASE_ANON_KEY
    ) {
        install(Postgrest) {
            // PostgreSQL REST API configuration
        }
        install(GoTrue) {
            // Authentication configuration
        }
        install(Realtime) {
            // Real-time subscriptions configuration
        }
    }
    
    /**
     * Database table names
     */
    object Tables {
        const val USERS = "users"
        const val TASKS = "tasks"
        const val BILLS = "bills"
        const val IMPORTANT_DATES = "important_dates"
        const val LINKED_EMAILS = "linked_emails"
    }
    
    /**
     * Check if Supabase is properly configured
     */
    fun isConfigured(): Boolean {
        return SUPABASE_URL != "https://your-project-ref.supabase.co" && 
               SUPABASE_ANON_KEY != "your-anon-key-here"
    }
}

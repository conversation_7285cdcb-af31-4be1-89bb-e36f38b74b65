package com.taskiq.app.config

import com.taskiq.app.BuildConfig
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.gotrue.GoTrue
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.realtime.Realtime

/**
 * Supabase configuration for TaskIQ app
 * Provides cloud database and authentication services
 */
object SupabaseConfig {
    
    // Supabase project configuration
    // Credentials are securely loaded from local.properties via BuildConfig
    private val SUPABASE_URL = BuildConfig.SUPABASE_URL
    private val SUPABASE_ANON_KEY = BuildConfig.SUPABASE_ANON_KEY
    
    /**
     * Supabase client instance with required modules
     */
    val client = createSupabaseClient(
        supabaseUrl = SUPABASE_URL,
        supabaseKey = SUPABASE_ANON_KEY
    ) {
        install(Postgrest) {
            // PostgreSQL REST API configuration
        }
        install(GoTrue) {
            // Authentication configuration
        }
        install(Realtime) {
            // Real-time subscriptions configuration
        }
    }
    
    /**
     * Database table names
     */
    object Tables {
        const val USERS = "users"
        const val TASKS = "tasks"
        const val BILLS = "bills"
        const val IMPORTANT_DATES = "important_dates"
        const val LINKED_EMAILS = "linked_emails"
    }
    
    /**
     * Check if Supabase is properly configured
     */
    fun isConfigured(): Boolean {
        return SUPABASE_URL.isNotEmpty() && SUPABASE_ANON_KEY.isNotEmpty() &&
               !SUPABASE_URL.contains("your-project-ref") &&
               !SUPABASE_ANON_KEY.contains("your-anon-key-here")
    }
}

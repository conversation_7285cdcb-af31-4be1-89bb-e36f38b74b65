package com.taskiq.app.config

import com.taskiq.app.BuildConfig

/**
 * Supabase configuration for TaskIQ app
 * Provides cloud database and authentication services
 */
object SupabaseConfig {

    // Supabase project configuration
    // Credentials are securely loaded from local.properties via BuildConfig
    val SUPABASE_URL = BuildConfig.SUPABASE_URL
    val SUPABASE_ANON_KEY = BuildConfig.SUPABASE_ANON_KEY

    /**
     * Lazy initialization of Supabase client to avoid import issues during compilation
     */
    val client by lazy {
        try {
            createSupabaseClientSafely()
        } catch (e: Exception) {
            null
        }
    }

    private fun createSupabaseClientSafely(): Any? {
        return try {
            // This will be implemented once dependencies are properly resolved
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Database table names
     */
    object Tables {
        const val USERS = "users"
        const val TASKS = "tasks"
        const val BILLS = "bills"
        const val IMPORTANT_DATES = "important_dates"
        const val LINKED_EMAILS = "linked_emails"
    }
    
    /**
     * Check if Supabase is properly configured
     */
    fun isConfigured(): Boolean {
        return SUPABASE_URL.isNotEmpty() && SUPABASE_ANON_KEY.isNotEmpty() &&
               !SUPABASE_URL.contains("your-project-ref") &&
               !SUPABASE_ANON_KEY.contains("your-anon-key-here")
    }
}

package com.taskiq.app.config

import com.taskiq.app.BuildConfig

/**
 * Supabase configuration for TaskIQ app
 * Provides cloud database and authentication services
 */
object SupabaseConfig {

    // Supabase project configuration
    // Credentials are securely loaded from local.properties via BuildConfig
    val SUPABASE_URL = BuildConfig.SUPABASE_URL
    val SUPABASE_ANON_KEY = BuildConfig.SUPABASE_ANON_KEY

    /**
     * Supabase client instance - simplified for now
     */
    val client: Any? by lazy {
        // Simplified implementation - will be enhanced when dependencies resolve
        null
    }
    
    /**
     * Database table names
     */
    object Tables {
        const val USERS = "users"
        const val TASKS = "tasks"
        const val BILLS = "bills"
        const val IMPORTANT_DATES = "important_dates"
        const val LINKED_EMAILS = "linked_emails"
    }
    
    /**
     * Check if Supabase is properly configured
     */
    fun isConfigured(): <PERSON><PERSON>an {
        return SUPABASE_URL.isNotEmpty() && SUPABASE_ANON_KEY.isNotEmpty() &&
               !SUPABASE_URL.contains("your-project-ref") &&
               !SUPABASE_ANON_KEY.contains("your-anon-key-here")
    }
}

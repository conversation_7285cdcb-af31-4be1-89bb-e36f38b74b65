{"logs": [{"outputFile": "com.taskiq.app-mergeReleaseResources-79:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4035,4138,4300,4427,4535,4685,4814,4930,5173,5333,5441,5605,5737,5892,6037,6100,6165", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "4133,4295,4422,4530,4680,4809,4925,5028,5328,5436,5600,5732,5887,6032,6095,6160,6248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,253,371,500,629,758,889,1020,1119,1258,1393", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "158,248,366,495,624,753,884,1015,1114,1253,1388,1505"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6253,6557,7130,7248,7377,7506,7635,7766,7897,7996,8135,8270", "endColumns": "107,89,117,128,128,128,130,130,98,138,134,116", "endOffsets": "6356,6642,7243,7372,7501,7630,7761,7892,7991,8130,8265,8382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0aa768bf2c77d0f2a11c0fea0a786\\transformed\\browser-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6361,6824,6924,7031", "endColumns": "99,99,106,98", "endOffsets": "6456,6919,7026,7125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14802", "endColumns": "89", "endOffsets": "14887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,115", "endOffsets": "160,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2827,2937", "endColumns": "109,115", "endOffsets": "2932,3048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,279,359,455,550,632,710,801,892,976,1058,1143,1215,1301,1376,1451,1523,1600,1671", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "274,354,450,545,627,705,796,887,971,1053,1138,1210,1296,1371,1446,1518,1595,1666,1788"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3862,3955,6461,6647,6742,8387,8465,14892,14983,15067,15149,15317,15389,15475,15550,15625,15798,15875,15946", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "3950,4030,6552,6737,6819,8460,8551,14978,15062,15144,15229,15384,15470,15545,15620,15692,15870,15941,16063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,225", "endColumns": "79,89,88", "endOffsets": "130,220,309"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3053,16068,16158", "endColumns": "79,89,88", "endOffsets": "3128,16153,16242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5033", "endColumns": "139", "endOffsets": "5168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8556,8676,8798,8912,9031,9130,9231,9349,9482,9602,9750,9837,9938,10032,10131,10247,10374,10480,10615,10748,10879,11054,11180,11299,11420,11542,11637,11734,11854,11988,12093,12196,12301,12432,12567,12675,12778,12855,12951,13047,13151,13238,13323,13429,13509,13595,13696,13800,13894,13998,14085,14194,14295,14402,14519,14599,14703", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "8671,8793,8907,9026,9125,9226,9344,9477,9597,9745,9832,9933,10027,10126,10242,10369,10475,10610,10743,10874,11049,11175,11294,11415,11537,11632,11729,11849,11983,12088,12191,12296,12427,12562,12670,12773,12850,12946,13042,13146,13233,13318,13424,13504,13590,13691,13795,13889,13993,14080,14189,14290,14397,14514,14594,14698,14797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,15234", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,15312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3133,3228,3330,3428,3531,3637,3742,15697", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3223,3325,3423,3526,3632,3737,3857,15793"}}]}]}
-- Quick Fix SQL for Gender Column and Important Dates Issues
-- Run this in your Supabase SQL Editor

-- 1. Fix Gender Column Issue
-- This will add the gender column to existing users table or create the table if it doesn't exist
DO $$
BEGIN
    -- Check if users table exists and add gender column if missing
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        -- Table exists, check if gender column exists
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'users' AND column_name = 'gender' AND table_schema = 'public') THEN
            -- Add gender column
            ALTER TABLE public.users ADD COLUMN gender TEXT CHECK (gender IN ('Male', 'Female', 'Other', 'Select')) DEFAULT 'Select';
            RAISE NOTICE 'Gender column added to existing users table';
        ELSE
            RAISE NOTICE 'Gender column already exists in users table';
        END IF;
    ELSE
        -- Create users table with gender column
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
            email TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            gender TEXT CHECK (gender IN ('Male', 'Female', 'Other', 'Select')) DEFAULT 'Select',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Enable RLS
        ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
        CREATE POLICY "Users can view own profile" ON public.users
            FOR SELECT USING (auth.uid() = id);

        DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
        CREATE POLICY "Users can update own profile" ON public.users
            FOR UPDATE USING (auth.uid() = id);

        DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
        CREATE POLICY "Users can insert own profile" ON public.users
            FOR INSERT WITH CHECK (auth.uid() = id);
            
        RAISE NOTICE 'Users table created with gender column';
    END IF;
END $$;

-- 2. Ensure Important Dates Table Exists
CREATE TABLE IF NOT EXISTS public.important_dates (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurring_type TEXT CHECK (recurring_type IN ('YEARLY', 'MONTHLY', 'WEEKLY')),
    subtasks JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on important_dates table
ALTER TABLE public.important_dates ENABLE ROW LEVEL SECURITY;

-- Create/Update policies for important_dates
DROP POLICY IF EXISTS "Users can view own important dates" ON public.important_dates;
CREATE POLICY "Users can view own important dates" ON public.important_dates
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own important dates" ON public.important_dates;
CREATE POLICY "Users can insert own important dates" ON public.important_dates
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own important dates" ON public.important_dates;
CREATE POLICY "Users can update own important dates" ON public.important_dates
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own important dates" ON public.important_dates;
CREATE POLICY "Users can delete own important dates" ON public.important_dates
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_important_dates_user_id ON public.important_dates(user_id);
CREATE INDEX IF NOT EXISTS idx_important_dates_date ON public.important_dates(date);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_important_dates_updated_at ON public.important_dates;
CREATE TRIGGER update_important_dates_updated_at BEFORE UPDATE ON public.important_dates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Verification queries
SELECT 'Gender column exists in users table' as status 
WHERE EXISTS (SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'users' AND column_name = 'gender' AND table_schema = 'public');

SELECT 'Important dates table exists' as status 
WHERE EXISTS (SELECT 1 FROM information_schema.tables 
              WHERE table_name = 'important_dates' AND table_schema = 'public');

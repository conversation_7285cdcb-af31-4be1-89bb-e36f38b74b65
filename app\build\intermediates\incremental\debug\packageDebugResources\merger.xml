<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res"><file name="ic_check_circle" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_mailbox" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_mailbox.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_summary" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\ic_summary.xml" qualifiers="" type="drawable"/><file name="taskiq" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\drawable\taskiq.png" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="professional_blue">#FF1565C0</color><color name="off_white">#FFF8F8F8</color></file><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#0066CC</color></file><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TaskIQ</string><string name="app_tagline">For minds that multitask.</string><string name="notification_history">Notification History</string><string name="no_notifications">No notifications yet</string><string name="back">Back</string><string name="clear_all">Clear All</string></file><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values\styles.xml" qualifiers=""/><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TaskIQ" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style><style name="Theme.TaskIQ.Main" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\values-v31\themes.xml" qualifiers="v31"><style name="Theme.TaskIQ" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        
        <item name="android:windowSplashScreenBackground">@color/professional_blue</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
    </style><style name="Theme.TaskIQ.Main" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\processDebugGoogleServices"/><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">923766663449-mqg30ij6rit4qfmsog58fea3dfrdaist.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">923766663449</string><string name="google_api_key" translatable="false">AIzaSyDLiFx-qCwDFHkCwaprbOFp-3kysoi1IBs</string><string name="google_app_id" translatable="false">1:923766663449:android:46b297660eda892e987d88</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDLiFx-qCwDFHkCwaprbOFp-3kysoi1IBs</string><string name="google_storage_bucket" translatable="false">task-reminder-app-bb81d.firebasestorage.app</string><string name="project_id" translatable="false">task-reminder-app-bb81d</string></file></source><source path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\injectCrashlyticsMappingFileIdDebug"><file path="C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\injectCrashlyticsMappingFileIdDebug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-injectCrashlyticsMappingFileIdDebug" generated-set="res-injectCrashlyticsMappingFileIdDebug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
@file:OptIn(ExperimentalMaterial3Api::class)

package com.taskiq.app.ui.screens

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Title
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import java.text.SimpleDateFormat
import java.util.*

import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.ssp

private fun isSameDate(date1: Date?, date2: Date?): Boolean {
    if (date1 == null && date2 == null) return true
    if (date1 == null || date2 == null) return false
    
    val cal1 = Calendar.getInstance().apply { time = date1 }
    val cal2 = Calendar.getInstance().apply { time = date2 }
    
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
           cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
           cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddImportantDatesScreen(
    onDismiss: () -> Unit,
    onDateAdded: (Task) -> Unit,
    existingDate: Task? = null,
    taskViewModel: TaskViewModel = viewModel()
) {
    var title by remember { mutableStateOf(existingDate?.title ?: "") }
    var description by remember { mutableStateOf(existingDate?.description ?: "") }
    var selectedDate by remember { mutableStateOf(existingDate?.dueDate ?: Calendar.getInstance().time) }
    var showDatePicker by remember { mutableStateOf(false) }
    var selectedFrequency by remember { mutableStateOf(existingDate?.frequency ?: TaskFrequency.ONE_TIME) }
    var customDays by remember { mutableStateOf(existingDate?.customFrequencyDays?.toString() ?: "28") }
    
    // State for duplicate warning
    var showDuplicateWarning by remember { mutableStateOf(false) }
    
    val context = LocalContext.current
    
    // Determine if we're editing or adding
    val isEditing = existingDate != null
    val screenTitle = if (isEditing) "Edit Date" else "Add Date"
    val confirmButtonText = if (isEditing) "Update" else "Add Date"
    
    // Form validation
    val isFormValid = title.isNotBlank() &&
        (selectedFrequency != TaskFrequency.CUSTOM || (customDays.toIntOrNull() ?: 0) > 0)

    // Handle system back button
    BackHandler {
        onDismiss()
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = screenTitle,
                        fontSize = 18.ssp(),
                        fontWeight = FontWeight.Bold,
                        color = ProfessionalWhite
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.padding(end = 16.dp)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back to Dates screen",
                            tint = ProfessionalWhite
                        )
                    }
                },
                actions = {
                    if (isEditing) {
                        // Share Icon
                        IconButton(onClick = {
                            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                                type = "text/plain"
                                val dateFormatted = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(selectedDate)
                                val shareText = """
                                    Important Date Details:
                                    Title: $title
                                    Description: $description
                                    Date: $dateFormatted
                                    Frequency: ${selectedFrequency.name.replace("_", " ")}
                                """.trimIndent()
                                putExtra(Intent.EXTRA_TEXT, shareText)
                            }
                            context.startActivity(Intent.createChooser(shareIntent, "Share Date Details"))
                        }) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "Share Date",
                                tint = ProfessionalWhite
                            )
                        }
                        
                        // Delete Icon
                        IconButton(onClick = { 
                            existingDate?.let { date ->
                                taskViewModel.deleteImportantDate(date.id)
                                onDismiss()
                            }
                        }) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "Delete Date",
                                tint = ProfessionalWhite
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite,
                    actionIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            
            // Title field with icon
            OutlinedTextField(
                value = title,
                onValueChange = { 
                    title = it 
                    showDuplicateWarning = false
                },
                label = { Text("Title") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // Show duplicate warning if needed
            if (showDuplicateWarning) {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "A date with the same title, description, date, and frequency already exists.",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Description field with icon - made larger
            OutlinedTextField(
                value = description,
                onValueChange = { description = it },
                label = { Text("Description") },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                singleLine = false,
                maxLines = 5
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Date selection with icon
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { showDatePicker = true },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.DateRange,
                        contentDescription = "Date",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = "Date",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = java.text.SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(selectedDate),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
            
            if (showDatePicker) {
                val datePickerState = rememberDatePickerState(
                    initialSelectedDateMillis = selectedDate.time
                )
                
                DatePickerDialog(
                    onDismissRequest = { showDatePicker = false },
                    confirmButton = {
                        TextButton(
                            onClick = {
                                datePickerState.selectedDateMillis?.let { millis ->
                                    selectedDate = Date(millis)
                                }
                                showDatePicker = false
                            }
                        ) {
                            Text("OK")
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showDatePicker = false }
                        ) {
                            Text("Cancel")
                        }
                    }
                ) {
                    DatePicker(state = datePickerState)
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Frequency selection with icon
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                FrequencyPickerContent(
                    selectedFrequency = selectedFrequency,
                    onFrequencySelected = { selectedFrequency = it }
                )
            }
            
            // Custom frequency days input field (shown only when Custom is selected)
            AnimatedVisibility(visible = selectedFrequency == TaskFrequency.CUSTOM) {
                Column {
                    Spacer(modifier = Modifier.height(16.dp))

                    OutlinedTextField(
                        value = customDays,
                        onValueChange = { customDays = it },
                        label = { Text("Repeat every X days") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Bottom buttons with modern styling
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = onDismiss,
                    modifier = Modifier.weight(1f),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text("Cancel")
                }
                
                Button(
                    onClick = {
                        val isDuplicate = taskViewModel.importantDates.any { date ->
                            date.title.equals(title, ignoreCase = true) &&
                            date.description.equals(description, ignoreCase = true) &&
                            isSameDate(date.dueDate, selectedDate) &&
                            date.frequency == selectedFrequency
                        }

                        if (isDuplicate) {
                            showDuplicateWarning = true
                        } else {
                            val task = if (isEditing) {
                                existingDate!!.copy(
                                    title = title,
                                    description = description,
                                    dueDate = selectedDate,
                                    frequency = selectedFrequency,
                                    customFrequencyDays = if (selectedFrequency == TaskFrequency.CUSTOM) 
                                        customDays.toIntOrNull() ?: 28 
                                    else 
                                        null
                                )
                            } else {
                                Task(
                                    id = UUID.randomUUID().toString(),
                                    title = title,
                                    description = description,
                                    dueDate = selectedDate,
                                    frequency = selectedFrequency,
                                    customFrequencyDays = if (selectedFrequency == TaskFrequency.CUSTOM) 
                                        customDays.toIntOrNull() ?: 28 
                                    else 
                                        null,
                                    isCompleted = false,
                                    priority = TaskPriority.HIGH, // Important dates are set to HIGH priority
                                    userId = "current_user_id",
                                    createdAt = Date()
                                )
                            }
                            onDateAdded(task)
                        }
                    },
                    enabled = isFormValid,
                    modifier = Modifier.weight(1f),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text(confirmButtonText)
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

@Composable
private fun FrequencyPickerContent(
    selectedFrequency: TaskFrequency,
    onFrequencySelected: (TaskFrequency) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { expanded = true }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Repeat,
            contentDescription = "Repeat",
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "Repeat",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = when (selectedFrequency) {
                    TaskFrequency.ONE_TIME -> "No Repeat"
                    TaskFrequency.MONTHLY -> "Monthly"
                    TaskFrequency.YEARLY -> "Yearly"
                    TaskFrequency.CUSTOM -> "Custom"
                    else -> "No Repeat"
                },
                style = MaterialTheme.typography.bodyLarge
            )
            // Show description of the selected frequency
            val description = when (selectedFrequency) {
                TaskFrequency.MONTHLY -> "Repeats on the same day each month"
                TaskFrequency.YEARLY -> "Repeats on the same date each year"
                TaskFrequency.CUSTOM -> "Repeats based on custom days interval"
                else -> null
            }
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }
        }
        
        Icon(
            imageVector = Icons.Default.ArrowDropDown,
            contentDescription = "Select",
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
    
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = { expanded = false },
        modifier = Modifier.fillMaxWidth(0.7f)
    ) {
        // Only show No Repeat, Monthly, Yearly, and Custom options
        listOf(
            TaskFrequency.ONE_TIME to "No Repeat",
            TaskFrequency.MONTHLY to "Monthly",
            TaskFrequency.YEARLY to "Yearly",
            TaskFrequency.CUSTOM to "Custom"
        ).forEach { (frequency, label) ->
            DropdownMenuItem(
                text = { 
                    Column {
                        Text(label)
                        if (frequency != TaskFrequency.ONE_TIME) {
                            Text(
                                text = when (frequency) {
                                    TaskFrequency.MONTHLY -> "Repeats on the same day each month"
                                    TaskFrequency.YEARLY -> "Repeats on the same date each year"
                                    TaskFrequency.CUSTOM -> "Repeats based on custom days interval"
                                    else -> ""
                                },
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                            )
                        }
                    }
                },
                onClick = {
                    onFrequencySelected(frequency)
                    expanded = false
                }
            )
        }
    }
}
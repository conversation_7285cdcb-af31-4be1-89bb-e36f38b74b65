#androidx.activity.ComponentActivityandroid.app.Applicationkotlin.Enum#com.taskiq.app.service.BackupResult!android.content.BroadcastReceiver!com.taskiq.app.service.AuthResult"com.taskiq.app.service.DriveResult2kotlinx.serialization.internal.GeneratedSerializer*com.taskiq.app.ui.navigation.BottomNavItemandroidx.lifecycle.ViewModel,androidx.lifecycle.ViewModelProvider.Factory$com.taskiq.app.viewmodel.BackupState#androidx.lifecycle.AndroidViewModel"com.taskiq.app.viewmodel.AuthState                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
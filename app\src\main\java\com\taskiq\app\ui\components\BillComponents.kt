package com.taskiq.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow

import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.model.BillType
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.cardTextColor
import com.taskiq.app.ui.theme.cardSecondaryTextColor
import com.taskiq.app.ui.theme.cardDescriptionTextColor
import com.taskiq.app.ui.theme.responsiveContentPadding
import com.taskiq.app.ui.theme.responsiveCardElevation
import com.taskiq.app.ui.theme.responsiveCornerRadius
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.responsiveListItemPadding
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveHorizontalPadding
import com.taskiq.app.ui.theme.responsiveSmallSpacing
import com.taskiq.app.ui.theme.responsiveSmallIconSize
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveVerticalPadding
import java.text.NumberFormat
import java.util.Currency
import java.util.Locale
import java.time.format.DateTimeFormatter
import java.time.LocalDate
import androidx.compose.foundation.interaction.MutableInteractionSource


@Composable
fun getBillTypeIcon(type: BillType): ImageVector {
    return when (type) {
        BillType.RENT -> Icons.Default.Home
        BillType.UTILITY -> Icons.Default.Phone
        BillType.CREDIT_CARD -> Icons.Default.Person
        BillType.LOAN_EMI -> Icons.Default.Info
        BillType.SUBSCRIPTION -> Icons.Default.ShoppingCart
        BillType.INSURANCE -> Icons.Default.Person
        BillType.OTHER -> Icons.Default.Info
    }
}

// Add this function to determine bill priority color based on due date
fun getBillPriorityColor(bill: Bill): Color {
    if (bill.status == BillStatus.PAID) {
        return Color(0xFF006400) // Darker green for paid bills
    }

    val today = LocalDate.now()
    val daysUntilDue = java.time.temporal.ChronoUnit.DAYS.between(today, bill.dueDate)

    return when {
        daysUntilDue < 0 -> Color.Red // Overdue
        daysUntilDue == 0L -> Color(0xFFFF6D00) // Due today (orange)
        daysUntilDue <= 3 -> Color(0xFFFFB300) // Due soon (amber)
        else -> Color(0xFF2196F3) // Due later (blue)
    }
}

// Function to get original priority color without considering paid status
fun getOriginalBillPriorityColor(bill: Bill): Color {
    val today = LocalDate.now()
    val daysUntilDue = java.time.temporal.ChronoUnit.DAYS.between(today, bill.dueDate)

    return when {
        daysUntilDue < 0 -> Color.Red // Overdue
        daysUntilDue == 0L -> Color(0xFFFF6D00) // Due today (orange)
        daysUntilDue <= 3 -> Color(0xFFFFB300) // Due soon (amber)
        else -> Color(0xFF2196F3) // Due later (blue)
    }
}

@Composable
fun BillItem(
    bill: Bill,
    onMarkAsPaid: (String) -> Unit,
    onDelete: (String) -> Unit,
    onEdit: ((Bill) -> Unit)? = null
) {
    val formatter = NumberFormat.getCurrencyInstance(Locale("en", "IN"))
    formatter.currency = Currency.getInstance("INR")
    val dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy")
    val priorityColor = getBillPriorityColor(bill)
    val originalPriorityColor = getOriginalBillPriorityColor(bill) // For icon and title
    
    // Check if bill is overdue
    val today = LocalDate.now()
    val isOverdue = bill.status == BillStatus.OVERDUE || 
                   (bill.status == BillStatus.PENDING && bill.dueDate.isBefore(today))
    
    var expanded by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = responsiveContentPadding(), vertical = responsiveContentPadding())
            .clickable(
                enabled = onEdit != null,
                onClick = { onEdit?.invoke(bill) },
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                role = null
            ),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor()
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = responsiveCardElevation(),
            pressedElevation = responsiveCardElevation(),
            focusedElevation = responsiveCardElevation(),
            hoveredElevation = responsiveCardElevation(),
            draggedElevation = responsiveCardElevation()
        ),
        shape = RoundedCornerShape(responsiveCornerRadius()),
        border = if (bill.status == BillStatus.PAID) {
            null
        } else {
            BorderStroke(
                width = 1.sdp(),
                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = FastOutSlowInEasing
                    )
                )
        ) {
            // Main bill row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(responsiveListItemPadding()),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Bill type icon
                Box(
                    modifier = Modifier
                        .size(responsiveIconSize() * 2)
                        .clip(CircleShape)
                        .background(originalPriorityColor.copy(alpha = 0.2f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = getBillTypeIcon(bill.type),
                        contentDescription = null,
                        tint = originalPriorityColor,
                        modifier = Modifier.size(responsiveIconSize())
                    )
                }

                // Bill details
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = responsiveHorizontalPadding())
                ) {
                    // Title with priority color
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(responsiveSmallSpacing())
                    ) {
                        if (isOverdue) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = "Overdue Bill",
                                tint = Color.Red,
                                modifier = Modifier.size(responsiveSmallIconSize())
                            )
                        }



                        Text(
                            text = bill.title,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            color = if (isOverdue) Color.Red else cardTextColor(),
                            textDecoration = if (bill.status == BillStatus.PAID) TextDecoration.LineThrough else TextDecoration.None
                        )
                    }

                    Spacer(modifier = Modifier.height(responsiveSmallSpacing()))

                    // Bill type, amount, and due date
                    Column {
                        // First row: Bill type and amount
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(responsiveSpacing())
                        ) {
                            Text(
                                text = bill.type.name.replace("_", " "),
                                style = MaterialTheme.typography.bodySmall,
                                color = cardSecondaryTextColor()
                            )
                            
                            Text(
                                text = "•",
                                style = MaterialTheme.typography.bodySmall,
                                color = cardSecondaryTextColor()
                            )
                            
                            Text(
                                text = formatter.format(bill.amount),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = priorityColor
                            )
                        }
                        
                        // Second row: Due date
                        Text(
                            text = "Due: ${bill.dueDate.format(dateFormatter)}",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isOverdue) Color.Red else cardSecondaryTextColor()
                        )
                    }
                }
                
                // For pending bills, show mark as paid button instead of status chip
                if (bill.status == BillStatus.PENDING) {
                    IconButton(
                        onClick = { onMarkAsPaid(bill.id) },
                        modifier = Modifier.background(Color.Transparent)
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Mark as Paid",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(responsiveIconSize())
                        )
                    }
                } else {
                    // Status chip for non-pending bills
                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(responsiveCornerRadius() * 2.5f))
                            .background(when (bill.status) {
                                BillStatus.PAID -> Color(0xFF006400).copy(alpha = 0.2f) // Darker green background
                                BillStatus.PENDING -> Color.Blue.copy(alpha = 0.2f)
                                BillStatus.OVERDUE -> Color.Red.copy(alpha = 0.2f)
                            })
                            .padding(horizontal = responsiveHorizontalPadding(), vertical = responsiveVerticalPadding())
                    ) {
                        Text(
                            text = bill.status.name.replace("_", " ").replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() },
                            color = when (bill.status) {
                                BillStatus.PAID -> Color(0xFF006400) // Darker green text
                                BillStatus.PENDING -> Color.Blue
                                BillStatus.OVERDUE -> Color.Red
                            },
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Expanded content
            AnimatedVisibility(visible = expanded) {
                Column(
                    modifier = Modifier.padding(horizontal = responsiveHorizontalPadding(), vertical = responsiveVerticalPadding())
                ) {
                    HorizontalDivider()
                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))
                    
                    // Due date or payment date
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column {
                            Text(
                                text = if (bill.status == BillStatus.PAID) "Paid On" else "Due Date",
                                style = MaterialTheme.typography.bodySmall,
                                color = cardSecondaryTextColor()
                            )
                            
                            Text(
                                text = if (bill.status == BillStatus.PAID && bill.paymentDate != null) 
                                    bill.paymentDate.format(dateFormatter) 
                                else 
                                    bill.dueDate.format(dateFormatter),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = when {
                                    bill.status == BillStatus.PAID -> Color.Green
                                    isOverdue -> Color.Red
                                    else -> cardTextColor()
                                }
                            )
                        }
                    }
                    
                    // Auto-detected indicator
                    if (bill.autoDetected == true) {
                        Spacer(modifier = Modifier.height(responsiveSpacing() * 3))
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(responsiveSpacing())
                        ) {
                            Icon(
                                imageVector = Icons.Default.Email,
                                contentDescription = "Auto-detected from Gmail",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(responsiveSmallIconSize())
                            )
                            Text(
                                text = "Auto-detected from Gmail",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }

                    // Description if available
                    if (bill.description.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(responsiveSpacing() * 3))
                        Text(
                            text = "Description",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = bill.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))
                    HorizontalDivider()

                    // Action buttons
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = responsiveVerticalPadding()),
                        horizontalArrangement = Arrangement.End
                    ) {
                        if (bill.status != BillStatus.PAID) {
                            IconButton(
                                onClick = { onMarkAsPaid(bill.id) },
                                modifier = Modifier.background(Color.Transparent)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "Mark as Paid",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(responsiveIconSize())
                                )
                            }
                        }
                        
                        // Edit button
                        if (onEdit != null) {
                            TextButton(
                                onClick = { onEdit(bill) },
                                colors = ButtonDefaults.textButtonColors(
                                    containerColor = Color.Transparent
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(responsiveSmallIconSize())
                                )
                                Spacer(modifier = Modifier.width(responsiveSmallSpacing()))
                                Text("Edit")
                            }
                        }
                        
                        // Delete button
                        TextButton(
                            onClick = { onDelete(bill.id) },
                            colors = ButtonDefaults.textButtonColors(
                                containerColor = Color.Transparent
                            )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = null,
                                tint = Color.Red,
                                modifier = Modifier.size(responsiveSmallIconSize())
                            )
                            Spacer(modifier = Modifier.width(responsiveSmallSpacing()))
                            Text("Delete", color = Color.Red)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DashboardBillItem(
    bill: Bill,
    onMarkAsPaid: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val formatter = NumberFormat.getCurrencyInstance(Locale("en", "IN"))
    formatter.currency = Currency.getInstance("INR")
    val dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy")
    val priorityColor = getBillPriorityColor(bill)
    val originalPriorityColor = getOriginalBillPriorityColor(bill) // For icon and title
    
    // Check if bill is overdue
    val today = LocalDate.now()
    val isOverdue = bill.status == BillStatus.OVERDUE || 
                   (bill.status == BillStatus.PENDING && bill.dueDate.isBefore(today))
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = responsiveContentPadding()),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor()
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = responsiveCardElevation(),
            pressedElevation = responsiveCardElevation(),
            focusedElevation = responsiveCardElevation(),
            hoveredElevation = responsiveCardElevation(),
            draggedElevation = responsiveCardElevation()
        ),
        shape = RoundedCornerShape(responsiveCornerRadius()),
        border = BorderStroke(
            width = 1.sdp(),
            color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(responsiveListItemPadding()),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Bill type icon
            Box(
                modifier = Modifier
                    .size(responsiveIconSize() * 1.6f)
                    .clip(CircleShape)
                    .background(originalPriorityColor.copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getBillTypeIcon(bill.type),
                    contentDescription = null,
                    tint = originalPriorityColor,
                    modifier = Modifier.size(responsiveIconSize())
                )
            }

            // Bill details
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = responsiveHorizontalPadding())
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(responsiveSmallSpacing())
                ) {
                    if (isOverdue) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = "Overdue Bill",
                            tint = Color.Red,
                            modifier = Modifier.size(responsiveSmallIconSize())
                        )
                    }



                    Text(
                        text = bill.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = if (isOverdue) Color.Red else cardTextColor(),
                        textDecoration = if (bill.status == BillStatus.PAID) TextDecoration.LineThrough else TextDecoration.None
                    )
                }
                
                // Amount and date row
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // Amount
                    Text(
                        text = formatter.format(bill.amount),
                        style = MaterialTheme.typography.bodyMedium,
                        color = priorityColor,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // Due date
                    Text(
                        text = "Due: ${bill.dueDate.format(dateFormatter)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isOverdue) Color.Red else cardSecondaryTextColor()
                    )
                }
            }
            
            // Mark as paid button
            IconButton(
                onClick = { onMarkAsPaid(bill.id) },
                modifier = Modifier.background(Color.Transparent)
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Mark as Paid",
                    tint = Color.Green,
                    modifier = Modifier.size(responsiveIconSize())
                )
            }
        }
    }
}
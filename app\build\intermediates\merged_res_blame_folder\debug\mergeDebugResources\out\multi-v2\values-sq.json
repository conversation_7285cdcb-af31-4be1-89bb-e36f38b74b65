{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-77:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2989e2c91f6d0a9894b01384862e89f4\\transformed\\play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5101", "endColumns": "128", "endOffsets": "5225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4dba44ce16815e024cb356c378b89b2b\\transformed\\appcompat-1.2.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,15603", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,15680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55690b61ef7b0489a32a7ec346714862\\transformed\\core-1.16.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3125,3224,3326,3424,3521,3629,3740,16071", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3219,3321,3419,3516,3624,3735,3857,16167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72b42ec2c0a6db09976b88668f84c08b\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,287,370,469,571,667,748,841,933,1023,1110,1201,1274,1363,1438,1514,1587,1664,1730", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "282,365,464,566,662,743,836,928,1018,1105,1196,1269,1358,1433,1509,1582,1659,1725,1846"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3862,3957,6609,6801,6903,8640,8721,15243,15335,15425,15512,15685,15758,15847,15922,15998,16172,16249,16315", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,88,74,75,72,76,65,120", "endOffsets": "3952,4035,6703,6898,6994,8716,8809,15330,15420,15507,15598,15753,15842,15917,15993,16066,16244,16310,16431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e866c3f6718b50411e7e0aa30260699c\\transformed\\play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4040,4147,4320,4457,4564,4725,4859,4985,5230,5400,5508,5683,5821,5983,6167,6232,6299", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "4142,4315,4452,4559,4720,4854,4980,5096,5395,5503,5678,5816,5978,6162,6227,6294,6376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dff1d73b7cef632e787de4d59cae0ad8\\transformed\\biometric-1.1.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,261,386,525,668,802,937,1081,1177,1320,1468", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "163,256,381,520,663,797,932,1076,1172,1315,1463,1584"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6381,6708,7312,7437,7576,7719,7853,7988,8132,8228,8371,8519", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "6489,6796,7432,7571,7714,7848,7983,8127,8223,8366,8514,8635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11771762b22044a889273e9ed7a93127\\transformed\\material3-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4722,4810,4893,4998,5084,5167,5266,5367,5462,5560,5648,5754,5854,5957,6085,6170,6284", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4717,4805,4888,4993,5079,5162,5261,5362,5457,5555,5643,5749,5849,5952,6080,6165,6279,6386"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8814,8933,9049,9168,9286,9388,9483,9595,9733,9849,9995,10079,10179,10271,10370,10488,10612,10717,10854,10988,11132,11321,11459,11582,11706,11832,11925,12021,12146,12287,12382,12493,12602,12741,12886,12997,13096,13173,13267,13361,13481,13569,13652,13757,13843,13926,14025,14126,14221,14319,14407,14513,14613,14716,14844,14929,15043", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "8928,9044,9163,9281,9383,9478,9590,9728,9844,9990,10074,10174,10266,10365,10483,10607,10712,10849,10983,11127,11316,11454,11577,11701,11827,11920,12016,12141,12282,12377,12488,12597,12736,12881,12992,13091,13168,13262,13356,13476,13564,13647,13752,13838,13921,14020,14121,14216,14314,14402,14508,14608,14711,14839,14924,15038,15145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45f9a3db26e33e80ab043af5a0e43024\\transformed\\browser-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6494,6999,7100,7211", "endColumns": "114,100,110,100", "endOffsets": "6604,7095,7206,7307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\396ff811c0e00aef5e8ac116f6d99809\\transformed\\material-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "15150", "endColumns": "92", "endOffsets": "15238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\75e92b145e13fc673dc76999f901f30d\\transformed\\credentials-1.5.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,122", "endOffsets": "165,288"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2801,2916", "endColumns": "114,122", "endOffsets": "2911,3034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\718231422f48010e85ca6edf2e677575\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,98", "endOffsets": "136,234,333"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3039,16436,16534", "endColumns": "85,97,98", "endOffsets": "3120,16529,16628"}}]}]}
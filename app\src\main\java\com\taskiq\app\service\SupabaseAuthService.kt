package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.config.SupabaseConfig
import com.taskiq.app.model.User
import com.taskiq.app.model.SupabaseUser
import io.github.jan.supabase.gotrue.auth
import io.github.jan.supabase.gotrue.providers.builtin.Email
import io.github.jan.supabase.postgrest.from
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Supabase authentication service
 * Handles user authentication and profile management using Supabase Auth
 */
class SupabaseAuthService(private val context: Context) {

    private val supabase = SupabaseConfig.client
    private val auth = supabase.auth

    companion object {
        private const val TAG = "SupabaseAuthService"
    }

    init {
        // Validate Supabase configuration on initialization
        if (!SupabaseConfig.isConfigured()) {
            Log.e(TAG, "Supabase is not properly configured! Check your credentials in local.properties")
            throw IllegalStateException("Supabase configuration is missing. Please check local.properties file.")
        }
        Log.d(TAG, "Supabase authentication service initialized successfully")
    }
    
    /**
     * Sign up a new user with email and password
     */
    suspend fun signUp(firstName: String, lastName: String, email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Signing up user: $email")
                
                // Create user account with Supabase Auth
                auth.signUpWith(Email) {
                    this.email = email
                    this.password = password
                }
                
                // Get the created user
                val supabaseUser = auth.currentUserOrNull()
                    ?: throw Exception("Failed to create user account")
                
                // Create user profile in database
                val userProfile = SupabaseUser(
                    id = supabaseUser.id,
                    email = email,
                    firstName = firstName,
                    lastName = lastName
                )
                
                // Insert user profile into users table
                supabase.from(SupabaseConfig.Tables.USERS).insert(userProfile)
                
                Log.d(TAG, "User signed up successfully: $email")
                
                // Return app User model
                User(
                    firstName = firstName,
                    lastName = lastName,
                    email = email,
                    isLoggedIn = true
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Sign up failed: ${e.message}", e)
                throw e
            }
        }
    }
    
    /**
     * Sign in user with email and password
     */
    suspend fun login(email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging in user: $email")
                
                // Sign in with Supabase Auth
                auth.signInWith(Email) {
                    this.email = email
                    this.password = password
                }
                
                // Get user profile from database
                val userProfile = getUserProfile()
                
                Log.d(TAG, "User logged in successfully: $email")
                
                userProfile
                
            } catch (e: Exception) {
                Log.e(TAG, "Login failed: ${e.message}", e)
                throw e
            }
        }
    }
    
    /**
     * Sign out current user
     */
    suspend fun logout() {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging out user")
                auth.signOut()
                Log.d(TAG, "User logged out successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Logout failed: ${e.message}", e)
                throw e
            }
        }
    }
    
    /**
     * Get current authenticated user
     */
    suspend fun getCurrentUser(): User? {
        return withContext(Dispatchers.IO) {
            try {
                val supabaseUser = auth.currentUserOrNull()
                if (supabaseUser != null) {
                    getUserProfile()
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting current user: ${e.message}", e)
                null
            }
        }
    }
    
    /**
     * Get current user ID for database operations
     */
    suspend fun getCurrentUserId(): String? {
        return withContext(Dispatchers.IO) {
            try {
                auth.currentUserOrNull()?.id
            } catch (e: Exception) {
                Log.e(TAG, "Error getting current user ID: ${e.message}", e)
                null
            }
        }
    }
    
    /**
     * Update user profile
     */
    suspend fun updateUserProfile(firstName: String, lastName: String): User {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUserOrNull()
                    ?: throw Exception("User is not logged in")
                
                Log.d(TAG, "Updating user profile: ${currentUser.email}")
                
                // Update user profile in database
                supabase.from(SupabaseConfig.Tables.USERS)
                    .update(
                        mapOf(
                            "firstName" to firstName,
                            "lastName" to lastName,
                            "updatedAt" to java.time.Instant.now().toString()
                        )
                    ) {
                        filter {
                            eq("id", currentUser.id)
                        }
                    }
                
                Log.d(TAG, "User profile updated successfully")
                
                // Return updated user
                User(
                    firstName = firstName,
                    lastName = lastName,
                    email = currentUser.email ?: "",
                    isLoggedIn = true
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error updating user profile: ${e.message}", e)
                throw e
            }
        }
    }
    
    /**
     * Delete user account
     */
    suspend fun deleteAccount(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val currentUser = auth.currentUserOrNull()
                    ?: throw Exception("User is not logged in")
                
                Log.d(TAG, "Deleting user account: ${currentUser.email}")
                
                // Delete user data from all tables
                deleteUserData(currentUser.id)
                
                // Delete user profile
                supabase.from(SupabaseConfig.Tables.USERS)
                    .delete {
                        filter {
                            eq("id", currentUser.id)
                        }
                    }
                
                // Sign out user
                auth.signOut()
                
                Log.d(TAG, "User account deleted successfully")
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting user account: ${e.message}", e)
                false
            }
        }
    }
    
    /**
     * Get user profile from database
     */
    private suspend fun getUserProfile(): User {
        val currentUser = auth.currentUserOrNull()
            ?: throw Exception("User is not logged in")
        
        val userProfiles = supabase.from(SupabaseConfig.Tables.USERS)
            .select()
            .decodeList<SupabaseUser>()
            .filter { it.id == currentUser.id }
        
        val userProfile = userProfiles.firstOrNull()
            ?: throw Exception("User profile not found")
        
        return User(
            firstName = userProfile.firstName ?: "",
            lastName = userProfile.lastName ?: "",
            email = userProfile.email,
            isLoggedIn = true
        )
    }
    
    /**
     * Delete all user data from database tables
     */
    private suspend fun deleteUserData(userId: String) {
        try {
            // Delete tasks
            supabase.from(SupabaseConfig.Tables.TASKS)
                .delete {
                    filter {
                        eq("userId", userId)
                    }
                }
            
            // Delete bills
            supabase.from(SupabaseConfig.Tables.BILLS)
                .delete {
                    filter {
                        eq("userId", userId)
                    }
                }
            
            // Delete important dates
            supabase.from(SupabaseConfig.Tables.IMPORTANT_DATES)
                .delete {
                    filter {
                        eq("userId", userId)
                    }
                }
            
            // Delete linked emails
            supabase.from(SupabaseConfig.Tables.LINKED_EMAILS)
                .delete {
                    filter {
                        eq("userId", userId)
                    }
                }
            
            Log.d(TAG, "User data deleted successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting user data: ${e.message}", e)
            throw e
        }
    }
}

package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.model.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * AuthService - Wrapper around SupabaseAuthService
 * Maintains compatibility with existing code while using Supabase backend
 */
class AuthService(private val context: Context) {
    // Temporarily use SharedPreferences until Supabase is properly configured
    private val sharedPrefs = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
    private var supabaseAuthService: SupabaseAuthService? = null

    init {
        try {
            // Try to initialize Supabase if properly configured
            if (com.taskiq.app.config.SupabaseConfig.SUPABASE_URL.isNotEmpty() &&
                !com.taskiq.app.config.SupabaseConfig.SUPABASE_URL.contains("your-project-ref")) {
                // supabaseAuthService = SupabaseAuthService(context)
                Log.d(TAG, "Supabase configuration detected but temporarily disabled for compilation")
            }
        } catch (e: Exception) {
            Log.w(TAG, "Supabase not available, using fallback authentication: ${e.message}")
        }
    }

    companion object {
        private const val TAG = "AuthService"
    }

    suspend fun signUp(firstName: String, lastName: String, email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Signing up user: $email")

                if (supabaseAuthService != null) {
                    // Use Supabase when available
                    supabaseAuthService!!.signUp(firstName, lastName, email, password)
                } else {
                    // Fallback: Create user locally for now
                    val userId = java.util.UUID.randomUUID().toString()
                    val user = User(
                        id = userId,
                        email = email,
                        firstName = firstName,
                        lastName = lastName
                    )

                    // Store user info locally
                    with(sharedPrefs.edit()) {
                        putString("user_id", userId)
                        putString("user_email", email)
                        putString("user_first_name", firstName)
                        putString("user_last_name", lastName)
                        putBoolean("is_logged_in", true)
                        apply()
                    }

                    Log.d(TAG, "User created locally (Supabase not configured): $email")
                    user
                }
            } catch (e: Exception) {
                Log.e(TAG, "Sign up failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun login(email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging in user: $email")

                if (supabaseAuthService != null) {
                    // Use Supabase when available
                    supabaseAuthService!!.login(email, password)
                } else {
                    // Fallback: Check local storage
                    val storedEmail = sharedPrefs.getString("user_email", "")
                    if (storedEmail == email) {
                        val user = User(
                            id = sharedPrefs.getString("user_id", "") ?: "",
                            email = email,
                            firstName = sharedPrefs.getString("user_first_name", "") ?: "",
                            lastName = sharedPrefs.getString("user_last_name", "") ?: ""
                        )

                        with(sharedPrefs.edit()) {
                            putBoolean("is_logged_in", true)
                            apply()
                        }

                        Log.d(TAG, "User logged in locally: $email")
                        user
                    } else {
                        throw Exception("User not found. Please sign up first.")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Login failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun logout() {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging out user")

                if (supabaseAuthService != null) {
                    // Use Supabase when available
                    supabaseAuthService!!.logout()
                } else {
                    // Fallback: Clear local storage
                    with(sharedPrefs.edit()) {
                        putBoolean("is_logged_in", false)
                        apply()
                    }
                    Log.d(TAG, "User logged out locally")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Logout failed: ${e.message}", e)
                throw e
            }
        }
    }

    fun getCurrentUser(): User? {
        return try {
            // For now, just use local storage fallback
            val isLoggedIn = sharedPrefs.getBoolean("is_logged_in", false)
            if (isLoggedIn) {
                User(
                    id = sharedPrefs.getString("user_id", "") ?: "",
                    email = sharedPrefs.getString("user_email", "") ?: "",
                    firstName = sharedPrefs.getString("user_first_name", "") ?: "",
                    lastName = sharedPrefs.getString("user_last_name", "") ?: ""
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current user: ${e.message}", e)
            null
        }
    }

    fun getCurrentUserId(): String? {
        return try {
            // For now, just use local storage fallback
            val isLoggedIn = sharedPrefs.getBoolean("is_logged_in", false)
            if (isLoggedIn) {
                sharedPrefs.getString("user_id", "")
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current user ID: ${e.message}", e)
            null
        }
    }

    suspend fun updatePassword(currentPassword: String, newPassword: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating password with Supabase")
                // Note: Supabase password update would require additional implementation
                // For now, return false to indicate this feature needs to be implemented
                Log.w(TAG, "Password update not yet implemented with Supabase")
                false
            } catch (e: Exception) {
                Log.e(TAG, "Password update failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun updateUserProfile(name: String, email: String, gender: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating user profile with Supabase")

                // Split name into first and last name
                val firstName = name.split(" ").firstOrNull() ?: ""
                val lastName = name.split(" ").drop(1).joinToString(" ")

                supabaseAuthService.updateUserProfile(firstName, lastName)
            } catch (e: Exception) {
                Log.e(TAG, "Profile update failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun deleteAccount(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Deleting account with Supabase")
                supabaseAuthService.deleteAccount()
            } catch (e: Exception) {
                Log.e(TAG, "Account deletion failed: ${e.message}", e)
                false
            }
        }
    }

    suspend fun resetPassword(email: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Sending password reset email with Supabase")
                // Note: Supabase password reset would require additional implementation
                // For now, return false to indicate this feature needs to be implemented
                Log.w(TAG, "Password reset not yet implemented with Supabase")
                false
            } catch (e: Exception) {
                Log.e(TAG, "Password reset failed: ${e.message}", e)
                false
            }
        }
    }
}
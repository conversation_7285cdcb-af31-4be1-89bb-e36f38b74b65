package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.model.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * AuthService - Wrapper around SupabaseAuthService
 * Maintains compatibility with existing code while using Supabase backend
 */
class AuthService(private val context: Context) {
    // Use SharedPreferences for local authentication fallback
    private val sharedPrefs = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)

    private val supabaseHttpService = try {
        SupabaseHttpService(context)
    } catch (e: Exception) {
        Log.w(TAG, "Supabase not available, using local storage fallback: ${e.message}")
        null
    }

    companion object {
        private const val TAG = "AuthService"
    }

    init {
        // Clear any old Firebase/local authentication data on service initialization
        clearOldAuthData()
    }

    private fun clearOldAuthData() {
        try {
            // Check if we have old data without proper Supabase session
            val hasOldData = sharedPrefs.getBoolean("is_logged_in", false)
            val hasSupabaseSession = sharedPrefs.contains("login_timestamp") || sharedPrefs.contains("signup_timestamp")

            if (hasOldData && !hasSupabaseSession) {
                Log.d(TAG, "Clearing old Firebase/local authentication data")
                with(sharedPrefs.edit()) {
                    clear()
                    apply()
                }
                // Also clear all app data when clearing old auth data
                clearAllAppData()
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to clear old auth data: ${e.message}")
        }
    }

    private fun clearAllAppData() {
        try {
            Log.d(TAG, "Clearing all app data for user logout/switch")

            // Clear tasks and dates data
            val taskPrefs = context.getSharedPreferences("task_prefs", Context.MODE_PRIVATE)
            with(taskPrefs.edit()) {
                clear()
                apply()
            }

            // Clear bills data
            val billPrefs = context.getSharedPreferences("bill_prefs", Context.MODE_PRIVATE)
            with(billPrefs.edit()) {
                clear()
                apply()
            }

            // Clear any other app-specific data
            val appPrefs = context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
            with(appPrefs.edit()) {
                clear()
                apply()
            }

            Log.d(TAG, "Successfully cleared all app data")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to clear app data: ${e.message}")
        }
    }

    suspend fun signUp(firstName: String, lastName: String, email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Signing up user with Supabase: $email")

                if (supabaseHttpService != null) {
                    // Use Supabase authentication only
                    val user = supabaseHttpService.signUp(firstName, lastName, email, password)

                    if (user != null) {
                        // Clear any old local data and store new session
                        with(sharedPrefs.edit()) {
                            clear() // Clear old Firebase/local data
                            putString("user_email", email)
                            putString("user_first_name", firstName)
                            putString("user_last_name", lastName)
                            putBoolean("is_logged_in", true)
                            putLong("signup_timestamp", System.currentTimeMillis())
                            apply()
                        }

                        Log.d(TAG, "User signed up successfully with Supabase: $email")
                        user
                    } else {
                        throw Exception("Failed to create account. Email may already be registered.")
                    }
                } else {
                    throw Exception("Authentication service unavailable. Please check your internet connection.")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Sign up failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun login(email: String, password: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging in user with Supabase: $email")

                if (supabaseHttpService != null) {
                    // Use Supabase authentication only
                    val user = supabaseHttpService.login(email, password)

                    if (user != null) {
                        // Clear any old local data and store new session
                        with(sharedPrefs.edit()) {
                            clear() // Clear old Firebase/local data
                            putString("user_email", user.email)
                            putString("user_first_name", user.firstName)
                            putString("user_last_name", user.lastName)
                            putBoolean("is_logged_in", true)
                            putLong("login_timestamp", System.currentTimeMillis())
                            apply()
                        }

                        Log.d(TAG, "User logged in successfully with Supabase: $email")
                        user
                    } else {
                        throw Exception("Invalid email or password. Please check your credentials.")
                    }
                } else {
                    throw Exception("Authentication service unavailable. Please check your internet connection.")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Login failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun logout() {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Logging out user from Supabase")

                if (supabaseHttpService != null) {
                    // Clear Supabase session
                    supabaseHttpService.logout()
                }

                // Clear local storage completely
                with(sharedPrefs.edit()) {
                    clear()
                    apply()
                }

                // Clear all app data to prevent data leakage between users
                clearAllAppData()

                Log.d(TAG, "User logged out successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Logout failed: ${e.message}", e)
                throw e
            }
        }
    }

    fun getCurrentUser(): User? {
        return try {
            // Always check local storage first for immediate response
            val isLoggedIn = sharedPrefs.getBoolean("is_logged_in", false)
            if (isLoggedIn) {
                User(
                    email = sharedPrefs.getString("user_email", "") ?: "",
                    firstName = sharedPrefs.getString("user_first_name", "") ?: "",
                    lastName = sharedPrefs.getString("user_last_name", "") ?: "",
                    gender = sharedPrefs.getString("user_gender", "Select") ?: "Select",
                    isLoggedIn = true
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current user: ${e.message}", e)
            null
        }
    }

    fun getCurrentUserId(): String? {
        return try {
            // For now, just use local storage fallback
            val isLoggedIn = sharedPrefs.getBoolean("is_logged_in", false)
            if (isLoggedIn) {
                sharedPrefs.getString("user_id", "")
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current user ID: ${e.message}", e)
            null
        }
    }

    suspend fun updatePassword(currentPassword: String, newPassword: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating password with Supabase")
                // Note: Supabase password update would require additional implementation
                // For now, return false to indicate this feature needs to be implemented
                Log.w(TAG, "Password update not yet implemented with Supabase")
                false
            } catch (e: Exception) {
                Log.e(TAG, "Password update failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun updateUserProfile(name: String, email: String, gender: String): User {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Updating user profile")

                // Split name into first and last name
                val firstName = name.split(" ").firstOrNull() ?: ""
                val lastName = name.split(" ").drop(1).joinToString(" ")

                // Try to update in Supabase first
                val updatedUser = supabaseHttpService?.updateUserProfile(firstName, lastName, email, gender)

                if (updatedUser != null) {
                    Log.d(TAG, "Profile updated in Supabase successfully")

                    // Update local storage
                    with(sharedPrefs.edit()) {
                        putString("user_first_name", firstName)
                        putString("user_last_name", lastName)
                        putString("user_email", email)
                        putString("user_gender", gender)
                        apply()
                    }

                    updatedUser
                } else {
                    // Fallback to local update only
                    Log.w(TAG, "Supabase update failed, updating locally only")

                    with(sharedPrefs.edit()) {
                        putString("user_first_name", firstName)
                        putString("user_last_name", lastName)
                        putString("user_email", email)
                        putString("user_gender", gender)
                        apply()
                    }

                    User(
                        email = email,
                        firstName = firstName,
                        lastName = lastName,
                        gender = gender,
                        isLoggedIn = true
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Profile update failed: ${e.message}", e)
                throw e
            }
        }
    }

    suspend fun deleteAccount(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Deleting account locally")
                // Clear all user data
                with(sharedPrefs.edit()) {
                    clear()
                    apply()
                }
                true
            } catch (e: Exception) {
                Log.e(TAG, "Account deletion failed: ${e.message}", e)
                false
            }
        }
    }

    suspend fun resetPassword(email: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Sending password reset email with Supabase")
                // Note: Supabase password reset would require additional implementation
                // For now, return false to indicate this feature needs to be implemented
                Log.w(TAG, "Password reset not yet implemented with Supabase")
                false
            } catch (e: Exception) {
                Log.e(TAG, "Password reset failed: ${e.message}", e)
                false
            }
        }
    }
}
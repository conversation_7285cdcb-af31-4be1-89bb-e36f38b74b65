com.taskiq.app-savedstate-release-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0540d7468fcc51e7f2b31af8fb65cf36\transformed\savedstate-release\res
com.taskiq.app-runtime-release-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c084ce663e3b61e4c1e4853a247c09c\transformed\runtime-release\res
com.taskiq.app-work-runtime-2.10.1-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\res
com.taskiq.app-firebase-common-21.0.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\res
com.taskiq.app-ui-text-release-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\199f88ec3ad743f2c35a8335d63315fb\transformed\ui-text-release\res
com.taskiq.app-material3-release-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1acecd73857259263b94b4bf35ef3376\transformed\material3-release\res
com.taskiq.app-savedstate-compose-release-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b26257cc7635899b9d6a93a88cd4c10\transformed\savedstate-compose-release\res
com.taskiq.app-lifecycle-viewmodel-ktx-2.9.1-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b8660eb880f8b2711f15a686737a57c\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.taskiq.app-lifecycle-livedata-core-ktx-2.9.1-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba46af5f91f3ad9b34fe3f4b6a7fd95\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.taskiq.app-datastore-release-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ca71b8a2674c54d7181a305963399bc\transformed\datastore-release\res
com.taskiq.app-lifecycle-livedata-2.9.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29e2d12f14a8342e6c010909b81c4d03\transformed\lifecycle-livedata-2.9.1\res
com.taskiq.app-play-services-auth-21.3.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\res
com.taskiq.app-core-common-2.0.3-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\res
com.taskiq.app-ui-geometry-release-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cd792feae66e603c32701664b5bfc9\transformed\ui-geometry-release\res
com.taskiq.app-sdp-android-1.1.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3aea764cac945604cf3ab6b602a3099c\transformed\sdp-android-1.1.1\res
com.taskiq.app-sqlite-2.4.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ff47a4e60effc96bd4f6c4fca4dcc2c\transformed\sqlite-2.4.0\res
com.taskiq.app-appcompat-resources-1.2.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\400e6bd31249aa26c4c216df15cabffb\transformed\appcompat-resources-1.2.0\res
com.taskiq.app-activity-ktx-1.10.1-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\486d7b41b1b73435893c0e64caf5cb8b\transformed\activity-ktx-1.10.1\res
com.taskiq.app-credentials-play-services-auth-1.5.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\res
com.taskiq.app-browser-1.4.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49d658310de5de4964d0a45e6cf7e2a2\transformed\browser-1.4.0\res
com.taskiq.app-core-1.16.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\res
com.taskiq.app-navigation-common-release-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aeab53d0ab5ed4b8be886be59417d94\transformed\navigation-common-release\res
com.taskiq.app-core-ktx-1.16.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b82a20d8ab9e467361d5c1c824617e3\transformed\core-ktx-1.16.0\res
com.taskiq.app-animation-release-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c4dfcf15834b5f1f77717b895969283\transformed\animation-release\res
com.taskiq.app-material-icons-extended-release-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4eb10a7420fe6ad59f256316d6dae013\transformed\material-icons-extended-release\res
com.taskiq.app-fragment-1.5.7-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5035e0a6201b66369960bcfd915e2dc5\transformed\fragment-1.5.7\res
com.taskiq.app-lifecycle-viewmodel-release-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\529478e4d8b6a7ccd4c1536cee8f46a5\transformed\lifecycle-viewmodel-release\res
com.taskiq.app-work-runtime-ktx-2.10.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57a6dffa5bdaf034bf66e6ce42e6cb28\transformed\work-runtime-ktx-2.10.1\res
com.taskiq.app-tracing-1.2.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58b29a996c2f04821c1936f79a37d28c\transformed\tracing-1.2.0\res
com.taskiq.app-foundation-layout-release-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a81ab1b530eaf39cf0cd390d7dc70b4\transformed\foundation-layout-release\res
com.taskiq.app-ui-graphics-release-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b102bcd17cb626f3917d24b70b43337\transformed\ui-graphics-release\res
com.taskiq.app-room-runtime-2.6.1-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\res
com.taskiq.app-play-services-base-18.5.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\res
com.taskiq.app-lifecycle-service-2.9.1-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61a3ac581b5b2a3ce46418c3e6af05e2\transformed\lifecycle-service-2.9.1\res
com.taskiq.app-datastore-preferences-release-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62551ab7441b197bd221e79ec98ac5ec\transformed\datastore-preferences-release\res
com.taskiq.app-datastore-core-release-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6849c69ac311b01d54ba1d748a4dd9d9\transformed\datastore-core-release\res
com.taskiq.app-tracing-ktx-1.2.0-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\752bfe24271c014ecb913f29e9dbaa48\transformed\tracing-ktx-1.2.0\res
com.taskiq.app-ui-unit-release-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c5a619c5856fbf03048baeb0bad39a\transformed\ui-unit-release\res
com.taskiq.app-ui-tooling-data-release-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\796519a00c292e2eda103f9d076cdb36\transformed\ui-tooling-data-release\res
com.taskiq.app-lifecycle-livedata-core-2.9.1-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c2c64db0954ba438737432ee7a45346\transformed\lifecycle-livedata-core-2.9.1\res
com.taskiq.app-activity-1.10.1-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f666c5db6b6a53b4eb945aeaa73169a\transformed\activity-1.10.1\res
com.taskiq.app-core-runtime-2.2.0-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a5783c6308f9cb53b47ca1cd5feb97\transformed\core-runtime-2.2.0\res
com.taskiq.app-lifecycle-process-2.9.1-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\res
com.taskiq.app-navigation-compose-release-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86ea420294b25904d442677540a2f2c2\transformed\navigation-compose-release\res
com.taskiq.app-lifecycle-viewmodel-savedstate-release-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89ea8fe0f1bf7f2542bdf27e39dc46e8\transformed\lifecycle-viewmodel-savedstate-release\res
com.taskiq.app-lifecycle-runtime-release-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d54d76b62491f6323e6900eda7a9066\transformed\lifecycle-runtime-release\res
com.taskiq.app-ssp-android-1.1.1-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6d7c100fc2c81a623ad484eab81a0e\transformed\ssp-android-1.1.1\res
com.taskiq.app-annotation-experimental-1.4.1-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af2ffecac7130320c871912e6677d84\transformed\annotation-experimental-1.4.1\res
com.taskiq.app-play-services-basement-18.4.0-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\res
com.taskiq.app-ui-test-manifest-1.8.2-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\res
com.taskiq.app-credentials-1.5.0-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a819a590a1c3b9b1aa788908f3957125\transformed\credentials-1.5.0\res
com.taskiq.app-foundation-release-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaaba50a6f61c3e2d02a4aaf04b349a6\transformed\foundation-release\res
com.taskiq.app-material-release-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acb9798c8ebd587acff296741d454536\transformed\material-release\res
com.taskiq.app-ui-util-release-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae1bccaa6526aee66857e62cb2bbad84\transformed\ui-util-release\res
com.taskiq.app-ui-tooling-release-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\res
com.taskiq.app-lifecycle-viewmodel-compose-release-55 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1eec2bd1fab78bf414ff0d366d35095\transformed\lifecycle-viewmodel-compose-release\res
com.taskiq.app-datastore-preferences-core-release-56 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b33df52dec7911fc70b59fa25e81e15e\transformed\datastore-preferences-core-release\res
com.taskiq.app-activity-compose-1.10.1-57 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4b4fe07d233f9df11ae5040d4b89786\transformed\activity-compose-1.10.1\res
com.taskiq.app-room-ktx-2.6.1-58 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bae5bbb3195ea11c7a4f83e40a5504d5\transformed\room-ktx-2.6.1\res
com.taskiq.app-biometric-1.1.0-59 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\res
com.taskiq.app-lifecycle-runtime-compose-release-60 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1bbb07ca9f1812743c7d762121965a1\transformed\lifecycle-runtime-compose-release\res
com.taskiq.app-runtime-saveable-release-61 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45aa18f216e45521c9549b84dca6d29\transformed\runtime-saveable-release\res
com.taskiq.app-firebase-crashlytics-19.4.4-62 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\res
com.taskiq.app-navigation-runtime-release-63 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce7818e902f96ec44af435d501959ab6\transformed\navigation-runtime-release\res
com.taskiq.app-core-viewtree-1.0.0-64 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfebf9316d0d17d44abb13963a482c8d\transformed\core-viewtree-1.0.0\res
com.taskiq.app-customview-poolingcontainer-1.0.0-65 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0f29b86586a77b8b0dccb44b168a780\transformed\customview-poolingcontainer-1.0.0\res
com.taskiq.app-savedstate-ktx-1.3.0-66 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d15501c407646cb2d2d14b196b73cfb1\transformed\savedstate-ktx-1.3.0\res
com.taskiq.app-emoji2-1.4.0-67 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\res
com.taskiq.app-graphics-path-1.0.1-68 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0ca0a170c104c10d076c52db04e97d8\transformed\graphics-path-1.0.1\res
com.taskiq.app-ui-tooling-preview-release-69 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e269597868f62679b3efa08c7a43001d\transformed\ui-tooling-preview-release\res
com.taskiq.app-appcompat-1.2.0-70 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee8ce6d4d9b244361a0641d37775e7c0\transformed\appcompat-1.2.0\res
com.taskiq.app-material-icons-core-release-71 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef69e115310717e06138e835d0060189\transformed\material-icons-core-release\res
com.taskiq.app-material-ripple-release-72 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f20a549680f10748dff71881f669775d\transformed\material-ripple-release\res
com.taskiq.app-sqlite-framework-2.4.0-73 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2f7890e1222c9b8cb8b6bf60e7bb020\transformed\sqlite-framework-2.4.0\res
com.taskiq.app-ui-release-74 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f30b1b16ade526cf11fedcbcce9e4989\transformed\ui-release\res
com.taskiq.app-startup-runtime-1.1.1-75 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f946208451562c739cc56d3adc46cf0e\transformed\startup-runtime-1.1.1\res
com.taskiq.app-profileinstaller-1.4.0-76 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\res
com.taskiq.app-animation-core-release-77 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe1bd5589aa3dbf350e1006df0882630\transformed\animation-core-release\res
com.taskiq.app-res-78 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\injectCrashlyticsMappingFileIdDebug
com.taskiq.app-pngs-79 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\pngs\debug
com.taskiq.app-res-80 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\processDebugGoogleServices
com.taskiq.app-resValues-81 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\generated\res\resValues\debug
com.taskiq.app-packageDebugResources-82 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.taskiq.app-packageDebugResources-83 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.taskiq.app-debug-84 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\build\intermediates\merged_res\debug\mergeDebugResources
com.taskiq.app-debug-85 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\debug\res
com.taskiq.app-main-86 C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\res

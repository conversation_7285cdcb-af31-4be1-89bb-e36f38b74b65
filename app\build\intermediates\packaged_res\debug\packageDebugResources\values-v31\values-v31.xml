<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.TaskIQ" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        
        <item name="android:windowSplashScreenBackground">@color/professional_blue</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
    </style>
    <style name="Theme.TaskIQ.Main" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowBackground">@color/professional_blue</item>
        <item name="android:statusBarColor">@color/professional_blue</item>
        <item name="android:navigationBarColor">@color/off_white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
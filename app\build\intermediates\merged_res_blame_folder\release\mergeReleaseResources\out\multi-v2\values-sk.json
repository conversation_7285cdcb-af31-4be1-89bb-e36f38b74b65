{"logs": [{"outputFile": "com.taskiq.app-mergeReleaseResources-79:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8694,8809,8924,9034,9149,9247,9342,9454,9589,9705,9857,9942,10043,10135,10232,10348,10470,10576,10709,10842,10976,11140,11268,11392,11522,11642,11735,11832,11953,12076,12174,12277,12386,12527,12676,12785,12885,12969,13063,13158,13274,13361,13448,13549,13629,13715,13812,13915,14008,14105,14193,14298,14395,14494,14614,14694,14796", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "8804,8919,9029,9144,9242,9337,9449,9584,9700,9852,9937,10038,10130,10227,10343,10465,10571,10704,10837,10971,11135,11263,11387,11517,11637,11730,11827,11948,12071,12169,12272,12381,12522,12671,12780,12880,12964,13058,13153,13269,13356,13443,13544,13624,13710,13807,13910,14003,14100,14188,14293,14390,14489,14609,14689,14791,14884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5051", "endColumns": "138", "endOffsets": "5185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2817,2926", "endColumns": "108,121", "endOffsets": "2921,3043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0aa768bf2c77d0f2a11c0fea0a786\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6425,6912,7015,7130", "endColumns": "106,102,114,101", "endOffsets": "6527,7010,7125,7227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14889", "endColumns": "93", "endOffsets": "14978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,15324", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,15402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,227", "endColumns": "87,83,86", "endOffsets": "138,222,309"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3048,16159,16243", "endColumns": "87,83,86", "endOffsets": "3131,16238,16325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,289,373,468,571,663,742,836,926,1007,1090,1177,1249,1339,1417,1493,1568,1646,1714", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "284,368,463,566,658,737,831,921,1002,1085,1172,1244,1334,1412,1488,1563,1641,1709,1823"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3873,3968,6532,6717,6820,8521,8600,14983,15073,15154,15237,15407,15479,15569,15647,15723,15899,15977,16045", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "3963,4047,6622,6815,6907,8595,8689,15068,15149,15232,15319,15474,15564,15642,15718,15793,15972,16040,16154"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4052,4158,4318,4442,4552,4708,4836,4949,5190,5359,5470,5640,5770,5933,6097,6165,6232", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "4153,4313,4437,4547,4703,4831,4944,5046,5354,5465,5635,5765,5928,6092,6160,6227,6314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3136,3232,3334,3435,3533,3643,3751,15798", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3227,3329,3430,3528,3638,3746,3868,15894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,378,527,663,792,927,1060,1158,1293,1426", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "156,246,373,522,658,787,922,1055,1153,1288,1421,1535"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6319,6627,7232,7359,7508,7644,7773,7908,8041,8139,8274,8407", "endColumns": "105,89,126,148,135,128,134,132,97,134,132,113", "endOffsets": "6420,6712,7354,7503,7639,7768,7903,8036,8134,8269,8402,8516"}}]}]}
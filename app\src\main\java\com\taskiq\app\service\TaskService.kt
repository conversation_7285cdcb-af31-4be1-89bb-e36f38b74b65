package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.model.Subtask
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.Calendar
import java.util.Date
import java.util.UUID
import java.util.concurrent.TimeUnit
import java.io.File

/**
 * Service to manage tasks in the application.
 * Uses Supabase for cloud storage with local fallback.
 * Maintains backward compatibility with existing code.
 */
class TaskService(private val context: Context) {

    // Authentication service
    private val authService = AuthService(context)

    // Supabase data service with fallback
    private val supabaseHttpService = try {
        SupabaseHttpService(context)
    } catch (e: Exception) {
        Log.w(TAG, "Supabase not available, using local storage fallback: ${e.message}")
        null
    }

    // In-memory storage for caching
    private val tasks = mutableListOf<Task>()
    private val importantDates = mutableListOf<Task>()

    private val gson = GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").create()

    // Keep SharedPreferences for backward compatibility and offline caching
    private val sharedPreferences = context.getSharedPreferences("task_reminder_data", Context.MODE_PRIVATE)

    companion object {
        private const val TAG = "TaskService"
        private const val KEY_TASKS = "tasks"
        private const val KEY_IMPORTANT_DATES = "important_dates"

        // Make these constants publicly accessible
        const val TASKS_KEY = "tasks"
        const val IMPORTANT_DATES_KEY = "important_dates"
    }
    
    init {
        // Only load data if user is properly authenticated with Supabase
        if (isSupabaseUser()) {
            // For Supabase users, start fresh and load from cloud
            clearLocalDataForSupabaseUser()
            Log.d(TAG, "TaskService initialized for Supabase user - loading from cloud")

            // Immediately sync from Supabase
            GlobalScope.launch {
                try {
                    syncFromSupabase()
                    Log.d(TAG, "Initial Supabase sync completed")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to sync from Supabase on init: ${e.message}", e)
                }
            }
        } else {
            // Load cached data from SharedPreferences for non-authenticated users
            loadTasksFromPrefs()
            Log.d(TAG, "TaskService initialized with ${tasks.size} cached tasks and ${importantDates.size} cached dates")
        }
    }

    private fun isSupabaseUser(): Boolean {
        val currentUser = authService.getCurrentUser()
        val hasSupabaseSession = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
            .let { prefs ->
                prefs.contains("login_timestamp") || prefs.contains("signup_timestamp")
            }
        return currentUser != null && hasSupabaseSession
    }

    private fun clearLocalDataForSupabaseUser() {
        try {
            Log.d(TAG, "Clearing old local data for Supabase user")
            tasks.clear()
            importantDates.clear()

            // Clear old local storage
            with(sharedPreferences.edit()) {
                remove(KEY_TASKS)
                remove(KEY_IMPORTANT_DATES)
                apply()
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to clear local data: ${e.message}")
        }
    }

    /**
     * Sync data from cloud storage (Supabase)
     */
    fun syncFromCloud(): Boolean {
        return try {
            Log.d(TAG, "Syncing data from Supabase cloud storage")
            if (supabaseHttpService != null) {
                // Use runBlocking to make this synchronous for compatibility
                runBlocking {
                    syncFromSupabase()
                }
            } else {
                Log.w(TAG, "Supabase not available for cloud sync")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in cloud sync: ${e.message}", e)
            false
        }
    }

    /**
     * Force save all data to cloud and sync from cloud
     */
    fun forceSaveAllData(): Boolean {
        return try {
            Log.d(TAG, "Force saving all data to Supabase and syncing from cloud")

            // Save to local storage first
            saveTasksToPrefs()

            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            // Sync all local tasks to Supabase
                            for (task in tasks) {
                                try {
                                    supabaseHttpService?.updateTask(task)
                                } catch (e: Exception) {
                                    // If update fails, try to add (might be new task)
                                    supabaseHttpService?.addTask(task)
                                }
                            }

                            // Then sync from Supabase to get latest data
                            syncFromSupabase()

                            Log.d(TAG, "Force save and sync completed")
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to force save to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not start Supabase force save: ${e.message}")
                }
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error in force save: ${e.message}", e)
            false
        }
    }

    /**
     * Sync data from Supabase to local storage
     */
    suspend fun syncFromSupabase(): Boolean {
        return try {
            if (supabaseHttpService != null) {
                Log.d(TAG, "Syncing tasks and important dates from Supabase")

                // Sync tasks
                val cloudTasks = supabaseHttpService.getTasks()
                for (cloudTask in cloudTasks) {
                    val localTaskIndex = tasks.indexOfFirst { it.id == cloudTask.id }
                    if (localTaskIndex != -1) {
                        // Update existing task if cloud version is newer
                        tasks[localTaskIndex] = cloudTask
                    } else {
                        // Add new task from cloud
                        tasks.add(cloudTask)
                    }
                }

                // Sync important dates
                val cloudDates = supabaseHttpService.getImportantDates()
                for (cloudDate in cloudDates) {
                    val localDateIndex = importantDates.indexOfFirst { it.id == cloudDate.id }
                    if (localDateIndex != -1) {
                        // Update existing date if cloud version is newer
                        importantDates[localDateIndex] = cloudDate
                    } else {
                        // Add new date from cloud
                        importantDates.add(cloudDate)
                    }
                }

                // Save merged data locally
                saveTasksToPrefs()

                Log.d(TAG, "Successfully synced ${cloudTasks.size} tasks and ${cloudDates.size} important dates from Supabase")
                true
            } else {
                Log.w(TAG, "Supabase not available for sync")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing from Supabase: ${e.message}", e)
            false
        }
    }
    
    private fun loadTasksFromPrefs() {
        val tasksJson = sharedPreferences.getString(KEY_TASKS, null)
        val datesJson = sharedPreferences.getString(KEY_IMPORTANT_DATES, null)
        
        Log.d("TaskService", "Loading tasks from SharedPreferences. Tasks JSON exists: ${tasksJson != null}, Dates JSON exists: ${datesJson != null}")
        
        if (tasksJson != null) {
            try {
                val type = object : TypeToken<List<Task>>() {}.type
                val loadedTasks = gson.fromJson<List<Task>>(tasksJson, type)
                
                // Validate the loaded tasks
                if (loadedTasks != null) {
                    tasks.clear()
                    tasks.addAll(loadedTasks)
                    Log.d("TaskService", "Successfully loaded ${tasks.size} tasks from preferences")
                    
                    // Log completion status of first few tasks for debugging
                    if (tasks.isNotEmpty()) {
                        tasks.take(3).forEach { task ->
                            Log.d("TaskService", "Loaded task: ${task.title}, isCompleted=${task.isCompleted}")
                        }
                    }
                } else {
                    Log.e("TaskService", "Error loading tasks: Deserialized tasks are null")
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Error loading tasks: ${e.message}", e)
            }
        } else {
            Log.w("TaskService", "No tasks found in SharedPreferences")
        }
        
        // Try loading dates from normal storage first
        if (datesJson != null) {
            try {
                val type = object : TypeToken<List<Task>>() {}.type
                val loadedDates = gson.fromJson<List<Task>>(datesJson, type)
                
                // Validate the loaded dates
                if (loadedDates != null) {
                    importantDates.clear()
                    importantDates.addAll(loadedDates)
                    Log.d("TaskService", "Successfully loaded ${importantDates.size} important dates from preferences")
                    
                    // Log details of first few dates for debugging
                    if (importantDates.isNotEmpty()) {
                        importantDates.take(3).forEach { date ->
                            Log.d("TaskService", "Loaded date: ${date.title}, dueDate=${date.dueDate}")
                        }
                    }
                } else {
                    Log.e("TaskService", "Error loading dates: Deserialized dates are null")
                    // Try to restore from backup files
                    tryRestoreFromBackup()
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Error loading dates: ${e.message}", e)
                // Try to restore from backup files
                tryRestoreFromBackup()
            }
        } else {
            Log.w("TaskService", "No dates found in SharedPreferences, checking backup files")
            // Try to restore from backup files
            tryRestoreFromBackup()
        }
        
        // If still no dates found, check emergency backup
        if (importantDates.isEmpty()) {
            Log.w("TaskService", "No dates found in normal storage, checking emergency backup")
            val emergencyDatesJson = sharedPreferences.getString("important_dates_emergency", null)
            
            if (emergencyDatesJson != null) {
                try {
                    val type = object : TypeToken<List<Task>>() {}.type
                    val emergencyDates = gson.fromJson<List<Task>>(emergencyDatesJson, type)
                    
                    if (emergencyDates != null && emergencyDates.isNotEmpty()) {
                        importantDates.clear()
                        importantDates.addAll(emergencyDates)
                        Log.d("TaskService", "Recovered ${emergencyDates.size} dates from emergency backup")
                        
                        // Save back to normal storage
                        sharedPreferences.edit()
                            .putString(KEY_IMPORTANT_DATES, emergencyDatesJson)
                            .commit()
                    }
                } catch (e: Exception) {
                    Log.e("TaskService", "Error loading emergency dates: ${e.message}", e)
                }
            }
        }
    }
    
    private fun saveTasksToPrefs() {
        try {
            val tasksJson = gson.toJson(tasks)
            val datesJson = gson.toJson(importantDates)
            
            Log.d("TaskService", "Saving ${tasks.size} tasks and ${importantDates.size} dates to SharedPreferences")
            
            // Use apply() for non-critical asynchronous saves
            sharedPreferences.edit()
                .putString(KEY_TASKS, tasksJson)
                .putString(KEY_IMPORTANT_DATES, datesJson)
                .apply()
            
            // Also save a backup to files for redundancy
            try {
                saveToBackupFile(tasksJson, "tasks_backup.json")
                saveToBackupFile(datesJson, "dates_backup.json")
            } catch (e: Exception) {
                Log.e("TaskService", "Error creating backup files: ${e.message}")
            }
            
            Log.d("TaskService", "Successfully saved ${tasks.size} tasks and ${importantDates.size} dates to preferences")
        } catch (e: Exception) {
            Log.e("TaskService", "Error saving tasks: ${e.message}")
        }
    }
    
    // Force save with commit for critical operations
    private fun saveTasksToPrefsImmediate(): Boolean {
        try {
            // Check for empty tasks or invalid data before converting to JSON
            if (tasks.isEmpty() && importantDates.isEmpty()) {
                Log.w("TaskService", "Warning: Attempting to save empty data collections")
            }
            
            // Count completed tasks before saving
            val completedTasksCount = tasks.count { it.isCompleted }
            
            val tasksJson = gson.toJson(tasks)
            val datesJson = gson.toJson(importantDates)
            
            // Verify JSON was generated correctly
            if (tasksJson.isBlank() && tasks.isNotEmpty()) {
                Log.e("TaskService", "ERROR: Generated tasks JSON is blank but tasks list has ${tasks.size} items!")
                return false
            }
            
            if (datesJson.isBlank() && importantDates.isNotEmpty()) {
                Log.e("TaskService", "ERROR: Generated dates JSON is blank but dates list has ${importantDates.size} items!")
                return false
            }
            
            Log.d("TaskService", "IMMEDIATE save of ${tasks.size} tasks and ${importantDates.size} dates to SharedPreferences")
            
            // Get the editor
            val editor = sharedPreferences.edit()
            
            // Add data to editor
            editor.putString(KEY_TASKS, tasksJson)
            editor.putString(KEY_IMPORTANT_DATES, datesJson)
            
            // Force immediate write to storage
            val saveSuccess = editor.commit()
            
            if (saveSuccess) {
                Log.d("TaskService", "Successfully committed data to SharedPreferences")
                
                // Verify the data was actually saved
                val savedTasksJson = sharedPreferences.getString(KEY_TASKS, null)
                val savedDatesJson = sharedPreferences.getString(KEY_IMPORTANT_DATES, null)
                
                val tasksVerified = savedTasksJson != null && savedTasksJson == tasksJson
                val datesVerified = savedDatesJson != null && savedDatesJson == datesJson
                
                if (!tasksVerified || !datesVerified) {
                    Log.e("TaskService", "Verification failed! Tasks verified: $tasksVerified, Dates verified: $datesVerified")
                    
                    // Try one more time with apply() for the one that failed
                    val retryEditor = sharedPreferences.edit()
                    
                    if (!tasksVerified) {
                        retryEditor.putString(KEY_TASKS, tasksJson)
                    }
                    
                    if (!datesVerified) {
                        retryEditor.putString(KEY_IMPORTANT_DATES, datesJson)
                    }
                    
                    // Use commit again for immediate effect
                    retryEditor.commit()
                    
                    // Create backup files as additional safety
                    saveToBackupFile(tasksJson, "tasks_backup.json")
                    saveToBackupFile(datesJson, "dates_backup.json")
                    
                    return tasksVerified && datesVerified
                }
                
                return true
            } else {
                Log.e("TaskService", "Failed to commit data to SharedPreferences")
                
                // Try alternative approach - apply() and backup files
                val retryEditor = sharedPreferences.edit()
                retryEditor.putString(KEY_TASKS, tasksJson)
                retryEditor.putString(KEY_IMPORTANT_DATES, datesJson)
                retryEditor.apply()
                
                // Create backup files
                saveToBackupFile(tasksJson, "tasks_backup.json")
                saveToBackupFile(datesJson, "dates_backup.json")
                
                return false
            }
        } catch (e: Exception) {
            Log.e("TaskService", "Exception during immediate save: ${e.message}", e)
            return false
        }
    }
    
    // Save data to backup file
    private fun saveToBackupFile(jsonData: String, fileName: String) {
        try {
            val file = File(context.filesDir, fileName)
            file.writeText(jsonData)
            
            // Create an additional copy with timestamp for critical backup
            val calendar = Calendar.getInstance()
            val timestamp = calendar.timeInMillis
            val backupFile = File(context.filesDir, "${fileName.substringBeforeLast(".")}_${timestamp}.json")
            backupFile.writeText(jsonData)
            
            // Clean up old backups (keep only the 5 most recent)
            val backupPattern = "${fileName.substringBeforeLast(".")}_*.json"
            val backupFiles = context.filesDir.listFiles { file -> 
                file.name.matches(Regex(backupPattern.replace(".", "\\.").replace("*", "\\d+"))) 
            }
            
            backupFiles?.sortByDescending { it.lastModified() }
            backupFiles?.drop(5)?.forEach { it.delete() }
            
            Log.d("TaskService", "Backup saved to file: $fileName and timestamped backup")
        } catch (e: Exception) {
            Log.e("TaskService", "Failed to save backup to file: ${e.message}")
        }
    }
    
    // Try to restore data from backup files if SharedPreferences save fails
    private fun tryRestoreFromBackup(): Boolean {
        Log.d("TaskService", "Attempting to restore from backup files")
        
        try {
            // Try standard backups first
            val tasksFile = File(context.filesDir, "tasks_backup.json")
            val datesFile = File(context.filesDir, "dates_backup.json")
            
            var tasksRestored = false
            var datesRestored = false
            
            // Try to restore tasks
            if (tasksFile.exists()) {
                val tasksJson = tasksFile.readText()
                if (tasksJson.isNotBlank()) {
                    val taskType = object : TypeToken<List<Task>>() {}.type
                    val restoredTasks = gson.fromJson<List<Task>>(tasksJson, taskType)
                    
                    if (restoredTasks != null && restoredTasks.isNotEmpty()) {
                        // Update SharedPreferences with the backup data
                        sharedPreferences.edit()
                            .putString(KEY_TASKS, tasksJson)
                            .apply()
                        
                        // Update in-memory list if needed
                        if (tasks.isEmpty() || tasks.size < restoredTasks.size) {
                            tasks.clear()
                            tasks.addAll(restoredTasks)
                            tasksRestored = true
                        }
                        
                        Log.d("TaskService", "Successfully restored ${restoredTasks.size} tasks from backup file")
                    }
                }
            }
            
            // Try to restore dates
            if (datesFile.exists()) {
                val datesJson = datesFile.readText()
                if (datesJson.isNotBlank()) {
                    val dateType = object : TypeToken<List<Task>>() {}.type
                    val restoredDates = gson.fromJson<List<Task>>(datesJson, dateType)
                    
                    if (restoredDates != null && restoredDates.isNotEmpty()) {
                        // Update SharedPreferences with the backup data
                        sharedPreferences.edit()
                            .putString(KEY_IMPORTANT_DATES, datesJson)
                            .apply()
                        
                        // Update in-memory list if needed
                        if (importantDates.isEmpty() || importantDates.size < restoredDates.size) {
                            importantDates.clear()
                            importantDates.addAll(restoredDates)
                            datesRestored = true
                        }
                        
                        Log.d("TaskService", "Successfully restored ${restoredDates.size} dates from backup file")
                    }
                }
            }
            
            // If standard backups failed, try timestamped backups
            if (!tasksRestored) {
                // Find the most recent timestamped backup
                val tasksBackupFiles = context.filesDir.listFiles { file -> 
                    file.name.matches(Regex("tasks_backup_\\d+\\.json")) 
                }
                
                if (tasksBackupFiles != null && tasksBackupFiles.isNotEmpty()) {
                    // Get the most recent backup
                    val mostRecentBackup = tasksBackupFiles.maxByOrNull { it.lastModified() }
                    if (mostRecentBackup != null) {
                        val tasksJson = mostRecentBackup.readText()
                        if (tasksJson.isNotBlank()) {
                            val taskType = object : TypeToken<List<Task>>() {}.type
                            val restoredTasks = gson.fromJson<List<Task>>(tasksJson, taskType)
                            
                            if (restoredTasks != null && restoredTasks.isNotEmpty()) {
                                // Update SharedPreferences with the backup data
                                sharedPreferences.edit()
                                    .putString(KEY_TASKS, tasksJson)
                                    .apply()
                                
                                // Update in-memory list
                                if (tasks.isEmpty() || tasks.size < restoredTasks.size) {
                                    tasks.clear()
                                    tasks.addAll(restoredTasks)
                                    tasksRestored = true
                                }
                                
                                Log.d("TaskService", "Successfully restored ${restoredTasks.size} tasks from timestamped backup")
                            }
                        }
                    }
                }
            }
            
            // Try timestamped backups for dates
            if (!datesRestored) {
                // Find the most recent timestamped backup
                val datesBackupFiles = context.filesDir.listFiles { file -> 
                    file.name.matches(Regex("dates_backup_\\d+\\.json")) 
                }
                
                if (datesBackupFiles != null && datesBackupFiles.isNotEmpty()) {
                    // Get the most recent backup
                    val mostRecentBackup = datesBackupFiles.maxByOrNull { it.lastModified() }
                    if (mostRecentBackup != null) {
                        val datesJson = mostRecentBackup.readText()
                        if (datesJson.isNotBlank()) {
                            val dateType = object : TypeToken<List<Task>>() {}.type
                            val restoredDates = gson.fromJson<List<Task>>(datesJson, dateType)
                            
                            if (restoredDates != null && restoredDates.isNotEmpty()) {
                                // Update SharedPreferences with the backup data
                                sharedPreferences.edit()
                                    .putString(KEY_IMPORTANT_DATES, datesJson)
                                    .apply()
                                
                                // Update in-memory list
                                if (importantDates.isEmpty() || importantDates.size < restoredDates.size) {
                                    importantDates.clear()
                                    importantDates.addAll(restoredDates)
                                    datesRestored = true
                                }
                                
                                Log.d("TaskService", "Successfully restored ${restoredDates.size} dates from timestamped backup")
                            }
                        }
                    }
                }
            }
            
            return tasksRestored || datesRestored
        } catch (e: Exception) {
            Log.e("TaskService", "Failed to restore from backup: ${e.message}")
            return false
        }
    }
    
    fun getTasks(): List<Task> {
        // For Supabase users, ensure we have loaded data from cloud
        if (isSupabaseUser() && tasks.isEmpty()) {
            // Trigger background sync from Supabase
            GlobalScope.launch {
                try {
                    syncFromSupabase()
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to sync tasks from Supabase: ${e.message}")
                }
            }
        }
        return tasks.toList()
    }
    
    fun getTask(taskId: String): Task? {
        return tasks.find { it.id == taskId }
    }
    
    fun addTask(task: Task): Boolean {
        return try {
            Log.d(TAG, "Adding new task: ${task.title}, isCompleted=${task.isCompleted}")

            // Add task to in-memory storage first
            tasks.add(task)

            // Save to local cache immediately
            saveTasksToPrefs()

            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            supabaseHttpService?.addTask(task)
                            Log.d(TAG, "Task synced to Supabase: ${task.title}")
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to sync task to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not start Supabase sync: ${e.message}")
                }
            }

            Log.d(TAG, "Task added and persisted: ${task.title}")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Error adding task: ${e.message}", e)
            false
        }
    }
    
    fun updateTask(task: Task): Boolean {
        return try {
            val index = tasks.indexOfFirst { it.id == task.id }
            if (index != -1) {
                // Get the original task to check for completion state changes
                val originalTask = tasks[index]
                val isCompletionChange = originalTask.isCompleted != task.isCompleted

                if (isCompletionChange) {
                    Log.d(TAG, "IMPORTANT: Detected completion state change for task: ${task.title}, from ${originalTask.isCompleted} to ${task.isCompleted}")
                }

                Log.d(TAG, "Updating task in memory: ${task.title}, isCompleted=${task.isCompleted}, completedAt=${task.completedAt}")
                tasks[index] = task

                // Save to local cache immediately
                saveTasksToPrefs()

                // Try to sync with Supabase in background
                if (supabaseHttpService != null) {
                    try {
                        GlobalScope.launch {
                            try {
                                supabaseHttpService?.updateTask(task)
                                Log.d(TAG, "Task update synced to Supabase: ${task.title}")
                            } catch (e: Exception) {
                                Log.w(TAG, "Failed to sync task update to Supabase: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Could not start Supabase sync: ${e.message}")
                    }
                }

                return true
            }

            Log.w(TAG, "Task not found for update: ${task.id}")
            false

        } catch (e: Exception) {
            Log.e(TAG, "Error updating task: ${e.message}", e)
            false
        }
    }
    
    fun deleteTask(taskId: String): Boolean {
        return try {
            val taskToDelete = tasks.find { it.id == taskId }
            if (taskToDelete == null) {
                Log.w(TAG, "Could not find task with ID $taskId for deletion")
                return false
            }

            // Log task details before deleting
            Log.d(TAG, "Deleting task: ${taskToDelete.title}, isRecurring=${taskToDelete.frequency != TaskFrequency.ONE_TIME}")

            // Remove from in-memory list
            tasks.removeIf { it.id == taskId }

            // Save to local cache immediately
            saveTasksToPrefs()

            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            supabaseHttpService?.deleteTask(taskId)
                            Log.d(TAG, "Task deletion synced to Supabase: ${taskToDelete.title}")
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to sync task deletion to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not start Supabase sync: ${e.message}")
                }
            }

            Log.d(TAG, "Task deleted and persisted: ${taskToDelete.title}")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Error deleting task: ${e.message}", e)
            false
        }
    }
    
    fun getImportantDates(): List<Task> {
        // For Supabase users, ensure we have loaded data from cloud
        if (isSupabaseUser() && importantDates.isEmpty()) {
            // Trigger background sync from Supabase
            GlobalScope.launch {
                try {
                    syncFromSupabase()
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to sync dates from Supabase: ${e.message}")
                }
            }
        }
        return importantDates.toList()
    }
    
    fun getImportantDate(dateId: String): Task? {
        return importantDates.find { it.id == dateId }
    }
    
    fun addImportantDate(importantDate: Task): Boolean {
        return try {
            Log.d(TAG, "Adding important date: ${importantDate.title}")

            // Add to in-memory list
            importantDates.add(importantDate)

            // Save to local cache immediately
            saveTasksToPrefs()

            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            // Use dedicated important dates table in Supabase
                            supabaseHttpService?.addImportantDate(importantDate)
                            Log.d(TAG, "Important date synced to Supabase: ${importantDate.title}")
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to sync important date to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Could not start Supabase sync: ${e.message}")
                }
            }

            Log.d(TAG, "Important date added and persisted: ${importantDate.title}")
            true

        } catch (e: Exception) {
            Log.e(TAG, "Error adding important date: ${e.message}", e)
            false
        }
    }
    
    // Additional emergency save method specifically for important dates
    private fun emergencySaveImportantDates() {
        try {
            Log.d("TaskService", "Performing emergency save of important dates")
            
            // Create a special file backup of all dates
            val allDatesJson = gson.toJson(importantDates)
            val allDatesFile = File(context.filesDir, "all_dates_backup.json")
            allDatesFile.writeText(allDatesJson)
            
            // Create a timestamped backup as well
            val timestamp = System.currentTimeMillis()
            val timestampedFile = File(context.filesDir, "all_dates_backup_${timestamp}.json")
            timestampedFile.writeText(allDatesJson)
            
            // Use direct SharedPreferences editor for another attempt
            sharedPreferences.edit()
                .putString("important_dates_emergency", allDatesJson)
                .putLong("important_dates_emergency_timestamp", timestamp)
                .commit()
            
            // Also save each date individually as a last resort
            for (date in importantDates) {
                try {
                    val dateJson = gson.toJson(date)
                    val dateFile = File(context.filesDir, "date_${date.id}.json")
                    dateFile.writeText(dateJson)
                } catch (e: Exception) {
                    Log.e("TaskService", "Failed to save individual date in emergency save: ${e.message}")
                }
            }
            
            Log.d("TaskService", "Emergency save of ${importantDates.size} important dates completed")
        } catch (e: Exception) {
            Log.e("TaskService", "Error during emergency save of important dates: ${e.message}")
        }
    }
    
    // New helper method to verify a date was properly saved
    private fun verifyDateWasSaved(date: Task): Boolean {
        try {
            val savedDatesJson = sharedPreferences.getString(KEY_IMPORTANT_DATES, null)
            if (savedDatesJson != null) {
                val dateType = object : TypeToken<List<Task>>() {}.type
                val savedDates = gson.fromJson<List<Task>>(savedDatesJson, dateType)
                
                // Check if our date exists in the saved list
                val dateFound = savedDates.any { it.id == date.id }
                if (dateFound) {
                    Log.d("TaskService", "Verification successful: Date ${date.title} found in saved data")
                    return true
                } else {
                    Log.e("TaskService", "Verification failed: Date ${date.title} not found in saved data")
                    
                    // Emergency backup of just this date
                    val singleDateJson = gson.toJson(date)
                    sharedPreferences.edit()
                        .putString("emergency_date_${date.id}", singleDateJson)
                        .commit()
                        
                    // Also write to a file as an extra backup
                    val emergencyFile = File(context.filesDir, "emergency_date_${date.id}.json")
                    emergencyFile.writeText(singleDateJson)
                        
                    return false
                }
            }
        } catch (e: Exception) {
            Log.e("TaskService", "Error verifying date save: ${e.message}")
        }
        return false
    }
    
    fun updateImportantDate(importantDate: Task): Boolean {
        val index = importantDates.indexOfFirst { it.id == importantDate.id }
        if (index != -1) {
            Log.d("TaskService", "Updating important date: ${importantDate.title}, isCompleted=${importantDate.isCompleted}")
            importantDates[index] = importantDate
            
            // Try multiple save methods to ensure persistence
            var saveSuccess = false
            
            // First attempt: immediate save
            try {
                saveSuccess = saveTasksToPrefsImmediate()
                
                if (saveSuccess) {
                    Log.d("TaskService", "Important date updated successfully with immediate save: ${importantDate.title}")
                } else {
                    Log.w("TaskService", "Immediate save failed for date update, trying second approach")
                    
                    // Second attempt: direct save just for dates
                    try {
                        val datesJson = gson.toJson(importantDates)
                        saveSuccess = sharedPreferences.edit()
                            .putString(KEY_IMPORTANT_DATES, datesJson)
                            .commit()
                        
                        // Also save to backup file
                        saveToBackupFile(datesJson, "dates_backup.json")
                        
                        if (saveSuccess) {
                            Log.d("TaskService", "Important date updated successfully with direct dates save: ${importantDate.title}")
                        } else {
                            Log.w("TaskService", "Direct dates update failed, trying regular save")
                            saveTasksToPrefs() // Fall back to regular save
                        }
                    } catch (e: Exception) {
                        Log.e("TaskService", "Error during direct dates update: ${e.message}")
                        saveTasksToPrefs() // Fall back to regular save
                    }
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Error during important date update: ${e.message}")
                saveTasksToPrefs() // Fall back to regular save
            }
            
            // Try to sync with Supabase in background
            if (supabaseHttpService != null) {
                try {
                    GlobalScope.launch {
                        try {
                            // For now, store important dates as tasks in Supabase
                            supabaseHttpService?.updateTask(importantDate)
                            Log.d("TaskService", "Important date update synced to Supabase as task: ${importantDate.title}")
                        } catch (e: Exception) {
                            Log.w("TaskService", "Failed to sync important date update to Supabase: ${e.message}")
                        }
                    }
                } catch (e: Exception) {
                    Log.w("TaskService", "Could not start Supabase sync: ${e.message}")
                }
            }

            // Force save to database to ensure persistence
            forceSaveToDatabase()

            // Trigger emergency save as ultimate backup
            emergencySaveImportantDates()

            return true
        }
        Log.w("TaskService", "Important date not found for update: ${importantDate.id}")
        return false
    }
    
    fun deleteImportantDate(dateId: String): Boolean {
        val dateToDelete = importantDates.find { it.id == dateId }
        if (dateToDelete == null) {
            Log.w("TaskService", "Could not find date with ID $dateId for deletion")
            return false
        }
        
        // Log date details before deleting
        Log.d("TaskService", "Deleting important date: ${dateToDelete.title}")
        
        // Remove from in-memory list
        importantDates.removeIf { it.id == dateId }
        
        // Use immediate save for deletion to ensure persistence
        val success = saveTasksToPrefsImmediate()
        
        if (!success) {
            // Fallback to regular save if immediate save fails
            Log.w("TaskService", "Immediate save failed for date deletion, trying regular save")
            saveTasksToPrefs()
        }
        
        // Try to sync with Supabase in background
        if (supabaseHttpService != null) {
            try {
                GlobalScope.launch {
                    try {
                        // For now, delete important dates as tasks in Supabase
                        supabaseHttpService?.deleteTask(dateId)
                        Log.d("TaskService", "Important date deletion synced to Supabase as task: ${dateToDelete.title}")
                    } catch (e: Exception) {
                        Log.w("TaskService", "Failed to sync important date deletion to Supabase: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.w("TaskService", "Could not start Supabase sync: ${e.message}")
            }
        }

        // Force save to ensure persistence
        forceSaveToDatabase()

        Log.d("TaskService", "Important date deleted and persisted: ${dateToDelete.title}")
        return true
    }
    
    fun getImportedDatesFromCalendar(): List<Task> {
        val personalImportantDates = mutableListOf<Task>()
        
        try {
            Log.d("TaskService", "Starting calendar import process")
            
            // Get the Google Calendar API service
            val calendarService = GoogleCalendarService(context)
            
            // Get events from the device calendar (already filtered for important events)
            val events = calendarService.getEvents()
            Log.d("TaskService", "Retrieved ${events.size} events from device calendar")

            // Process events
            events.forEach { event ->
                Log.d("TaskService", "Processing event: ${event.title}")

                val eventDate = event.startDate
                Log.d("TaskService", "Creating Task for event: ${event.title} on ${eventDate}")

                // Check if this event already exists to prevent duplicates
                val existingDate = importantDates.find { existing ->
                    existing.title.equals(event.title, ignoreCase = true) &&
                    existing.dueDate?.let { existingDate ->
                        // Check if dates are the same day
                        val cal1 = Calendar.getInstance().apply { time = existingDate }
                        val cal2 = Calendar.getInstance().apply { time = eventDate }
                        cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                        cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
                    } ?: false
                }

                if (existingDate == null) {
                    // Create a short, exact description
                    val shortDescription = when {
                        event.description?.isNotBlank() == true -> {
                            // Use first 50 characters of the original description
                            val desc = event.description!!.trim()
                            if (desc.length > 50) "${desc.take(47)}..." else desc
                        }
                        else -> "Calendar event"
                    }

                    // Create a Task from the calendar event
                    val newDate = Task(
                        id = UUID.randomUUID().toString(),
                        title = event.title,
                        description = shortDescription,
                        dueDate = eventDate,
                        frequency = TaskFrequency.ONE_TIME, // Device calendar doesn't provide recurrence info easily
                        priority = TaskPriority.HIGH,
                        isCompleted = false,
                        userId = "current_user_id",
                        createdAt = Date()
                    )

                    // Add only to the return list, not to the main importantDates list
                    personalImportantDates.add(newDate)

                    Log.d("TaskService", "Successfully added date: ${newDate.title}")
                } else {
                    Log.d("TaskService", "Skipped duplicate event: ${event.title}")
                }
            }
            
            // Save the newly added dates to persistent storage
            if (personalImportantDates.isNotEmpty()) {
                Log.d("TaskService", "Saving ${personalImportantDates.size} new dates to storage")
                
                // Use immediate save to ensure persistence
                val saveSuccess = saveTasksToPrefsImmediate()
                Log.d("TaskService", "Immediate save result: $saveSuccess")
                
                // Also create a backup of the dates
                val datesJson = gson.toJson(importantDates)
                saveToBackupFile(datesJson, "dates_backup.json")
                
                Log.d("TaskService", "Successfully imported and persisted ${personalImportantDates.size} important dates from Google Calendar")
            } else {
                Log.d("TaskService", "No important dates found in Google Calendar to import")
            }
        } catch (e: Exception) {
            Log.e("TaskService", "Error importing dates from Google Calendar: ${e.message}")
            e.printStackTrace()
        }
        
        return personalImportantDates
    }
    
    // Direct database access method - gets all tasks directly from storage
    fun getAllTasksDirectly(): List<Task> {
        val tasksJson = sharedPreferences.getString(KEY_TASKS, null)
        if (tasksJson != null) {
            try {
                val type = object : TypeToken<List<Task>>() {}.type
                val loadedTasks = gson.fromJson<List<Task>>(tasksJson, type)
                Log.d("TaskService", "Directly loaded ${loadedTasks.size} tasks from storage")
                return loadedTasks
            } catch (e: Exception) {
                Log.e("TaskService", "Error directly loading tasks: ${e.message}")
            }
        }
        return emptyList()
    }
    
    // Direct database delete method - deletes a task directly from storage
    fun deleteTaskDirectly(taskId: String): Boolean {
        val tasksJson = sharedPreferences.getString(KEY_TASKS, null) ?: return false
        
        try {
            val type = object : TypeToken<List<Task>>() {}.type
            val loadedTasks = gson.fromJson<List<Task>>(tasksJson, type).toMutableList()
            Log.d("TaskService", "Directly loaded ${loadedTasks.size} tasks to delete task: $taskId")
            
            val originalSize = loadedTasks.size
            loadedTasks.removeIf { it.id == taskId }
            
            if (loadedTasks.size < originalSize) {
                // Save the updated list back to storage
                val updatedTasksJson = gson.toJson(loadedTasks)
                sharedPreferences.edit()
                    .putString(KEY_TASKS, updatedTasksJson)
                    .apply()
                
                Log.d("TaskService", "Directly deleted task ID: $taskId from storage")
                return true
            }
        } catch (e: Exception) {
            Log.e("TaskService", "Error directly deleting task: ${e.message}")
        }
        
        return false
    }
    
    // Force save data to storage with multiple retries
    fun forceSaveToDatabase() {
        Log.d("TaskService", "Forcing save of all tasks to database - CRITICAL OPERATION")
        
        // First, ensure data integrity
        validateTaskData()
        
        // Track completed tasks before saving to verify after
        val completedTaskIds = tasks.filter { it.isCompleted }.map { it.id }
        val completedTaskCount = completedTaskIds.size
        
        Log.d("TaskService", "Before save: $completedTaskCount completed tasks with IDs: ${completedTaskIds.take(5).joinToString()}")
        
        // Multiple attempts with exponential backoff approach
        var attempt = 1
        var success = false
        
        while (!success && attempt <= 3) {
            Log.d("TaskService", "Save attempt #$attempt of 3")
            
            try {
                // Before saving, log status of completed tasks for debugging
                val completedTasks = tasks.filter { it.isCompleted }
                Log.d("TaskService", "Before save: ${completedTasks.size} completed tasks")
                completedTasks.take(3).forEach { task ->
                    Log.d("TaskService", "  Completed task: ${task.title}, ID: ${task.id}, completedAt: ${task.completedAt}")
                }
                
                // Ensure we synchronize to prevent any race conditions
                synchronized(this) {
                    success = saveTasksToPrefsImmediate()
                }
                
                if (success) {
                    // Verify data actually saved by loading it immediately
                    val savedTasksJson = sharedPreferences.getString(KEY_TASKS, null)
                    if (savedTasksJson == null || savedTasksJson.isEmpty()) {
                        Log.e("TaskService", "Data verification failed after successful save! Retrying...")
                        success = false
                    } else {
                        // Secondary validation - check the actual content
                        try {
                            val type = object : TypeToken<List<Task>>() {}.type
                            val loadedTasks = gson.fromJson<List<Task>>(savedTasksJson, type)
                            
                            if (loadedTasks == null || loadedTasks.isEmpty() && tasks.isNotEmpty()) {
                                Log.e("TaskService", "Saved tasks are empty while in-memory tasks exist! Retrying...")
                                success = false
                            } else {
                                // Verify if completed tasks stayed completed
                                val savedCompletedCount = loadedTasks.count { it.isCompleted }
                                val savedCompletedIds = loadedTasks.filter { it.isCompleted }.map { it.id }
                                
                                Log.d("TaskService", "Data verification: ${loadedTasks.size} tasks were saved with $savedCompletedCount completed tasks")
                                Log.d("TaskService", "Saved completed task IDs: ${savedCompletedIds.take(5).joinToString()}")
                                
                                // Check if any completed tasks lost their status
                                val missingCompletedTaskIds = completedTaskIds.filter { id -> 
                                    !savedCompletedIds.contains(id) 
                                }
                                
                                if (missingCompletedTaskIds.isNotEmpty()) {
                                    Log.e("TaskService", "Found ${missingCompletedTaskIds.size} completed tasks missing from saved data: ${missingCompletedTaskIds.joinToString()}")
                                    success = false
                                    
                                    // Find the tasks that weren't saved correctly and log details
                                    missingCompletedTaskIds.forEach { id ->
                                        val task = tasks.find { it.id == id }
                                        if (task != null) {
                                            Log.e("TaskService", "Missing completed task: ${task.title}, ID: ${task.id}, isCompleted: ${task.isCompleted}, completedAt: ${task.completedAt}")
                                        }
                                    }
                                    
                                    // Fix the saved data by forcing a specific update for these tasks
                                    if (loadedTasks.isNotEmpty()) {
                                        val fixedTasks = loadedTasks.toMutableList()
                                        missingCompletedTaskIds.forEach { id ->
                                            val taskInMemory = tasks.find { it.id == id }
                                            val taskIndex = fixedTasks.indexOfFirst { it.id == id }
                                            
                                            if (taskInMemory != null && taskIndex != -1) {
                                                // Replace the task with the in-memory version
                                                fixedTasks[taskIndex] = taskInMemory
                                                Log.d("TaskService", "Fixed task: ${taskInMemory.title} in saved data")
                                            }
                                        }
                                        
                                        // Save the fixed data immediately
                                        val fixedJson = gson.toJson(fixedTasks)
                                        val fixSuccess = sharedPreferences.edit()
                                            .putString(KEY_TASKS, fixedJson)
                                            .commit()
                                        
                                        Log.d("TaskService", "Fixed save attempt result: $fixSuccess")
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e("TaskService", "Data validation exception: ${e.message}")
                            success = false
                        }
                    }
                }
                
                if (!success) {
                    // Backoff delay before retry
                    val delayMs = 100L * (1 shl (attempt - 1)) // 100ms, 200ms, 400ms
                    Thread.sleep(delayMs)
                    attempt++
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Critical error during save attempt: ${e.message}", e)
                // Continue to next attempt
                attempt++
            }
        }
        
        // Final fallback
        if (!success) {
            Log.w("TaskService", "All immediate save attempts failed, falling back to regular save + direct file backup")
            
            // Regular save
            saveTasksToPrefs()
            
            // Last resort - try one more time with a delay
            Thread.sleep(200)
            try {
                // Verify in-memory state one last time
                validateTaskData()
                
                // Try one more immediate save with retry for completed tasks specifically
                synchronized(this) {
                    var finalSuccess = saveTasksToPrefsImmediate()
                    
                    if (!finalSuccess) {
                        // One last desperate attempt focused only on completed tasks
                        Log.d("TaskService", "Last attempt: Creating separate JSON just for completed tasks")
                        val completedTasksOnly = tasks.filter { it.isCompleted }
                        if (completedTasksOnly.isNotEmpty()) {
                            // Store just the completed tasks IDs separately for recovery
                            val completedTasksMap = completedTasksOnly.associate { 
                                it.id to mapOf(
                                    "completed" to true,
                                    "completedAt" to (it.completedAt?.time ?: 0),
                                    "title" to it.title
                                )
                            }
                            val backupJson = gson.toJson(completedTasksMap)
                            
                            // Store this in a separate key for emergency recovery
                            sharedPreferences.edit()
                                .putString("completed_tasks_backup", backupJson)
                                .putLong("backup_timestamp", System.currentTimeMillis())
                                .commit()
                            
                            Log.d("TaskService", "Created emergency backup of ${completedTasksOnly.size} completed tasks")
                        }
                    }
                }
                
                // Verify tasks saved properly with explicit load-back check
                val finalJson = sharedPreferences.getString(KEY_TASKS, null)
                if (finalJson != null) {
                    try {
                        val type = object : TypeToken<List<Task>>() {}.type
                        val finalLoadedTasks = gson.fromJson<List<Task>>(finalJson, type)
                        
                        // Final verification of completed tasks
                        val completedInMemory = tasks.count { it.isCompleted }
                        val completedSaved = finalLoadedTasks.count { it.isCompleted }
                        Log.d("TaskService", "Final verification: Completed in memory: $completedInMemory, Completed saved: $completedSaved")
                        
                        if (completedInMemory != completedSaved) {
                            Log.e("TaskService", "CRITICAL: Completed tasks count mismatch after all save attempts!")
                            // Add emergency intervention here if needed
                        }
                    } catch (e: Exception) {
                        Log.e("TaskService", "Final verification error: ${e.message}", e)
                    }
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Final save attempt also failed: ${e.message}", e)
            }
        }
        
        // Always log completion status of tasks for validation
        Log.d("TaskService", "After forceSaveToDatabase, tasks saved: ${tasks.size}")
        tasks.filter { it.isCompleted }.take(5).forEach { task ->
            Log.d("TaskService", "Completed task: ${task.title}, marked as completed: ${task.isCompleted}, has completedAt: ${task.completedAt != null}")
        }
        
        // Add a recovery check if we detect problems
        checkForRecoveryNeeded()
    }
    
    // Check if we need to recover any missing completed tasks
    private fun checkForRecoveryNeeded() {
        // First check the global backup
        val backupJson = sharedPreferences.getString("completed_tasks_backup", null)
        val backupTimestamp = sharedPreferences.getLong("backup_timestamp", 0)
        
        // Track if we need to save after recovery
        var needsSave = false
        
        // Only check recent backups (last 24 hours)
        if (backupJson != null && (System.currentTimeMillis() - backupTimestamp) < TimeUnit.HOURS.toMillis(24)) {
            try {
                val type = object : TypeToken<Map<String, Map<String, Any>>>() {}.type
                val backupMap = gson.fromJson<Map<String, Map<String, Any>>>(backupJson, type)
                
                if (backupMap.isNotEmpty()) {
                    // Check if any of these completed tasks are not marked as completed in the current task list
                    val backupIds = backupMap.keys.toSet()
                    val currentCompletedIds = tasks.filter { it.isCompleted }.map { it.id }.toSet()
                    
                    val missingCompletedIds = backupIds.filter { !currentCompletedIds.contains(it) }
                    
                    if (missingCompletedIds.isNotEmpty()) {
                        Log.w("TaskService", "Found ${missingCompletedIds.size} tasks in backup that should be completed but aren't in current data")
                        
                        // Update these tasks to be completed in our in-memory list
                        missingCompletedIds.forEach { id ->
                            val taskIndex = tasks.indexOfFirst { it.id == id }
                            if (taskIndex != -1) {
                                val task = tasks[taskIndex]
                                val completedAt = backupMap[id]?.get("completedAt") as? Long
                                
                                tasks[taskIndex] = task.copy(
                                    isCompleted = true,
                                    completedAt = if (completedAt != null && completedAt > 0) Date(completedAt) else Date()
                                )
                                
                                Log.d("TaskService", "Recovered completion state for task: ${task.title}")
                                needsSave = true
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("TaskService", "Error during global recovery check: ${e.message}")
            }
        }
        
        // Now check for individual task completion backups
        val allKeys = sharedPreferences.all.keys
        val taskBackupKeys = allKeys.filter { it.startsWith("task_completion_backup_") }
        
        if (taskBackupKeys.isNotEmpty()) {
            Log.d("TaskService", "Found ${taskBackupKeys.size} individual task completion backups")
            
            taskBackupKeys.forEach { key ->
                val taskId = key.removePrefix("task_completion_backup_")
                val backupTime = sharedPreferences.getLong("task_completion_backup_time_$taskId", 0)
                
                // Only process recent backups (last 24 hours)
                if (System.currentTimeMillis() - backupTime < TimeUnit.HOURS.toMillis(24)) {
                    val taskBackupJson = sharedPreferences.getString(key, null)
                    
                    if (taskBackupJson != null) {
                        try {
                            val type = object : TypeToken<Map<String, Map<String, Any>>>() {}.type
                            val backupData = gson.fromJson<Map<String, Map<String, Any>>>(taskBackupJson, type)
                            
                            if (backupData.containsKey(taskId)) {
                                val taskData = backupData[taskId]
                                val shouldBeCompleted = taskData?.get("completed") as? Boolean ?: false
                                val completedAtTime = taskData?.get("completedAt") as? Long ?: 0
                                
                                // Find the task in our current list
                                val taskIndex = tasks.indexOfFirst { it.id == taskId }
                                
                                if (taskIndex != -1) {
                                    val task = tasks[taskIndex]
                                    
                                    // If the saved state doesn't match the backup, restore from backup
                                    if (task.isCompleted != shouldBeCompleted) {
                                        Log.d("TaskService", "Recovering individual task completion for task: ${task.title}, setting isCompleted=$shouldBeCompleted")
                                        
                                        tasks[taskIndex] = task.copy(
                                            isCompleted = shouldBeCompleted,
                                            completedAt = if (shouldBeCompleted && completedAtTime > 0) Date(completedAtTime) else null
                                        )
                                        
                                        needsSave = true
                                    }
                                }
                                
                                // Clean up this backup since we've processed it
                                sharedPreferences.edit()
                                    .remove(key)
                                    .remove("task_completion_backup_time_$taskId")
                                    .apply()
                            }
                        } catch (e: Exception) {
                            Log.e("TaskService", "Error processing individual task backup: ${e.message}")
                        }
                    }
                } else {
                    // Clean up old backups
                    sharedPreferences.edit()
                        .remove(key)
                        .remove("task_completion_backup_time_$taskId")
                        .apply()
                }
            }
        }
        
        // If we recovered any tasks, save the changes
        if (needsSave) {
            Log.d("TaskService", "Saving recovered task data")
            saveTasksToPrefsImmediate()
        }
    }
    
    // Validate task data before saving to ensure integrity
    private fun validateTaskData() {
        // Ensure no invalid tasks in the list
        val invalidTasks = tasks.filterIndexed { index, task ->
            
            // Check for critical null fields that shouldn't be null
            if (task.id.isBlank()) {
                Log.e("TaskService", "Task with blank ID found: ${task.title}")
                return@filterIndexed true
            }
            
            if (task.title.isBlank()) {
                Log.e("TaskService", "Task with blank title found: ID=${task.id}")
                return@filterIndexed true
            }
            
            // Completion consistency check - if completed, should have completedAt timestamp
            if (task.isCompleted && task.completedAt == null) {
                Log.w("TaskService", "Found inconsistent task marked completed but missing completedAt timestamp: ${task.title}")
                // We won't remove it, but we'll log it
            }
            
            return@filterIndexed false
        }
        
        // Remove invalid tasks to prevent saving issues
        if (invalidTasks.isNotEmpty()) {
            Log.w("TaskService", "Removing ${invalidTasks.size} invalid tasks before saving")
            tasks.removeAll(invalidTasks)
        }
        
        // Similar validation for important dates
        val invalidDates = importantDates.filterIndexed { index, date ->
            
            if (date.id.isBlank()) {
                Log.e("TaskService", "Date with blank ID found: ${date.title}")
                return@filterIndexed true
            }
            
            if (date.title.isBlank()) {
                Log.e("TaskService", "Date with blank title found: ID=${date.id}")
                return@filterIndexed true
            }
            
            // Completion consistency check for dates
            if (date.isCompleted && date.completedAt == null) {
                Log.w("TaskService", "Found inconsistent date marked completed but missing completedAt timestamp: ${date.title}")
                // We won't remove it, but we'll log it
            }
            
            return@filterIndexed false
        }
        
        // Remove invalid dates
        if (invalidDates.isNotEmpty()) {
            Log.w("TaskService", "Removing ${invalidDates.size} invalid dates before saving")
            importantDates.removeAll(invalidDates)
        }
    }
    
    // New method for emergency data recovery if needed
    fun emergencyDataRecovery(): Boolean {
        try {
            Log.d("TaskService", "Attempting emergency data recovery")
            
            // Load from SharedPreferences again
            val tasksJson = sharedPreferences.getString(KEY_TASKS, null)
            val datesJson = sharedPreferences.getString(KEY_IMPORTANT_DATES, null)
            
            var recoverySuccessful = false
            
            if (tasksJson != null) {
                try {
                    val type = object : TypeToken<List<Task>>() {}.type
                    val loadedTasks = gson.fromJson<List<Task>>(tasksJson, type)
                    
                    if (loadedTasks != null && loadedTasks.isNotEmpty()) {
                        tasks.clear()
                        tasks.addAll(loadedTasks)
                        recoverySuccessful = true
                        Log.d("TaskService", "Successfully recovered ${tasks.size} tasks")
                    }
                } catch (e: Exception) {
                    Log.e("TaskService", "Task recovery failed: ${e.message}", e)
                }
            }
            
            if (datesJson != null) {
                try {
                    val type = object : TypeToken<List<Task>>() {}.type
                    val loadedDates = gson.fromJson<List<Task>>(datesJson, type)
                    
                    if (loadedDates != null && loadedDates.isNotEmpty()) {
                        importantDates.clear()
                        importantDates.addAll(loadedDates)
                        recoverySuccessful = true
                        Log.d("TaskService", "Successfully recovered ${importantDates.size} dates")
                    }
                } catch (e: Exception) {
                    Log.e("TaskService", "Date recovery failed: ${e.message}", e)
                }
            }
            
            return recoverySuccessful
        } catch (e: Exception) {
            Log.e("TaskService", "Emergency recovery failed: ${e.message}", e)
            return false
        }
    }
}
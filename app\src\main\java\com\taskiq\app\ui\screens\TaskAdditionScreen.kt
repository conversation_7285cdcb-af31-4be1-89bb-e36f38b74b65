package com.taskiq.app.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import java.util.*

import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.ui.components.TaskAdditionContent
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.ssp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskAdditionScreen(
    onDismiss: () -> Unit,
    onAddTask: (Task) -> Unit,
    taskViewModel: TaskViewModel,
    editingTask: Task? = null,
    onShowDuplicateWarning: (Task) -> Unit = { /* Default no-op implementation */ }
) {
    // Form field states with proper initialization
    var title by remember(editingTask) { mutableStateOf(editingTask?.title ?: "") }
    var description by remember(editingTask) { mutableStateOf(editingTask?.description ?: "") }
    var dueDate by remember(editingTask) { mutableStateOf(editingTask?.dueDate ?: Date(System.currentTimeMillis() + 86400000)) }
    var priority by remember(editingTask) { mutableStateOf(editingTask?.priority ?: TaskPriority.MEDIUM) }
    var frequency by remember(editingTask) { mutableStateOf(editingTask?.frequency ?: TaskFrequency.ONE_TIME) }
    var customDays by remember(editingTask) { mutableStateOf(editingTask?.customFrequencyDays?.toString() ?: "28") }
    var customHours by remember(editingTask) { mutableStateOf(editingTask?.customFrequencyHours?.toString() ?: "0") }
    var hasNoDueDate by remember(editingTask) { mutableStateOf(editingTask?.dueDate == null && editingTask != null) }
    var isTemplate by remember(editingTask) { mutableStateOf(editingTask?.isTemplate ?: false) }
    var reminderTime by remember(editingTask) { mutableStateOf(editingTask?.reminderTime) }
    
    // UI state
    var showDatePicker by remember { mutableStateOf(false) }
    var showTimePicker by remember { mutableStateOf(false) }
    var showPriorityMenu by remember { mutableStateOf(false) }
    var showFrequencyMenu by remember { mutableStateOf(false) }
    
    // Duplicate warning state
    var showDuplicateWarning by remember { mutableStateOf(false) }

    // Handle system back button
    BackHandler {
        onDismiss()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when {
                            editingTask != null && editingTask.isTemplate -> "Edit Template"
                            editingTask != null -> "Edit Task"
                            isTemplate -> "Create Template"
                            else -> "Add Task"
                        },
                        fontWeight = FontWeight.Bold,
                        fontSize = 17.ssp(),
                        color = ProfessionalWhite
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.padding(end = 2.dp)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = ProfessionalWhite
                        )
                    }
                },
                actions = {
                    if (editingTask != null) {
                        // Add share icon first (left of delete icon)
                        val context = LocalContext.current
                        IconButton(
                            onClick = {
                                // Create a share intent
                                val sendIntent = android.content.Intent().apply {
                                    action = android.content.Intent.ACTION_SEND
                                    putExtra(android.content.Intent.EXTRA_SUBJECT, "Task: ${editingTask.title}")
                                    
                                    // Format task details
                                    val shareText = buildString {
                                        append("Task: ${editingTask.title}\n")
                                        if (editingTask.description.isNotBlank()) {
                                            append("Description: ${editingTask.description}\n")
                                        }
                                        editingTask.dueDate?.let {
                                            val dateFormat = java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.getDefault())
                                            append("Due Date: ${dateFormat.format(it)}\n")
                                        }
                                        append("Priority: ${editingTask.priority}\n")
                                        append("Status: ${if (editingTask.isCompleted) "Completed" else "Pending"}\n")
                                        
                                        if (editingTask.subtasks.isNotEmpty()) {
                                            append("\nSubtasks:\n")
                                            editingTask.subtasks.forEach { subtask ->
                                                append("• ${subtask.title} - ${if (subtask.isCompleted) "Done" else "Pending"}\n")
                                            }
                                        }
                                    }
                                    
                                    putExtra(android.content.Intent.EXTRA_TEXT, shareText)
                                    type = "text/plain"
                                }
                                
                                // Create chooser
                                val shareIntent = android.content.Intent.createChooser(sendIntent, "Share Task Details")
                                context.startActivity(shareIntent)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "Share Task",
                                tint = ProfessionalWhite // Use the same color as other icons for consistency
                            )
                        }
                        
                        // Delete icon
                        IconButton(
                            onClick = {
                                taskViewModel.deleteTask(editingTask.id)
                                onDismiss()
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "Delete Task",
                                tint = ProfessionalWhite
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite,
                    actionIconContentColor = MaterialTheme.colorScheme.error
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 12.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // Initial spacing
            Spacer(modifier = Modifier.height(12.dp))

            // Reuse the existing AddTaskDialog content without the dialog wrapper
            TaskAdditionContent(
                title = title,
                onTitleChange = { title = it },
                description = description,
                onDescriptionChange = { description = it },
                dueDate = dueDate,
                onDueDateChange = { dueDate = it },
                priority = priority,
                onPriorityChange = { priority = it },
                frequency = frequency,
                onFrequencyChange = { frequency = it },
                customDays = customDays,
                onCustomDaysChange = { customDays = it.trim() },
                customHours = customHours,
                onCustomHoursChange = { customHours = it.trim() },
                hasNoDueDate = hasNoDueDate,
                onHasNoDueDateChange = { hasNoDueDate = it },
                isTemplate = isTemplate,
                onIsTemplateChange = { isTemplate = it },
                showDatePicker = showDatePicker,
                onShowDatePickerChange = { showDatePicker = it },
                showTimePicker = showTimePicker,
                onShowTimePickerChange = { showTimePicker = it },
                showPriorityMenu = showPriorityMenu,
                onShowPriorityMenuChange = { showPriorityMenu = it },
                showFrequencyMenu = showFrequencyMenu,
                onShowFrequencyMenuChange = { showFrequencyMenu = it },

                onSave = {
                    val newTask = Task(
                        id = editingTask?.id ?: UUID.randomUUID().toString(),
                        title = title,
                        description = description,
                        dueDate = if (hasNoDueDate) null else dueDate,
                        frequency = frequency,
                        priority = priority,
                        customFrequencyDays = if (frequency == TaskFrequency.CUSTOM) customDays.toIntOrNull() ?: 28 else null,
                        customFrequencyHours = if (frequency == TaskFrequency.CUSTOM) customHours.toIntOrNull() ?: 0 else null,
                        isCompleted = editingTask?.isCompleted ?: false,
                        subtasks = editingTask?.subtasks ?: emptyList(),
                        createdAt = editingTask?.createdAt ?: Date(),
                        isTemplate = isTemplate,
                        completedAt = editingTask?.completedAt,
                        reminderTime = reminderTime,
                        userId = "user123" // Default user ID for now
                    )
                    
                    // Check for duplicate first if a new task
                    if (editingTask == null && !isTemplate && taskViewModel.isDuplicateTask(newTask)) {
                        onShowDuplicateWarning(newTask)
                    } else {
                        onAddTask(newTask)
                    }
                },
                onDismiss = onDismiss
            )
            
            // Show duplicate warning message if needed
            if (showDuplicateWarning) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "A task with this title already exists. Please use a different title.",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}
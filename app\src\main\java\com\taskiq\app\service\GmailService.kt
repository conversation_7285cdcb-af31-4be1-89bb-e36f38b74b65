package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.gmail.Gmail
import com.google.api.services.gmail.model.ListMessagesResponse
import com.google.api.services.gmail.model.Message
import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillType
import com.taskiq.app.model.BillStatus
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.regex.Pattern
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Base64

/**
 * Service for handling real Gmail API operations
 */
class GmailService(
    private val context: Context,
    private val credential: GoogleAccountCredential
) {
    private val TAG = "GmailService"
    
    private val gmailService: Gmail by lazy {
        Gmail.Builder(
            com.google.api.client.http.javanet.NetHttpTransport(),
            GsonFactory.getDefaultInstance(),
            credential
        )
        .setApplicationName("Task Reminder")
        .build()
    }
    
    /**
     * Scan emails for bills using real Gmail API
     */
    suspend fun scanEmailsForBills(): List<Bill> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== STARTING REAL GMAIL API SCAN FOR BILLS ===")
            Log.d(TAG, "Gmail service instance: ${gmailService.javaClass.simpleName}")

            val detectedBills = mutableListOf<Bill>()

            // Search for bill-related emails in the last 30 days (increased from 7 days)
            val query = buildBillSearchQuery()
            Log.d(TAG, "Using Gmail search query: $query")
            Log.d(TAG, "Searching emails from last 30 days...")

            Log.d(TAG, "Executing Gmail API request...")
            val listResponse: ListMessagesResponse = gmailService.users().messages()
                .list("me")
                .setQ(query)
                .setMaxResults(50)
                .execute()
            Log.d(TAG, "Gmail API request completed successfully")
            
            var messages = listResponse.messages ?: emptyList()
            Log.d(TAG, "=== GMAIL API SEARCH RESULTS ===")
            Log.d(TAG, "Found ${messages.size} potential bill emails")

            if (messages.isEmpty()) {
                Log.w(TAG, "No emails found matching bill criteria in the last 7 days")
                Log.w(TAG, "Trying broader search for any emails with financial keywords...")

                // Try a broader search for the last 7 days
                val broadQuery = "subject:(bill OR payment OR due OR invoice OR statement OR credit OR loan OR EMI OR amount OR balance) after:${LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))}"
                Log.d(TAG, "Broad search query: $broadQuery")

                val broadResponse = gmailService.users().messages()
                    .list("me")
                    .setQ(broadQuery)
                    .setMaxResults(30)
                    .execute()

                val broadMessages = broadResponse.messages ?: emptyList()
                Log.d(TAG, "Broad search found ${broadMessages.size} emails")

                if (broadMessages.isEmpty()) {
                    Log.w(TAG, "No emails found even with broad search")
                    Log.w(TAG, "Trying final search for any recent emails...")

                    // Final attempt - search for any emails in the last 7 days
                    val finalQuery = "after:${LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))}"
                    Log.d(TAG, "Final search query: $finalQuery")

                    val finalResponse = gmailService.users().messages()
                        .list("me")
                        .setQ(finalQuery)
                        .setMaxResults(10)
                        .execute()

                    val finalMessages = finalResponse.messages ?: emptyList()
                    Log.d(TAG, "Final search found ${finalMessages.size} total emails in last 7 days")

                    if (finalMessages.isEmpty()) {
                        Log.w(TAG, "No emails found at all in the last 7 days")
                        return@withContext emptyList()
                    } else {
                        messages = finalMessages
                        Log.d(TAG, "Using final search results: ${messages.size} emails")
                    }
                } else {
                    // Use the broad search results
                    messages = broadMessages
                    Log.d(TAG, "Using broad search results: ${messages.size} emails")
                }
            }
            
            // Process each message
            for (messageRef in messages.take(20)) { // Limit to 20 messages to avoid rate limits
                try {
                    val message: Message = gmailService.users().messages()
                        .get("me", messageRef.id)
                        .execute()
                    
                    val bill = parseEmailForBill(message)
                    if (bill != null) {
                        detectedBills.add(bill)
                        Log.d(TAG, "Detected bill: ${bill.title} - $${bill.amount}")
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Error processing message ${messageRef.id}: ${e.message}")
                }
            }
            
            Log.d(TAG, "=== GMAIL SCAN COMPLETED ===")
            Log.d(TAG, "Successfully detected ${detectedBills.size} bills from Gmail")

            if (detectedBills.isNotEmpty()) {
                detectedBills.forEach { bill ->
                    Log.d(TAG, "Detected bill: ${bill.title} - $${bill.amount} due ${bill.dueDate}")
                }
            }

            return@withContext detectedBills

        } catch (e: Exception) {
            Log.e(TAG, "=== GMAIL SCAN ERROR ===")
            Log.e(TAG, "Error scanning Gmail for bills: ${e.message}", e)
            Log.e(TAG, "Stack trace: ${e.stackTrace.joinToString("\n")}")
            return@withContext emptyList()
        }
    }
    
    /**
     * Build search query for bill-related emails in the last 30 days
     */
    private fun buildBillSearchQuery(): String {
        val thirtyDaysAgo = LocalDate.now().minusDays(30)
        val dateString = thirtyDaysAgo.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))

        Log.d(TAG, "Building search query for emails after: $dateString (last 30 days)")

        val query = """
            (
                (
                    (from:billing OR from:statements OR from:invoice OR from:noreply) AND
                    (subject:bill OR subject:invoice OR subject:statement OR subject:"amount due" OR subject:"payment due" OR subject:EMI OR subject:loan OR subject:overdue OR subject:"final notice")
                ) OR
                (
                    (from:bank OR from:credit) AND
                    (subject:statement OR subject:"amount due" OR subject:"payment due" OR subject:"minimum payment" OR subject:overdue)
                ) OR
                (
                    (subject:"electricity bill" OR subject:"gas bill" OR subject:"water bill" OR subject:"internet bill" OR subject:"phone bill") AND
                    (body:"amount due" OR body:"payment due" OR body:"bill amount")
                ) OR
                (
                    has:attachment AND
                    (subject:bill OR subject:invoice OR subject:statement) AND
                    (body:"amount due" OR body:"payment due" OR body:"outstanding balance")
                )
            ) AND after:$dateString AND
            -subject:"transaction successful" AND -subject:"payment successful" AND -subject:"payment completed" AND
            -subject:"payment received" AND -subject:"transaction confirmed" AND -subject:"payment confirmed" AND
            -subject:receipt AND -subject:confirmation AND -subject:"thank you for your payment" AND
            -subject:"payment processed" AND -subject:"order confirmed" AND -subject:"booking confirmed" AND
            -from:paytm AND -from:phonepe AND -from:googlepay AND -from:amazonpay AND -from:razorpay AND
            -from:amazon AND -from:flipkart AND -from:zomato AND -from:swiggy AND -from:uber AND -from:ola
        """.trimIndent().replace("\n", " ")

        Log.d(TAG, "Final search query: $query")
        return query
    }
    
    /**
     * Parse email message to extract bill information
     */
    private fun parseEmailForBill(message: Message): Bill? {
        try {
            val headers = message.payload?.headers ?: return null

            // Extract basic email info
            val subject = headers.find { it.name.equals("Subject", ignoreCase = true) }?.value ?: ""
            val from = headers.find { it.name.equals("From", ignoreCase = true) }?.value ?: ""
            val date = headers.find { it.name.equals("Date", ignoreCase = true) }?.value ?: ""

            // Get email body
            val body = extractEmailBody(message)

            Log.d(TAG, "Parsing email - Subject: $subject, From: $from")

            // First, validate if this is actually a bill (not a transaction confirmation)
            if (!isActualBill(subject, from, body)) {
                Log.d(TAG, "Skipping email - not a bill (likely transaction confirmation or receipt)")
                return null
            }

            // Extract bill information
            val billTitle = extractBillTitle(subject, from, body)
            val amount = extractAmount(subject, body)
            val dueDate = extractDueDate(subject, body)
            val billType = determineBillType(subject, from, body)

            // Create bill only if we have a proper title and it passes validation
            if (billTitle.isNotEmpty()) {
                val finalAmount = if (amount > 0) amount else 0.0

                Log.d(TAG, "Creating bill: $billTitle, amount: $finalAmount, type: $billType")

                return Bill(
                    id = UUID.randomUUID().toString(),
                    title = billTitle,
                    amount = finalAmount,
                    dueDate = dueDate ?: LocalDate.now().plusDays(30),
                    type = billType,
                    description = "Auto-detected from email: ${from.take(50)}",
                    status = BillStatus.PENDING,
                    userId = "current_user_id",
                    createdAt = LocalDate.now(),
                    autoDetected = true
                )
            } else {
                Log.d(TAG, "Skipping email - no recognizable bill title found")
            }

            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing email for bill: ${e.message}")
            return null
        }
    }
    
    /**
     * Extract email body text
     */
    private fun extractEmailBody(message: Message): String {
        return try {
            val payload = message.payload
            when {
                payload?.body?.data != null -> {
                    String(Base64.getDecoder().decode(payload.body.data))
                }
                payload?.parts != null -> {
                    payload.parts.mapNotNull { part ->
                        part.body?.data?.let { data ->
                            String(Base64.getDecoder().decode(data))
                        }
                    }.joinToString(" ")
                }
                else -> ""
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error extracting email body: ${e.message}")
            ""
        }
    }
    
    /**
     * Extract bill title from subject and sender with account details
     */
    private fun extractBillTitle(subject: String, from: String, body: String): String {
        // Extract company name from email address
        val emailPattern = Pattern.compile("([^@<]+)@([^>]+)")
        val matcher = emailPattern.matcher(from)

        val companyName = if (matcher.find()) {
            val domain = matcher.group(2)?.split(".")?.let { parts ->
                // Get the main domain (before .com, .org, etc.)
                if (parts.size >= 2) parts[parts.size - 2] else parts.firstOrNull()
            } ?: ""
            domain.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
        } else {
            // Fallback: extract name from the sender field
            from.split("@").firstOrNull()?.split("<")?.firstOrNull()?.trim() ?: "Unknown"
        }

        // Extract account details from subject and body
        val accountDetails = extractAccountDetails(subject, body)

        // More aggressive bill type detection with account details
        val lowerSubject = subject.lowercase()
        val lowerFrom = from.lowercase()
        val content = "$subject $body".lowercase()

        return when {
            lowerSubject.contains("credit card") || lowerSubject.contains("card statement") -> {
                val cardNumber = extractCreditCardNumber(content)
                if (cardNumber.isNotEmpty()) {
                    "$companyName Credit Card ****$cardNumber"
                } else {
                    // Try to extract from generic account details
                    if (accountDetails.isNotEmpty()) "$companyName Credit Card $accountDetails" else "$companyName Credit Card"
                }
            }
            lowerSubject.contains("loan") || lowerSubject.contains("emi") || lowerSubject.contains("mortgage") -> {
                val loanAccount = extractLoanAccountNumber(content)
                if (loanAccount.isNotEmpty()) {
                    "$companyName Loan EMI $loanAccount"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Loan EMI $accountDetails" else "$companyName Loan EMI"
                }
            }
            lowerSubject.contains("electric") || lowerSubject.contains("electricity") -> {
                val accountNumber = extractUtilityAccountNumber(content, "electric")
                if (accountNumber.isNotEmpty()) {
                    "$companyName Electricity $accountNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Electricity $accountDetails" else "$companyName Electricity Bill"
                }
            }
            lowerSubject.contains("gas") -> {
                val accountNumber = extractUtilityAccountNumber(content, "gas")
                if (accountNumber.isNotEmpty()) {
                    "$companyName Gas $accountNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Gas $accountDetails" else "$companyName Gas Bill"
                }
            }
            lowerSubject.contains("water") -> {
                val accountNumber = extractUtilityAccountNumber(content, "water")
                if (accountNumber.isNotEmpty()) {
                    "$companyName Water $accountNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Water $accountDetails" else "$companyName Water Bill"
                }
            }
            lowerSubject.contains("internet") || lowerSubject.contains("broadband") -> {
                val accountNumber = extractUtilityAccountNumber(content, "internet")
                if (accountNumber.isNotEmpty()) {
                    "$companyName Internet $accountNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Internet $accountDetails" else "$companyName Internet Bill"
                }
            }
            lowerSubject.contains("phone") || lowerSubject.contains("mobile") -> {
                val phoneNumber = extractPhoneNumber(content)
                if (phoneNumber.isNotEmpty()) {
                    "$companyName Mobile $phoneNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Mobile $accountDetails" else "$companyName Mobile Bill"
                }
            }
            lowerSubject.contains("insurance") -> {
                val policyNumber = extractPolicyNumber(content)
                if (policyNumber.isNotEmpty()) {
                    "$companyName Insurance $policyNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Insurance $accountDetails" else "$companyName Insurance"
                }
            }
            lowerSubject.contains("statement") && (lowerFrom.contains("bank") || lowerFrom.contains("credit")) -> {
                val accountNumber = extractBankAccountNumber(content)
                if (accountNumber.isNotEmpty()) {
                    "$companyName Statement $accountNumber"
                } else {
                    if (accountDetails.isNotEmpty()) "$companyName Statement $accountDetails" else "$companyName Statement"
                }
            }
            else -> {
                // For other bill types, try to extract specific account details
                val specificAccount = when {
                    content.contains("credit") -> extractCreditCardNumber(content)
                    content.contains("loan") || content.contains("emi") -> extractLoanAccountNumber(content)
                    content.contains("bank") -> extractBankAccountNumber(content)
                    else -> ""
                }

                if (specificAccount.isNotEmpty()) {
                    "$companyName Bill $specificAccount"
                } else if (accountDetails.isNotEmpty()) {
                    "$companyName Bill $accountDetails"
                } else {
                    // Only create generic bill if it has clear bill indicators
                    if (lowerSubject.contains("bill") || lowerSubject.contains("invoice") ||
                        lowerSubject.contains("amount due") || lowerSubject.contains("payment due")) {
                        "$companyName Bill"
                    } else {
                        "" // Return empty if it doesn't clearly seem like a bill
                    }
                }
            }
        }
    }
    
    /**
     * Extract amount from email content with improved accuracy
     */
    private fun extractAmount(subject: String, body: String): Double {
        val content = "$subject $body"

        // Priority patterns - more specific patterns first
        val priorityPatterns = listOf(
            // Specific amount labels
            Pattern.compile("amount\\s+due[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("total\\s+amount[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("payment\\s+amount[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("bill\\s+amount[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("outstanding\\s+balance[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("minimum\\s+payment[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE),
            Pattern.compile("current\\s+balance[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)", Pattern.CASE_INSENSITIVE)
        )

        // Try priority patterns first
        for (pattern in priorityPatterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                try {
                    val amountStr = matcher.group(1)?.replace(",", "") ?: "0"
                    val amount = amountStr.toDouble()
                    if (amount > 0 && amount < 1000000) {
                        Log.d(TAG, "Found amount using priority pattern: $amount")
                        return amount
                    }
                } catch (e: NumberFormatException) {
                    continue
                }
            }
        }

        // Common amount patterns (including Indian Rupee formats)
        val patterns = listOf(
            Pattern.compile("₹\\s*([0-9,]+\\.?[0-9]*)"),                  // ₹123.45
            Pattern.compile("INR\\s*([0-9,]+\\.?[0-9]*)"),                // INR 123.45
            Pattern.compile("Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"),             // Rs. 123.45
            Pattern.compile("\\$([0-9,]+\\.?[0-9]*)"),                    // $123.45
            Pattern.compile("amount[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"), // amount: ₹123.45
            Pattern.compile("due[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"),    // due: ₹123.45
            Pattern.compile("total[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"),  // total: ₹123.45
            Pattern.compile("balance[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"), // balance: ₹123.45
            Pattern.compile("payment[:\\s]*\\$?₹?INR?Rs\\.?\\s*([0-9,]+\\.?[0-9]*)"), // payment: ₹123.45
            Pattern.compile("([0-9,]+\\.?[0-9]*)\\s*(?:INR|Rs\\.?|₹)")     // 123.45 INR
        )

        // Try regular patterns
        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                try {
                    val amountStr = matcher.group(1)?.replace(",", "") ?: "0"
                    val amount = amountStr.toDouble()
                    if (amount > 0 && amount < 1000000) { // Reasonable bill amount (increased for INR)
                        Log.d(TAG, "Found amount using regular pattern: $amount")
                        return amount
                    }
                } catch (e: NumberFormatException) {
                    continue
                }
            }
        }

        Log.d(TAG, "No amount found in email content")
        return 0.0
    }
    
    /**
     * Extract due date from email content with improved parsing
     */
    private fun extractDueDate(subject: String, body: String): LocalDate? {
        val content = "$subject $body"

        // Look for due date patterns with improved parsing
        val patterns = listOf(
            // Date patterns like "due on January 15, 2024" or "due January 15, 2024"
            Pattern.compile("due\\s+(?:on\\s+)?([A-Za-z]+\\s+\\d{1,2},?\\s+\\d{4})", Pattern.CASE_INSENSITIVE),
            // Date patterns like "due 01/15/2024" or "due date 01/15/2024"
            Pattern.compile("due\\s+(?:date\\s+)?([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})", Pattern.CASE_INSENSITIVE),
            // Date patterns like "payment due January 15, 2024"
            Pattern.compile("payment\\s+due\\s+([A-Za-z]+\\s+\\d{1,2},?\\s+\\d{4})", Pattern.CASE_INSENSITIVE),
            // Date patterns like "due by 15-01-2024" or "due by 15/01/2024"
            Pattern.compile("due\\s+by\\s+([0-9]{1,2}[-/][0-9]{1,2}[-/][0-9]{4})", Pattern.CASE_INSENSITIVE),
            // Date patterns like "payment date: 15 Jan 2024"
            Pattern.compile("payment\\s+date[:\\s]+([0-9]{1,2}\\s+[A-Za-z]+\\s+\\d{4})", Pattern.CASE_INSENSITIVE),
            // Date patterns like "next payment: 15/01/2024"
            Pattern.compile("next\\s+payment[:\\s]+([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                try {
                    val dateStr = matcher.group(1)
                    Log.d(TAG, "Found date string: $dateStr")

                    // Try to parse the date string
                    val parsedDate = parseDateString(dateStr)
                    if (parsedDate != null) {
                        Log.d(TAG, "Successfully parsed date: $parsedDate")
                        return parsedDate
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Error parsing date: ${e.message}")
                    continue
                }
            }
        }

        // If no specific date found, look for relative dates like "in 15 days"
        val relativeDatePattern = Pattern.compile("(?:in|within)\\s+(\\d+)\\s+days?", Pattern.CASE_INSENSITIVE)
        val relativeMatcher = relativeDatePattern.matcher(content)
        if (relativeMatcher.find()) {
            try {
                val days = relativeMatcher.group(1)?.toInt() ?: 15
                return LocalDate.now().plusDays(days.toLong())
            } catch (e: Exception) {
                Log.w(TAG, "Error parsing relative date: ${e.message}")
            }
        }

        // Default to 30 days from now if no date found
        return LocalDate.now().plusDays(30)
    }
    
    /**
     * Validate if email represents an actual bill (not a transaction confirmation)
     */
    private fun isActualBill(subject: String, from: String, body: String): Boolean {
        val content = "$subject $body".lowercase()
        val lowerSubject = subject.lowercase()
        val lowerFrom = from.lowercase()

        // Exclude transaction confirmations and receipts
        val excludePatterns = listOf(
            "transaction successful", "payment successful", "payment completed", "payment received",
            "transaction confirmed", "payment confirmed", "receipt", "confirmation",
            "thank you for your payment", "payment processed", "transaction processed",
            "money transferred", "transfer successful", "deposit successful",
            "withdrawal successful", "purchase successful", "order confirmed",
            "booking confirmed", "ticket confirmed", "refund processed",
            "cashback credited", "reward points", "points credited"
        )

        // Check if email contains exclusion patterns
        for (pattern in excludePatterns) {
            if (content.contains(pattern)) {
                Log.d(TAG, "Email excluded - contains pattern: $pattern")
                return false
            }
        }

        // Exclude emails from payment processors and e-commerce
        val excludeSenders = listOf(
            "paytm", "phonepe", "googlepay", "amazonpay", "razorpay", "paypal",
            "amazon", "flipkart", "myntra", "zomato", "swiggy", "uber", "ola",
            "bookmyshow", "makemytrip", "goibibo", "irctc"
        )

        for (sender in excludeSenders) {
            if (lowerFrom.contains(sender)) {
                Log.d(TAG, "Email excluded - from payment processor/e-commerce: $sender")
                return false
            }
        }

        // Must contain bill indicators
        val billIndicators = listOf(
            "amount due", "payment due", "bill amount", "outstanding balance",
            "minimum payment", "total amount due", "current balance",
            "statement", "invoice", "bill", "due date", "payment reminder",
            "overdue", "past due", "final notice"
        )

        var hasBillIndicator = false
        for (indicator in billIndicators) {
            if (content.contains(indicator)) {
                hasBillIndicator = true
                break
            }
        }

        if (!hasBillIndicator) {
            Log.d(TAG, "Email excluded - no bill indicators found")
            return false
        }

        // Additional validation: must be from legitimate billing sources
        val validBillingSources = listOf(
            "bank", "credit", "loan", "electric", "gas", "water", "internet",
            "phone", "mobile", "insurance", "billing", "statements", "invoice",
            "noreply", "support", "accounts", "finance"
        )

        var hasValidSource = false
        for (source in validBillingSources) {
            if (lowerFrom.contains(source) || lowerSubject.contains(source)) {
                hasValidSource = true
                break
            }
        }

        if (!hasValidSource) {
            Log.d(TAG, "Email excluded - not from valid billing source")
            return false
        }

        Log.d(TAG, "Email validated as actual bill")
        return true
    }

    /**
     * Determine bill type based on email content
     */
    private fun determineBillType(subject: String, from: String, body: String): BillType {
        val content = "$subject $from $body".lowercase()

        return when {
            content.contains("credit card") || content.contains("mastercard") || content.contains("visa") -> BillType.CREDIT_CARD
            content.contains("loan") || content.contains("emi") || content.contains("mortgage") -> BillType.LOAN_EMI
            content.contains("electric") || content.contains("gas") || content.contains("water") -> BillType.UTILITY
            content.contains("internet") || content.contains("phone") || content.contains("cable") -> BillType.SUBSCRIPTION
            else -> BillType.OTHER
        }
    }

    /**
     * Parse date string into LocalDate
     */
    private fun parseDateString(dateStr: String): LocalDate? {
        val cleanDateStr = dateStr.trim()

        try {
            // Try different date formats
            val formats = listOf(
                DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH),
                DateTimeFormatter.ofPattern("MMM d, yyyy", Locale.ENGLISH),
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                DateTimeFormatter.ofPattern("dd-MM-yyyy"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("d MMMM yyyy", Locale.ENGLISH),
                DateTimeFormatter.ofPattern("d MMM yyyy", Locale.ENGLISH)
            )

            for (format in formats) {
                try {
                    return LocalDate.parse(cleanDateStr, format)
                } catch (e: Exception) {
                    continue
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error parsing date string '$cleanDateStr': ${e.message}")
        }

        return null
    }

    /**
     * Extract general account details from content
     */
    private fun extractAccountDetails(subject: String, body: String): String {
        val content = "$subject $body"

        // Look for account numbers, reference numbers, etc.
        val patterns = listOf(
            Pattern.compile("account[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("ref[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("reference[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val account = matcher.group(1)
                if (account != null && account.length >= 4) {
                    return "****${account.takeLast(4)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract credit card number (last 4 digits)
     */
    private fun extractCreditCardNumber(content: String): String {
        val patterns = listOf(
            // More specific patterns for credit card bills
            Pattern.compile("card[\\s#:]*ending[\\s#:]*(?:in|with)[\\s#:]*([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("card[\\s#:]*number[\\s#:]*ending[\\s#:]*([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("credit[\\s#:]*card[\\s#:]*\\*{4,}([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("card[\\s#:]*\\*{4,}([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("\\*{4,}[\\s-]*([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("xxxx[\\s-]*xxxx[\\s-]*xxxx[\\s-]*([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("xxxx[\\s-]*([0-9]{4})", Pattern.CASE_INSENSITIVE),
            // Patterns for Indian credit card formats
            Pattern.compile("(?:visa|mastercard|rupay)[\\s#:]*\\*{4,}([0-9]{4})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("statement[\\s#:]*for[\\s#:]*card[\\s#:]*ending[\\s#:]*([0-9]{4})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val cardNumber = matcher.group(1) ?: ""
                if (cardNumber.length == 4 && cardNumber.all { it.isDigit() }) {
                    return cardNumber
                }
            }
        }

        return ""
    }

    /**
     * Extract loan account number
     */
    private fun extractLoanAccountNumber(content: String): String {
        val patterns = listOf(
            // More specific patterns for loan accounts
            Pattern.compile("loan[\\s#:]*account[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("loan[\\s#:]*a/c[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("emi[\\s#:]*account[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("home[\\s#:]*loan[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("personal[\\s#:]*loan[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("car[\\s#:]*loan[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("mortgage[\\s#:]*account[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            // Pattern for loan reference numbers
            Pattern.compile("loan[\\s#:]*ref[\\s#:]*(?:no[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("reference[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{8,16})", Pattern.CASE_INSENSITIVE),
            // Pattern for EMI reference
            Pattern.compile("emi[\\s#:]*ref[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val account = matcher.group(1)
                if (account != null && account.length >= 6) {
                    return "****${account.takeLast(4)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract utility account number
     */
    private fun extractUtilityAccountNumber(content: String, utilityType: String): String {
        val patterns = listOf(
            // More specific patterns for utility accounts
            Pattern.compile("$utilityType[\\s#:]*account[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("$utilityType[\\s#:]*a/c[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("customer[\\s#:]*(?:id|number)[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("consumer[\\s#:]*(?:id|number)[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("service[\\s#:]*(?:id|number)[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("connection[\\s#:]*(?:id|number)[\\s#:]*([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            // Generic account patterns (more restrictive)
            Pattern.compile("account[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{8,16})", Pattern.CASE_INSENSITIVE),
            // Meter number patterns
            Pattern.compile("meter[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE),
            // Bill number patterns
            Pattern.compile("bill[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{6,16})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val account = matcher.group(1)
                if (account != null && account.length >= 6) {
                    return "****${account.takeLast(4)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract phone number
     */
    private fun extractPhoneNumber(content: String): String {
        val patterns = listOf(
            Pattern.compile("phone[\\s#:]*([0-9]{10})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("mobile[\\s#:]*([0-9]{10})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("number[\\s#:]*([0-9]{10})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val phone = matcher.group(1)
                if (phone != null && phone.length == 10) {
                    return "****${phone.takeLast(4)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract policy number
     */
    private fun extractPolicyNumber(content: String): String {
        val patterns = listOf(
            Pattern.compile("policy[\\s#:]*([A-Z0-9]{6,12})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("policy[\\s#:]*number[\\s#:]*([A-Z0-9]{6,12})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val policy = matcher.group(1)
                if (policy != null && policy.length >= 4) {
                    return "****${policy.takeLast(4)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract invoice number
     */
    private fun extractInvoiceNumber(content: String): String {
        val patterns = listOf(
            Pattern.compile("invoice[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("invoice[\\s#:]*number[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("inv[\\s#:]*([A-Z0-9]{4,12})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val invoice = matcher.group(1)
                if (invoice != null && invoice.length >= 4) {
                    return "#${invoice.takeLast(6)}"
                }
            }
        }

        return ""
    }

    /**
     * Extract bank account number
     */
    private fun extractBankAccountNumber(content: String): String {
        val patterns = listOf(
            // More specific patterns for bank accounts
            Pattern.compile("account[\\s#:]*(?:number[\\s#:]*)?([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("a/c[\\s#:]*(?:no[\\s#:]*)?([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("savings[\\s#:]*account[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("current[\\s#:]*account[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("bank[\\s#:]*account[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            // Statement specific patterns
            Pattern.compile("statement[\\s#:]*for[\\s#:]*account[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("statement[\\s#:]*for[\\s#:]*a/c[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE),
            // Indian bank account patterns
            Pattern.compile("(?:hdfc|icici|sbi|axis|kotak|pnb)[\\s#:]*account[\\s#:]*([A-Z0-9]{8,18})", Pattern.CASE_INSENSITIVE)
        )

        for (pattern in patterns) {
            val matcher = pattern.matcher(content)
            if (matcher.find()) {
                val account = matcher.group(1)
                if (account != null && account.length >= 8) {
                    return "****${account.takeLast(4)}"
                }
            }
        }

        return ""
    }
}

{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,10,9,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "286,424,370,55,102,149,196,241,328", "endColumns": "41,45,53,46,46,46,44,44,41", "endOffsets": "323,465,419,97,144,191,236,281,365"}, "to": {"startLines": "60,108,117,118,119,120,131,132,135", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2921,6197,6831,6885,6932,6979,7723,7768,7934", "endColumns": "41,45,53,46,46,46,44,44,41", "endOffsets": "2958,6238,6880,6927,6974,7021,7763,7808,7971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\30e6723bf3223f45401a01df88b36a37\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2693", "startColumns": "4", "startOffsets": "161135", "endLines": "2700", "endColumns": "8", "endOffsets": "161540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aeab53d0ab5ed4b8be886be59417d94\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3849,3862,3868,3874,3883", "startColumns": "4,4,4,4,4", "startOffsets": "205359,205998,206242,206489,206852", "endLines": "3861,3867,3873,3876,3887", "endColumns": "24,24,24,24,24", "endOffsets": "205993,206237,206484,206617,207029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61468bcbedfc450e580f06e70db30d2d\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "75,76,77,78,79,80,81,82,1152,1153,1154,1155,1156,1157,1158,1159,1161,1162,1163,1164,1165,1166,1167,1168,1169,3671,3939", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3984,4074,4154,4244,4334,4414,4495,4575,56991,57096,57277,57402,57509,57689,57812,57928,58198,58386,58491,58672,58797,58972,59120,59183,59245,199781,208712", "endLines": "75,76,77,78,79,80,81,82,1152,1153,1154,1155,1156,1157,1158,1159,1161,1162,1163,1164,1165,1166,1167,1168,1169,3683,3957", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4069,4149,4239,4329,4409,4490,4570,4650,57091,57272,57397,57504,57684,57807,57923,58026,58381,58486,58667,58792,58967,59115,59178,59240,59319,200091,209124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a6d7c100fc2c81a623ad484eab81a0e\\transformed\\ssp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,139,180,221,262,303,344,385,426,467,508,547,588,629,670,711,752,793,834,875,916,957,996,1037,1078,1119,1160,1201,1242,1283,1324,1365,1406,1445,1486,1527,1568,1609,1650,1691,1732,1773,1814,1855,1894,1935,1976,2017,2058,2099,2140,2181,2222,2263,2304,2343,2384,2425,2466,2507,2548,2589,2630,2671,2712,2753,2792,2833,2874,2915,2956,2997,3038,3079,3120,3161,3202,3241,3282,3323,3364,3405,3446,3487,3528,3569,3610,3651,3690,3731,3772,3813,3854,3895,3936,3977,4018,4059,4100", "endColumns": "42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38", "endOffsets": "93,134,175,216,257,298,339,380,421,462,503,542,583,624,665,706,747,788,829,870,911,952,991,1032,1073,1114,1155,1196,1237,1278,1319,1360,1401,1440,1481,1522,1563,1604,1645,1686,1727,1768,1809,1850,1889,1930,1971,2012,2053,2094,2135,2176,2217,2258,2299,2338,2379,2420,2461,2502,2543,2584,2625,2666,2707,2748,2787,2828,2869,2910,2951,2992,3033,3074,3115,3156,3197,3236,3277,3318,3359,3400,3441,3482,3523,3564,3605,3646,3685,3726,3767,3808,3849,3890,3931,3972,4013,4054,4095,4134"}, "to": {"startLines": "137,148,160,172,184,196,208,220,232,244,256,258,270,282,294,306,318,330,342,354,366,378,380,392,404,416,428,440,452,464,476,488,500,502,514,526,538,550,562,574,586,598,610,622,624,636,648,660,672,684,696,708,720,732,744,746,749,751,753,755,757,759,761,763,765,767,769,771,773,775,777,779,781,783,785,787,789,791,793,795,797,799,801,803,805,807,809,811,813,815,817,819,821,823,825,827,829,831,833,835", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8019,8490,9002,9514,10026,10538,11050,11562,12074,12586,13098,13178,13688,14200,14712,15224,15736,16248,16760,17272,17784,18296,18376,18886,19398,19910,20422,20934,21446,21958,22470,22982,23494,23574,24084,24596,25108,25620,26132,26644,27156,27668,28180,28692,28772,29282,29794,30306,30818,31330,31842,32354,32866,33378,33890,33970,34093,34175,34257,34339,34421,34503,34585,34667,34749,34831,34911,34991,35073,35155,35237,35319,35401,35483,35565,35647,35729,35809,35889,35971,36053,36135,36217,36299,36381,36463,36545,36627,36707,36787,36869,36951,37033,37115,37197,37279,37361,37443,37525,37605", "endColumns": "42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38", "endOffsets": "8057,8526,9038,9550,10062,10574,11086,11598,12110,12622,13134,13212,13724,14236,14748,15260,15772,16284,16796,17308,17820,18332,18410,18922,19434,19946,20458,20970,21482,21994,22506,23018,23530,23608,24120,24632,25144,25656,26168,26680,27192,27704,28216,28728,28806,29318,29830,30342,30854,31366,31878,32390,32902,33414,33926,34004,34129,34211,34293,34375,34457,34539,34621,34703,34785,34867,34945,35027,35109,35191,35273,35355,35437,35519,35601,35683,35765,35843,35925,36007,36089,36171,36253,36335,36417,36499,36581,36663,36741,36823,36905,36987,37069,37151,37233,37315,37397,37479,37561,37639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,59,985,1170,1173,1180,1181,1182,1183,1184,1185,1186,1188,1189,1190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,2865,46562,59324,59534,60112,60201,60300,60408,60505,60593,60693,60845,60942,61052", "endLines": "5,7,10,14,33,59,985,1170,1173,1180,1181,1182,1183,1184,1185,1186,1188,1189,1190", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,2916,46610,59395,59589,60196,60295,60403,60500,60588,60688,60758,60937,61047,61136"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\build\\generated\\res\\injectCrashlyticsMappingFileIdDebug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "8", "startColumns": "0", "startOffsets": "286", "endColumns": "173", "endOffsets": "459"}, "to": {"startLines": "1151", "startColumns": "4", "startOffsets": "56815", "endColumns": "175", "endOffsets": "56986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "1139,1276,1277", "startColumns": "4,4,4", "startOffsets": "56053,67165,67221", "endColumns": "45,55,54", "endOffsets": "56094,67216,67271"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,13", "startColumns": "4,4", "startOffsets": "56,612", "endLines": "10,20", "endColumns": "12,12", "endOffsets": "548,1109"}, "to": {"startLines": "2701,2709", "startColumns": "4,4", "startOffsets": "161545,162042", "endLines": "2708,2716", "endColumns": "12,12", "endOffsets": "162037,162539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cfebf9316d0d17d44abb13963a482c8d\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "1093", "startColumns": "4", "startOffsets": "53007", "endColumns": "42", "endOffsets": "53045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0f29b86586a77b8b0dccb44b168a780\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "1064,1068", "startColumns": "4,4", "startOffsets": "51509,51686", "endColumns": "53,66", "endOffsets": "51558,51748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1462ff9226cf23fb2bd96fcd62f3f998\\transformed\\work-runtime-2.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "42,43,44,45", "startColumns": "4,4,4,4", "startOffsets": "1625,1690,1760,1824", "endColumns": "64,69,63,60", "endOffsets": "1685,1755,1819,1880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8d54d76b62491f6323e6900eda7a9066\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "1094", "startColumns": "4", "startOffsets": "53050", "endColumns": "42", "endOffsets": "53088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0540d7468fcc51e7f2b31af8fb65cf36\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "1096", "startColumns": "4", "startOffsets": "53153", "endColumns": "53", "endOffsets": "53202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\400e6bd31249aa26c4c216df15cabffb\\transformed\\appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2947,2963,2969,3970,3986", "startColumns": "4,4,4,4,4", "startOffsets": "175686,176111,176289,209441,209852", "endLines": "2962,2968,2978,3985,3989", "endColumns": "24,24,24,24,24", "endOffsets": "176106,176284,176568,209847,209974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "67,68,69,70,974,975,1171,1177,1178,1179", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3489,3547,3613,3676,45758,45829,59400,59897,59964,60043", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3542,3608,3671,3733,45824,45896,59463,59959,60038,60107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,211", "endColumns": "73,81,83", "endOffsets": "124,206,290"}, "to": {"startLines": "1056,1134,1135", "startColumns": "4,4,4", "startOffsets": "51059,55694,55776", "endColumns": "73,81,83", "endOffsets": "51128,55771,55855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "34,35,36,38,39,40,41,46,47,48,49,50,51,52,55,56,57,58,61,62,63,64,65,66,71,72,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,109,110,111,112,113,114,115,116,121,122,123,124,125,126,127,128,129,130,133,134,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,983,984,986,987,988,989,990,991,992,1008,1009,1010,1011,1012,1013,1014,1015,1051,1052,1053,1054,1062,1069,1070,1073,1092,1100,1101,1102,1103,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1266,1278,1279,1280,1281,1282,1283,1291,1292,1296,1300,1304,1309,1315,1322,1326,1330,1335,1339,1343,1347,1351,1355,1359,1365,1369,1375,1379,1385,1389,1394,1398,1401,1405,1411,1415,1421,1425,1431,1434,1438,1442,1446,1450,1454,1455,1456,1457,1460,1463,1466,1469,1473,1474,1475,1476,1477,1480,1482,1484,1486,1491,1492,1496,1502,1506,1507,1509,1520,1521,1525,1531,1535,1536,1537,1541,1568,1572,1573,1577,1605,1774,1800,1969,1995,2026,2034,2040,2054,2076,2081,2086,2096,2105,2114,2118,2125,2133,2140,2141,2150,2153,2156,2160,2164,2168,2171,2172,2177,2182,2192,2197,2204,2210,2211,2214,2218,2223,2225,2227,2230,2233,2235,2239,2242,2249,2252,2255,2259,2261,2265,2267,2269,2271,2275,2283,2291,2303,2309,2318,2321,2332,2335,2336,2341,2342,2371,2440,2510,2511,2521,2530,2531,2533,2537,2540,2543,2546,2549,2552,2555,2558,2562,2565,2568,2571,2575,2578,2582,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2608,2610,2611,2612,2613,2614,2615,2616,2617,2619,2620,2622,2623,2625,2627,2628,2630,2631,2632,2633,2634,2635,2637,2638,2639,2640,2641,2653,2655,2657,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2673,2674,2675,2676,2677,2678,2679,2681,2685,2717,2718,2719,2720,2721,2722,2726,2727,2728,2729,2731,2733,2735,2737,2739,2740,2741,2742,2744,2746,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2759,2762,2763,2764,2765,2767,2769,2770,2772,2773,2775,2777,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2790,2792,2793,2794,2795,2797,2798,2799,2800,2801,2803,2805,2807,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2826,2901,2904,2907,2910,2924,2937,2979,3008,3035,3044,3106,3465,3485,3513,3635,3659,3665,3684,3705,3829,3888,3894,3898,3904,3958,3990,4056,4076,4131,4143,4169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1199,1254,1299,1408,1449,1504,1563,1885,1949,2019,2080,2155,2231,2308,2546,2631,2713,2789,2963,3040,3118,3224,3330,3409,3738,3795,4655,4729,4804,4869,4935,4995,5056,5128,5201,5268,5393,5452,5511,5570,5629,5688,5742,5796,5849,5903,5957,6011,6243,6317,6396,6469,6543,6614,6686,6758,7026,7083,7141,7214,7288,7362,7437,7509,7582,7652,7813,7873,40446,40515,40584,40654,40728,40804,40868,40945,41021,41098,41163,41232,41309,41384,41453,41521,41598,41664,41725,41822,41887,41956,42055,42126,42185,42243,42300,42359,42423,42494,42566,42638,42710,42782,42849,42917,42985,43044,43107,43171,43261,43352,43412,43478,43545,43611,43681,43745,43798,43865,43926,43993,44106,44164,44227,44292,44357,44432,44505,44577,44626,44687,44748,44809,44871,44935,44999,45063,45128,45191,45251,45312,45378,45437,45497,45559,45630,45690,46389,46475,46615,46705,46792,46880,46962,47045,47135,48204,48256,48314,48359,48425,48489,48546,48603,50780,50837,50885,50934,51420,51753,51800,51956,52975,53374,53438,53500,53560,53828,53902,53972,54050,54104,54174,54259,54307,54353,54414,54477,54543,54607,54678,54741,54806,54870,54931,54992,55044,55117,55191,55260,55335,55409,55483,55624,66640,67276,67354,67444,67532,67628,67718,68300,68389,68636,68917,69169,69454,69847,70324,70546,70768,71044,71271,71501,71731,71961,72191,72418,72837,73063,73488,73718,74146,74365,74648,74856,74987,75214,75640,75865,76292,76513,76938,77058,77334,77635,77959,78250,78564,78701,78832,78937,79179,79346,79550,79758,80029,80141,80253,80358,80475,80689,80835,80975,81061,81409,81497,81743,82161,82410,82492,82590,83182,83282,83534,83958,84213,84307,84396,84633,86657,86899,87001,87254,89410,99851,101367,111906,113434,115191,115817,116237,117298,118563,118819,119055,119602,120096,120701,120899,121479,122043,122418,122536,123074,123231,123427,123700,123956,124126,124267,124331,124696,125063,125739,126003,126341,126694,126788,126974,127280,127542,127667,127794,128033,128244,128363,128556,128733,129188,129369,129491,129750,129863,130050,130152,130259,130388,130663,131171,131667,132544,132838,133408,133557,134289,134461,134545,134881,134973,136599,141845,147234,147296,147874,148458,148549,148662,148891,149051,149203,149374,149540,149709,149876,150039,150282,150452,150625,150796,151070,151269,151474,151804,151888,151984,152080,152178,152278,152380,152482,152584,152686,152788,152888,152984,153096,153225,153348,153479,153610,153708,153822,153916,154056,154190,154286,154398,154498,154614,154710,154822,154922,155062,155198,155362,155492,155650,155800,155941,156085,156220,156332,156482,156610,156738,156874,157006,157136,157266,157378,158276,158422,158566,158704,158770,158860,158936,159040,159130,159232,159340,159448,159548,159628,159720,159818,159928,159980,160058,160164,160256,160360,160470,160592,160755,162544,162624,162724,162814,162924,163014,163255,163349,163455,163547,163647,163759,163873,163989,164105,164199,164313,164425,164527,164647,164769,164851,164955,165075,165201,165299,165393,165481,165593,165709,165831,165943,166118,166234,166320,166412,166524,166648,166715,166841,166909,167037,167181,167309,167378,167473,167588,167701,167800,167909,168020,168131,168232,168337,168437,168567,168658,168781,168875,168987,169073,169177,169273,169361,169479,169583,169687,169813,169901,170009,170109,170199,170309,170393,170495,170579,170633,170697,170803,170889,170999,171083,171342,173958,174076,174191,174271,174632,175169,176573,177917,179278,179666,182441,192345,192984,194341,198568,199319,199581,200096,200475,204753,207034,207263,207414,207629,209129,209979,213005,213749,215880,216220,217531", "endLines": "34,35,36,38,39,40,41,46,47,48,49,50,51,52,55,56,57,58,61,62,63,64,65,66,71,72,83,84,85,86,87,88,89,90,91,92,94,95,96,97,98,99,100,101,102,103,104,105,109,110,111,112,113,114,115,116,121,122,123,124,125,126,127,128,129,130,133,134,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,983,984,986,987,988,989,990,991,992,1008,1009,1010,1011,1012,1013,1014,1015,1051,1052,1053,1054,1062,1069,1070,1073,1092,1100,1101,1102,1103,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1266,1278,1279,1280,1281,1282,1290,1291,1295,1299,1303,1308,1314,1321,1325,1329,1334,1338,1342,1346,1350,1354,1358,1364,1368,1374,1378,1384,1388,1393,1397,1400,1404,1410,1414,1420,1424,1430,1433,1437,1441,1445,1449,1453,1454,1455,1456,1459,1462,1465,1468,1472,1473,1474,1475,1476,1479,1481,1483,1485,1490,1491,1495,1501,1505,1506,1508,1519,1520,1524,1530,1534,1535,1536,1540,1567,1571,1572,1576,1604,1773,1799,1968,1994,2025,2033,2039,2053,2075,2080,2085,2095,2104,2113,2117,2124,2132,2139,2140,2149,2152,2155,2159,2163,2167,2170,2171,2176,2181,2191,2196,2203,2209,2210,2213,2217,2222,2224,2226,2229,2232,2234,2238,2241,2248,2251,2254,2258,2260,2264,2266,2268,2270,2274,2282,2290,2302,2308,2317,2320,2331,2334,2335,2340,2341,2346,2439,2509,2510,2520,2529,2530,2532,2536,2539,2542,2545,2548,2551,2554,2557,2561,2564,2567,2570,2574,2577,2581,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2607,2609,2610,2611,2612,2613,2614,2615,2616,2618,2619,2621,2622,2624,2626,2627,2629,2630,2631,2632,2633,2634,2636,2637,2638,2639,2640,2641,2654,2656,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2672,2673,2674,2675,2676,2677,2678,2680,2684,2688,2717,2718,2719,2720,2721,2725,2726,2727,2728,2730,2732,2734,2736,2738,2739,2740,2741,2743,2745,2747,2748,2749,2750,2751,2752,2753,2754,2755,2756,2757,2758,2761,2762,2763,2764,2766,2768,2769,2771,2772,2774,2776,2778,2779,2780,2781,2782,2783,2784,2785,2786,2787,2788,2789,2791,2792,2793,2794,2796,2797,2798,2799,2800,2802,2804,2806,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2900,2903,2906,2909,2923,2929,2946,3007,3034,3043,3105,3464,3468,3512,3530,3658,3664,3670,3704,3828,3848,3893,3897,3903,3938,3969,4055,4075,4130,4142,4168,4175", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1249,1294,1343,1444,1499,1558,1620,1944,2014,2075,2150,2226,2303,2381,2626,2708,2784,2860,3035,3113,3219,3325,3404,3484,3790,3848,4724,4799,4864,4930,4990,5051,5123,5196,5263,5331,5447,5506,5565,5624,5683,5737,5791,5844,5898,5952,6006,6060,6312,6391,6464,6538,6609,6681,6753,6826,7078,7136,7209,7283,7357,7432,7504,7577,7647,7718,7868,7929,40510,40579,40649,40723,40799,40863,40940,41016,41093,41158,41227,41304,41379,41448,41516,41593,41659,41720,41817,41882,41951,42050,42121,42180,42238,42295,42354,42418,42489,42561,42633,42705,42777,42844,42912,42980,43039,43102,43166,43256,43347,43407,43473,43540,43606,43676,43740,43793,43860,43921,43988,44101,44159,44222,44287,44352,44427,44500,44572,44621,44682,44743,44804,44866,44930,44994,45058,45123,45186,45246,45307,45373,45432,45492,45554,45625,45685,45753,46470,46557,46700,46787,46875,46957,47040,47130,47221,48251,48309,48354,48420,48484,48541,48598,48652,50832,50880,50929,50980,51449,51795,51844,51997,53002,53433,53495,53555,53612,53897,53967,54045,54099,54169,54254,54302,54348,54409,54472,54538,54602,54673,54736,54801,54865,54926,54987,55039,55112,55186,55255,55330,55404,55478,55619,55689,66688,67349,67439,67527,67623,67713,68295,68384,68631,68912,69164,69449,69842,70319,70541,70763,71039,71266,71496,71726,71956,72186,72413,72832,73058,73483,73713,74141,74360,74643,74851,74982,75209,75635,75860,76287,76508,76933,77053,77329,77630,77954,78245,78559,78696,78827,78932,79174,79341,79545,79753,80024,80136,80248,80353,80470,80684,80830,80970,81056,81404,81492,81738,82156,82405,82487,82585,83177,83277,83529,83953,84208,84302,84391,84628,86652,86894,86996,87249,89405,99846,101362,111901,113429,115186,115812,116232,117293,118558,118814,119050,119597,120091,120696,120894,121474,122038,122413,122531,123069,123226,123422,123695,123951,124121,124262,124326,124691,125058,125734,125998,126336,126689,126783,126969,127275,127537,127662,127789,128028,128239,128358,128551,128728,129183,129364,129486,129745,129858,130045,130147,130254,130383,130658,131166,131662,132539,132833,133403,133552,134284,134456,134540,134876,134968,135246,141840,147229,147291,147869,148453,148544,148657,148886,149046,149198,149369,149535,149704,149871,150034,150277,150447,150620,150791,151065,151264,151469,151799,151883,151979,152075,152173,152273,152375,152477,152579,152681,152783,152883,152979,153091,153220,153343,153474,153605,153703,153817,153911,154051,154185,154281,154393,154493,154609,154705,154817,154917,155057,155193,155357,155487,155645,155795,155936,156080,156215,156327,156477,156605,156733,156869,157001,157131,157261,157373,157513,158417,158561,158699,158765,158855,158931,159035,159125,159227,159335,159443,159543,159623,159715,159813,159923,159975,160053,160159,160251,160355,160465,160587,160750,160907,162619,162719,162809,162919,163009,163250,163344,163450,163542,163642,163754,163868,163984,164100,164194,164308,164420,164522,164642,164764,164846,164950,165070,165196,165294,165388,165476,165588,165704,165826,165938,166113,166229,166315,166407,166519,166643,166710,166836,166904,167032,167176,167304,167373,167468,167583,167696,167795,167904,168015,168126,168227,168332,168432,168562,168653,168776,168870,168982,169068,169172,169268,169356,169474,169578,169682,169808,169896,170004,170104,170194,170304,170388,170490,170574,170628,170692,170798,170884,170994,171078,171198,173953,174071,174186,174266,174627,174860,175681,177912,179273,179661,182436,192340,192475,194336,194908,199314,199576,199776,200470,204748,205354,207258,207409,207624,208707,209436,213000,213744,215875,216215,217526,217729"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "93", "startColumns": "4", "startOffsets": "5336", "endColumns": "56", "endOffsets": "5388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b102bcd17cb626f3917d24b70b43337\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "1060", "startColumns": "4", "startOffsets": "51303", "endColumns": "65", "endOffsets": "51364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5035e0a6201b66369960bcfd915e2dc5\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "1059,1072,1098,3587,3592", "startColumns": "4,4,4,4,4", "startOffsets": "51246,51891,53257,197400,197570", "endLines": "1059,1072,1098,3591,3595", "endColumns": "56,64,63,24,24", "endOffsets": "51298,51951,53316,197565,197714"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,729", "endColumns": "143,81,103,108,119,114,83", "endOffsets": "194,276,380,489,609,724,808"}, "to": {"startLines": "1175,1187,1191,1192,1193,1194,1263", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "59695,60763,61141,61245,61354,61474,66452", "endColumns": "143,81,103,108,119,114,83", "endOffsets": "59834,60840,61240,61349,61469,61584,66531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "37,53,54,73,74,106,107,976,977,978,979,980,981,982,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1065,1066,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1106,1141,1142,1143,1144,1145,1146,1147,1272,2642,2643,2647,2648,2652,2824,2825,3469,3475,3531,3566,3596,3629", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1348,2386,2458,3853,3918,6065,6134,45901,45971,46039,46111,46181,46242,46316,47226,47287,47348,47410,47474,47536,47597,47665,47765,47825,47891,47964,48033,48090,48142,48657,48729,48805,48870,48929,48988,49048,49108,49168,49228,49288,49348,49408,49468,49528,49588,49647,49707,49767,49827,49887,49947,50007,50067,50127,50187,50247,50306,50366,50426,50485,50544,50603,50662,50721,51563,51598,52002,52057,52120,52175,52233,52289,52347,52408,52471,52528,52579,52637,52687,52748,52805,52871,52905,52940,53758,56137,56204,56276,56345,56414,56488,56560,66921,157518,157635,157836,157946,158147,171203,171275,192480,192683,194913,196719,197719,198401", "endLines": "37,53,54,73,74,106,107,976,977,978,979,980,981,982,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1065,1066,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1106,1141,1142,1143,1144,1145,1146,1147,1272,2642,2646,2647,2651,2652,2824,2825,3474,3484,3565,3586,3628,3634", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1403,2453,2541,3913,3979,6129,6192,45966,46034,46106,46176,46237,46311,46384,47282,47343,47405,47469,47531,47592,47660,47760,47820,47886,47959,48028,48085,48137,48199,48724,48800,48865,48924,48983,49043,49103,49163,49223,49283,49343,49403,49463,49523,49583,49642,49702,49762,49822,49882,49942,50002,50062,50122,50182,50242,50301,50361,50421,50480,50539,50598,50657,50716,50775,51593,51628,52052,52115,52170,52228,52284,52342,52403,52466,52523,52574,52632,52682,52743,52800,52866,52900,52935,52970,53823,56199,56271,56340,56409,56483,56555,56643,66987,157630,157831,157941,158142,158271,171270,171337,192678,192979,196714,197395,198396,198563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\48abf8d0bb245a0792f907060c344438\\transformed\\credentials-play-services-auth-1.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2689", "startColumns": "4", "startOffsets": "160912", "endLines": "2692", "endColumns": "12", "endOffsets": "161130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1ba429aa6de3be7de3bb6e415af4e51\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "1104,1160", "startColumns": "4,4", "startOffsets": "53617,58031", "endColumns": "67,166", "endOffsets": "53680,58193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "1105,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,2350,2360", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "53685,61701,61789,61875,61956,62040,62109,62174,62257,62363,62449,62569,62623,62692,62753,62822,62911,63006,63080,63177,63270,63368,63517,63608,63696,63792,63890,63954,64022,64109,64203,64270,64342,64414,64515,64624,64700,64769,64817,64883,64947,65021,65078,65135,65207,65257,65311,65382,65453,65523,65592,65650,65726,65797,65871,65957,66007,66077,135363,136078", "endLines": "1105,1197,1198,1199,1200,1201,1202,1203,1204,1205,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,2359,2362", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "53753,61784,61870,61951,62035,62104,62169,62252,62358,62444,62564,62618,62687,62748,62817,62906,63001,63075,63172,63265,63363,63512,63603,63691,63787,63885,63949,64017,64104,64198,64265,64337,64409,64510,64619,64695,64764,64812,64878,64942,65016,65073,65130,65202,65252,65306,65377,65448,65518,65587,65645,65721,65792,65866,65952,66002,66072,66137,136073,136226"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Task Reminder2- AugmentCode\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,5,6,4,3", "startColumns": "4,4,4,4,4,4", "startOffsets": "16,60,262,300,196,126", "endColumns": "43,65,37,47,65,69", "endOffsets": "55,121,295,343,257,191"}, "to": {"startLines": "1137,1138,1140,1148,1260,1262", "startColumns": "4,4,4,4,4,4", "startOffsets": "55943,55987,56099,56648,66262,66382", "endColumns": "43,65,37,47,65,69", "endOffsets": "55982,56048,56132,56691,66323,66447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3aea764cac945604cf3ab6b602a3099c\\transformed\\sdp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,141,184,227,270,313,356,399,442,485,526,569,612,655,698,741,784,827,870,913,956,997,1040,1083,1126,1169,1212,1255,1298,1341,1384,1427,1468,1511,1554,1597,1640,1683,1726,1769,1812,1855,1898,1939,1982,2025,2068,2111,2154,2197,2240,2283,2326,2369,2410,2453,2496,2539,2582,2625,2668,2711,2754,2797,2840,2881,2924,2967,3010,3053,3096,3139,3182,3225,3268,3311,3352,3395,3438,3481,3524,3567,3610,3653,3696,3739,3782,3823,3866,3909,3952,3995,4038,4081,4124,4167,4210,4253,4294,4337,4380,4423,4466,4509,4552,4595,4638,4681,4724,4765,4804,4847,4890,4933,4976,5019,5062,5105,5148,5191,5234,5275,5318,5361,5404,5447,5490,5533,5576,5619,5662,5705,5746,5789,5832,5875,5918,5961,6004,6047,6090,6133,6176,6217,6260,6303,6346,6389,6432,6475,6518,6561,6604,6647,6688,6731,6774,6817,6860,6903,6946,6989,7032,7075,7118,7159,7202,7245,7288,7331,7374,7417,7460,7503,7546,7589,7630,7673,7716,7759,7802,7845,7888,7931,7974,8017,8060,8101,8144,8187,8230,8273,8316,8359,8402,8445,8488,8531,8572,8615,8658,8701,8744,8787,8830,8873,8916,8959,9002,9043,9086,9129,9172,9215,9258,9301,9344,9387,9430,9473,9514,9553,9596,9639,9682,9725,9768,9811,9854,9897,9940,9983,10024,10067,10110,10153,10196,10239,10282,10325,10368,10411,10454,10495,10538,10581,10624,10667,10710,10753,10796,10839,10882,10925,10966,11009,11052,11095,11138,11181,11224,11267,11310,11353,11396,11437,11480,11523,11566,11609,11652,11695,11738,11781,11824,11867,11908,11951,11994,12037,12080,12123,12166,12209,12252,12295,12338,12379,12422,12465,12508,12551,12594,12637,12680,12723,12766,12809,12850,12893,12936,12979,13022,13065,13108,13151,13194,13237,13280,13321,13364,13407,13450,13493,13536,13579,13622,13665,13708,13751,13792,13835,13878,13921,13964,14007,14050,14093,14136,14179,14222,14263,14302,14345,14388,14431,14474,14517,14560,14603,14646,14689,14732,14773,14816,14859,14902,14945,14988,15031,15074,15117,15160,15203,15244,15287,15330,15373,15416,15459,15502,15545,15588,15631,15674,15715,15758,15801,15844,15887,15930,15973,16016,16059,16102,16145,16186,16229,16272,16315,16358,16401,16444,16487,16530,16573,16616,16657,16700,16743,16786,16829,16872,16915,16958,17001,17044,17087,17128,17171,17214,17257,17300,17343,17386,17429,17472,17515,17558,17599,17642,17685,17728,17771,17814,17857,17900,17943,17986,18029,18070,18113,18156,18199,18242,18285,18328,18371,18414,18457,18500,18541,18584,18627,18670,18713,18756,18799,18842,18885,18928,18971,19012,19051,19094,19137,19180,19223,19266,19309,19352,19395,19438,19481,19522,19565,19608,19651,19694,19737,19780,19823,19866,19909,19952,19993,20036,20079,20122,20165,20208,20251,20294,20337,20380,20423,20464,20507,20550,20593,20636,20679,20722,20765,20808,20851,20894,20935,20978,21021,21064,21107,21150,21193,21236,21279,21322,21365,21406,21449,21492,21535,21578,21621,21664,21707,21750,21793,21836,21877,21920,21963,22006,22049,22092,22135,22178,22221,22264,22307,22348,22391,22434,22477,22520,22563,22606,22649,22692,22735,22778,22819,22862,22905,22948,22991,23034,23077,23120,23163,23206,23249,23290,23333,23376,23419,23462,23505,23548,23591,23634,23677,23720,23761,23800,23843,23884,23925,23966,24007,24048,24089,24130,24171,24212,24253,24292,24333,24374,24415,24456,24497,24538,24579,24620,24661,24702,24741,24782,24823,24864,24905,24946,24987,25028,25069,25110,25151,25190,25231,25272,25313,25354,25395,25436,25477,25518,25559,25600,25639,25686,25733,25780,25827,25874,25921,25968,26015,26062,26109,26154,26201,26248,26295,26342,26389,26436,26483,26530,26577,26624,26669,26716,26763,26810,26857,26904,26951,26998,27045,27092,27139,27184,27231,27278,27325,27372,27419,27466,27513,27560,27607,27654,27699,27746,27793,27840,27887,27934,27981,28028,28075,28122,28169,28214,28261,28306,28351,28396", "endColumns": "42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,44,44,44,44", "endOffsets": "93,136,179,222,265,308,351,394,437,480,521,564,607,650,693,736,779,822,865,908,951,992,1035,1078,1121,1164,1207,1250,1293,1336,1379,1422,1463,1506,1549,1592,1635,1678,1721,1764,1807,1850,1893,1934,1977,2020,2063,2106,2149,2192,2235,2278,2321,2364,2405,2448,2491,2534,2577,2620,2663,2706,2749,2792,2835,2876,2919,2962,3005,3048,3091,3134,3177,3220,3263,3306,3347,3390,3433,3476,3519,3562,3605,3648,3691,3734,3777,3818,3861,3904,3947,3990,4033,4076,4119,4162,4205,4248,4289,4332,4375,4418,4461,4504,4547,4590,4633,4676,4719,4760,4799,4842,4885,4928,4971,5014,5057,5100,5143,5186,5229,5270,5313,5356,5399,5442,5485,5528,5571,5614,5657,5700,5741,5784,5827,5870,5913,5956,5999,6042,6085,6128,6171,6212,6255,6298,6341,6384,6427,6470,6513,6556,6599,6642,6683,6726,6769,6812,6855,6898,6941,6984,7027,7070,7113,7154,7197,7240,7283,7326,7369,7412,7455,7498,7541,7584,7625,7668,7711,7754,7797,7840,7883,7926,7969,8012,8055,8096,8139,8182,8225,8268,8311,8354,8397,8440,8483,8526,8567,8610,8653,8696,8739,8782,8825,8868,8911,8954,8997,9038,9081,9124,9167,9210,9253,9296,9339,9382,9425,9468,9509,9548,9591,9634,9677,9720,9763,9806,9849,9892,9935,9978,10019,10062,10105,10148,10191,10234,10277,10320,10363,10406,10449,10490,10533,10576,10619,10662,10705,10748,10791,10834,10877,10920,10961,11004,11047,11090,11133,11176,11219,11262,11305,11348,11391,11432,11475,11518,11561,11604,11647,11690,11733,11776,11819,11862,11903,11946,11989,12032,12075,12118,12161,12204,12247,12290,12333,12374,12417,12460,12503,12546,12589,12632,12675,12718,12761,12804,12845,12888,12931,12974,13017,13060,13103,13146,13189,13232,13275,13316,13359,13402,13445,13488,13531,13574,13617,13660,13703,13746,13787,13830,13873,13916,13959,14002,14045,14088,14131,14174,14217,14258,14297,14340,14383,14426,14469,14512,14555,14598,14641,14684,14727,14768,14811,14854,14897,14940,14983,15026,15069,15112,15155,15198,15239,15282,15325,15368,15411,15454,15497,15540,15583,15626,15669,15710,15753,15796,15839,15882,15925,15968,16011,16054,16097,16140,16181,16224,16267,16310,16353,16396,16439,16482,16525,16568,16611,16652,16695,16738,16781,16824,16867,16910,16953,16996,17039,17082,17123,17166,17209,17252,17295,17338,17381,17424,17467,17510,17553,17594,17637,17680,17723,17766,17809,17852,17895,17938,17981,18024,18065,18108,18151,18194,18237,18280,18323,18366,18409,18452,18495,18536,18579,18622,18665,18708,18751,18794,18837,18880,18923,18966,19007,19046,19089,19132,19175,19218,19261,19304,19347,19390,19433,19476,19517,19560,19603,19646,19689,19732,19775,19818,19861,19904,19947,19988,20031,20074,20117,20160,20203,20246,20289,20332,20375,20418,20459,20502,20545,20588,20631,20674,20717,20760,20803,20846,20889,20930,20973,21016,21059,21102,21145,21188,21231,21274,21317,21360,21401,21444,21487,21530,21573,21616,21659,21702,21745,21788,21831,21872,21915,21958,22001,22044,22087,22130,22173,22216,22259,22302,22343,22386,22429,22472,22515,22558,22601,22644,22687,22730,22773,22814,22857,22900,22943,22986,23029,23072,23115,23158,23201,23244,23285,23328,23371,23414,23457,23500,23543,23586,23629,23672,23715,23756,23795,23838,23879,23920,23961,24002,24043,24084,24125,24166,24207,24248,24287,24328,24369,24410,24451,24492,24533,24574,24615,24656,24697,24736,24777,24818,24859,24900,24941,24982,25023,25064,25105,25146,25185,25226,25267,25308,25349,25390,25431,25472,25513,25554,25595,25634,25681,25728,25775,25822,25869,25916,25963,26010,26057,26104,26149,26196,26243,26290,26337,26384,26431,26478,26525,26572,26619,26664,26711,26758,26805,26852,26899,26946,26993,27040,27087,27134,27179,27226,27273,27320,27367,27414,27461,27508,27555,27602,27649,27694,27741,27788,27835,27882,27929,27976,28023,28070,28117,28164,28209,28256,28301,28346,28391,28436"}, "to": {"startLines": "136,138,139,140,141,142,143,144,145,146,147,149,150,151,152,153,154,155,156,157,158,159,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,185,186,187,188,189,190,191,192,193,194,195,197,198,199,200,201,202,203,204,205,206,207,209,210,211,212,213,214,215,216,217,218,219,221,222,223,224,225,226,227,228,229,230,231,233,234,235,236,237,238,239,240,241,242,243,245,246,247,248,249,250,251,252,253,254,255,257,259,260,261,262,263,264,265,266,267,268,269,271,272,273,274,275,276,277,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,295,296,297,298,299,300,301,302,303,304,305,307,308,309,310,311,312,313,314,315,316,317,319,320,321,322,323,324,325,326,327,328,329,331,332,333,334,335,336,337,338,339,340,341,343,344,345,346,347,348,349,350,351,352,353,355,356,357,358,359,360,361,362,363,364,365,367,368,369,370,371,372,373,374,375,376,377,379,381,382,383,384,385,386,387,388,389,390,391,393,394,395,396,397,398,399,400,401,402,403,405,406,407,408,409,410,411,412,413,414,415,417,418,419,420,421,422,423,424,425,426,427,429,430,431,432,433,434,435,436,437,438,439,441,442,443,444,445,446,447,448,449,450,451,453,454,455,456,457,458,459,460,461,462,463,465,466,467,468,469,470,471,472,473,474,475,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,501,503,504,505,506,507,508,509,510,511,512,513,515,516,517,518,519,520,521,522,523,524,525,527,528,529,530,531,532,533,534,535,536,537,539,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558,559,560,561,563,564,565,566,567,568,569,570,571,572,573,575,576,577,578,579,580,581,582,583,584,585,587,588,589,590,591,592,593,594,595,596,597,599,600,601,602,603,604,605,606,607,608,609,611,612,613,614,615,616,617,618,619,620,621,623,625,626,627,628,629,630,631,632,633,634,635,637,638,639,640,641,642,643,644,645,646,647,649,650,651,652,653,654,655,656,657,658,659,661,662,663,664,665,666,667,668,669,670,671,673,674,675,676,677,678,679,680,681,682,683,685,686,687,688,689,690,691,692,693,694,695,697,698,699,700,701,702,703,704,705,706,707,709,710,711,712,713,714,715,716,717,718,719,721,722,723,724,725,726,727,728,729,730,731,733,734,735,736,737,738,739,740,741,742,743,745,747,748,750,752,754,756,758,760,762,764,766,768,770,772,774,776,778,780,782,784,786,788,790,792,794,796,798,800,802,804,806,808,810,812,814,816,818,820,822,824,826,828,830,832,834,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7976,8062,8105,8148,8191,8234,8277,8320,8363,8406,8449,8531,8574,8617,8660,8703,8746,8789,8832,8875,8918,8961,9043,9086,9129,9172,9215,9258,9301,9344,9387,9430,9473,9555,9598,9641,9684,9727,9770,9813,9856,9899,9942,9985,10067,10110,10153,10196,10239,10282,10325,10368,10411,10454,10497,10579,10622,10665,10708,10751,10794,10837,10880,10923,10966,11009,11091,11134,11177,11220,11263,11306,11349,11392,11435,11478,11521,11603,11646,11689,11732,11775,11818,11861,11904,11947,11990,12033,12115,12158,12201,12244,12287,12330,12373,12416,12459,12502,12545,12627,12670,12713,12756,12799,12842,12885,12928,12971,13014,13057,13139,13217,13260,13303,13346,13389,13432,13475,13518,13561,13604,13647,13729,13772,13815,13858,13901,13944,13987,14030,14073,14116,14159,14241,14284,14327,14370,14413,14456,14499,14542,14585,14628,14671,14753,14796,14839,14882,14925,14968,15011,15054,15097,15140,15183,15265,15308,15351,15394,15437,15480,15523,15566,15609,15652,15695,15777,15820,15863,15906,15949,15992,16035,16078,16121,16164,16207,16289,16332,16375,16418,16461,16504,16547,16590,16633,16676,16719,16801,16844,16887,16930,16973,17016,17059,17102,17145,17188,17231,17313,17356,17399,17442,17485,17528,17571,17614,17657,17700,17743,17825,17868,17911,17954,17997,18040,18083,18126,18169,18212,18255,18337,18415,18458,18501,18544,18587,18630,18673,18716,18759,18802,18845,18927,18970,19013,19056,19099,19142,19185,19228,19271,19314,19357,19439,19482,19525,19568,19611,19654,19697,19740,19783,19826,19869,19951,19994,20037,20080,20123,20166,20209,20252,20295,20338,20381,20463,20506,20549,20592,20635,20678,20721,20764,20807,20850,20893,20975,21018,21061,21104,21147,21190,21233,21276,21319,21362,21405,21487,21530,21573,21616,21659,21702,21745,21788,21831,21874,21917,21999,22042,22085,22128,22171,22214,22257,22300,22343,22386,22429,22511,22554,22597,22640,22683,22726,22769,22812,22855,22898,22941,23023,23066,23109,23152,23195,23238,23281,23324,23367,23410,23453,23535,23613,23656,23699,23742,23785,23828,23871,23914,23957,24000,24043,24125,24168,24211,24254,24297,24340,24383,24426,24469,24512,24555,24637,24680,24723,24766,24809,24852,24895,24938,24981,25024,25067,25149,25192,25235,25278,25321,25364,25407,25450,25493,25536,25579,25661,25704,25747,25790,25833,25876,25919,25962,26005,26048,26091,26173,26216,26259,26302,26345,26388,26431,26474,26517,26560,26603,26685,26728,26771,26814,26857,26900,26943,26986,27029,27072,27115,27197,27240,27283,27326,27369,27412,27455,27498,27541,27584,27627,27709,27752,27795,27838,27881,27924,27967,28010,28053,28096,28139,28221,28264,28307,28350,28393,28436,28479,28522,28565,28608,28651,28733,28811,28854,28897,28940,28983,29026,29069,29112,29155,29198,29241,29323,29366,29409,29452,29495,29538,29581,29624,29667,29710,29753,29835,29878,29921,29964,30007,30050,30093,30136,30179,30222,30265,30347,30390,30433,30476,30519,30562,30605,30648,30691,30734,30777,30859,30902,30945,30988,31031,31074,31117,31160,31203,31246,31289,31371,31414,31457,31500,31543,31586,31629,31672,31715,31758,31801,31883,31926,31969,32012,32055,32098,32141,32184,32227,32270,32313,32395,32438,32481,32524,32567,32610,32653,32696,32739,32782,32825,32907,32950,32993,33036,33079,33122,33165,33208,33251,33294,33337,33419,33462,33505,33548,33591,33634,33677,33720,33763,33806,33849,33931,34009,34052,34134,34216,34298,34380,34462,34544,34626,34708,34790,34872,34950,35032,35114,35196,35278,35360,35442,35524,35606,35688,35770,35848,35930,36012,36094,36176,36258,36340,36422,36504,36586,36668,36746,36828,36910,36992,37074,37156,37238,37320,37402,37484,37566,37644,37691,37738,37785,37832,37879,37926,37973,38020,38067,38114,38159,38206,38253,38300,38347,38394,38441,38488,38535,38582,38629,38674,38721,38768,38815,38862,38909,38956,39003,39050,39097,39144,39189,39236,39283,39330,39377,39424,39471,39518,39565,39612,39659,39704,39751,39798,39845,39892,39939,39986,40033,40080,40127,40174,40219,40266,40311,40356,40401", "endColumns": "42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,44,44,44,44", "endOffsets": "8014,8100,8143,8186,8229,8272,8315,8358,8401,8444,8485,8569,8612,8655,8698,8741,8784,8827,8870,8913,8956,8997,9081,9124,9167,9210,9253,9296,9339,9382,9425,9468,9509,9593,9636,9679,9722,9765,9808,9851,9894,9937,9980,10021,10105,10148,10191,10234,10277,10320,10363,10406,10449,10492,10533,10617,10660,10703,10746,10789,10832,10875,10918,10961,11004,11045,11129,11172,11215,11258,11301,11344,11387,11430,11473,11516,11557,11641,11684,11727,11770,11813,11856,11899,11942,11985,12028,12069,12153,12196,12239,12282,12325,12368,12411,12454,12497,12540,12581,12665,12708,12751,12794,12837,12880,12923,12966,13009,13052,13093,13173,13255,13298,13341,13384,13427,13470,13513,13556,13599,13642,13683,13767,13810,13853,13896,13939,13982,14025,14068,14111,14154,14195,14279,14322,14365,14408,14451,14494,14537,14580,14623,14666,14707,14791,14834,14877,14920,14963,15006,15049,15092,15135,15178,15219,15303,15346,15389,15432,15475,15518,15561,15604,15647,15690,15731,15815,15858,15901,15944,15987,16030,16073,16116,16159,16202,16243,16327,16370,16413,16456,16499,16542,16585,16628,16671,16714,16755,16839,16882,16925,16968,17011,17054,17097,17140,17183,17226,17267,17351,17394,17437,17480,17523,17566,17609,17652,17695,17738,17779,17863,17906,17949,17992,18035,18078,18121,18164,18207,18250,18291,18371,18453,18496,18539,18582,18625,18668,18711,18754,18797,18840,18881,18965,19008,19051,19094,19137,19180,19223,19266,19309,19352,19393,19477,19520,19563,19606,19649,19692,19735,19778,19821,19864,19905,19989,20032,20075,20118,20161,20204,20247,20290,20333,20376,20417,20501,20544,20587,20630,20673,20716,20759,20802,20845,20888,20929,21013,21056,21099,21142,21185,21228,21271,21314,21357,21400,21441,21525,21568,21611,21654,21697,21740,21783,21826,21869,21912,21953,22037,22080,22123,22166,22209,22252,22295,22338,22381,22424,22465,22549,22592,22635,22678,22721,22764,22807,22850,22893,22936,22977,23061,23104,23147,23190,23233,23276,23319,23362,23405,23448,23489,23569,23651,23694,23737,23780,23823,23866,23909,23952,23995,24038,24079,24163,24206,24249,24292,24335,24378,24421,24464,24507,24550,24591,24675,24718,24761,24804,24847,24890,24933,24976,25019,25062,25103,25187,25230,25273,25316,25359,25402,25445,25488,25531,25574,25615,25699,25742,25785,25828,25871,25914,25957,26000,26043,26086,26127,26211,26254,26297,26340,26383,26426,26469,26512,26555,26598,26639,26723,26766,26809,26852,26895,26938,26981,27024,27067,27110,27151,27235,27278,27321,27364,27407,27450,27493,27536,27579,27622,27663,27747,27790,27833,27876,27919,27962,28005,28048,28091,28134,28175,28259,28302,28345,28388,28431,28474,28517,28560,28603,28646,28687,28767,28849,28892,28935,28978,29021,29064,29107,29150,29193,29236,29277,29361,29404,29447,29490,29533,29576,29619,29662,29705,29748,29789,29873,29916,29959,30002,30045,30088,30131,30174,30217,30260,30301,30385,30428,30471,30514,30557,30600,30643,30686,30729,30772,30813,30897,30940,30983,31026,31069,31112,31155,31198,31241,31284,31325,31409,31452,31495,31538,31581,31624,31667,31710,31753,31796,31837,31921,31964,32007,32050,32093,32136,32179,32222,32265,32308,32349,32433,32476,32519,32562,32605,32648,32691,32734,32777,32820,32861,32945,32988,33031,33074,33117,33160,33203,33246,33289,33332,33373,33457,33500,33543,33586,33629,33672,33715,33758,33801,33844,33885,33965,34047,34088,34170,34252,34334,34416,34498,34580,34662,34744,34826,34906,34986,35068,35150,35232,35314,35396,35478,35560,35642,35724,35804,35884,35966,36048,36130,36212,36294,36376,36458,36540,36622,36702,36782,36864,36946,37028,37110,37192,37274,37356,37438,37520,37600,37686,37733,37780,37827,37874,37921,37968,38015,38062,38109,38154,38201,38248,38295,38342,38389,38436,38483,38530,38577,38624,38669,38716,38763,38810,38857,38904,38951,38998,39045,39092,39139,39184,39231,39278,39325,39372,39419,39466,39513,39560,39607,39654,39699,39746,39793,39840,39887,39934,39981,40028,40075,40122,40169,40214,40261,40306,40351,40396,40441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "1258", "startColumns": "4", "startOffsets": "66142", "endColumns": "57", "endOffsets": "66195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f666c5db6b6a53b4eb945aeaa73169a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "1071,1095", "startColumns": "4,4", "startOffsets": "51849,53093", "endColumns": "41,59", "endOffsets": "51886,53148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ce7818e902f96ec44af435d501959ab6\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "1067,2930,3877,3880", "startColumns": "4,4,4,4", "startOffsets": "51633,174865,206622,206737", "endLines": "1067,2936,3879,3882", "endColumns": "52,24,24,24", "endOffsets": "51681,175164,206732,206847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f946208451562c739cc56d3adc46cf0e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "1136", "startColumns": "4", "startOffsets": "55860", "endColumns": "82", "endOffsets": "55938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "1055,1057,1058,1061,1063,1099,1149,1150,1172,1174,1176,1195,1196,1259,1261,1264,1265,1267,1268,1269,1270,1271,1273,1274,1275,2347,2363,2366", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "50985,51133,51191,51369,51454,53321,56696,56761,59468,59594,59839,61589,61641,66200,66328,66536,66586,66693,66739,66793,66839,66881,66992,67039,67075,135251,136231,136342", "endLines": "1055,1057,1058,1061,1063,1099,1149,1150,1172,1174,1176,1195,1196,1259,1261,1264,1265,1267,1268,1269,1270,1271,1273,1274,1275,2349,2365,2370", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "51054,51186,51241,51415,51504,53369,56756,56810,59529,59690,59892,61636,61696,66257,66377,66581,66635,66734,66788,66834,66876,66916,67034,67070,67160,135358,136337,136594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\529478e4d8b6a7ccd4c1536cee8f46a5\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "1097", "startColumns": "4", "startOffsets": "53207", "endColumns": "49", "endOffsets": "53252"}}]}]}
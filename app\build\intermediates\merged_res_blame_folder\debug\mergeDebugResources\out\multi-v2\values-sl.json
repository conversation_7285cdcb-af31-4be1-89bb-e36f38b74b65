{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4071,4177,4342,4471,4578,4725,4855,4969,5213,5371,5478,5645,5775,5927,6079,6149,6211", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "4172,4337,4466,4573,4720,4850,4964,5068,5366,5473,5640,5770,5922,6074,6144,6206,6290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,253,374,512,642,768,893,1033,1132,1275,1409", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "157,248,369,507,637,763,888,1028,1127,1270,1404,1537"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6295,6604,7196,7317,7455,7585,7711,7836,7976,8075,8218,8352", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "6397,6690,7312,7450,7580,7706,7831,7971,8070,8213,8347,8480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14962", "endColumns": "91", "endOffsets": "15049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2851,2960", "endColumns": "108,121", "endOffsets": "2955,3077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2568,2751,2851", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,107,182,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2563,2746,2846,2930"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2568,2751,15396", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,107,182,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2563,2746,2846,15475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3172,3269,3371,3469,3573,3676,3778,15875", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3264,3366,3464,3568,3671,3773,3890,15971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cdb44a9a24015f2bfec4b4de89ce351f\\transformed\\browser-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6402,6878,6982,7094", "endColumns": "105,103,111,101", "endOffsets": "6503,6977,7089,7191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8649,8778,8907,9025,9154,9264,9360,9473,9613,9739,9882,9967,10066,10159,10256,10373,10495,10599,10736,10870,11001,11185,11312,11435,11560,11682,11776,11874,11994,12118,12218,12327,12433,12576,12723,12832,12934,13018,13113,13209,13317,13405,13491,13594,13676,13759,13854,13954,14045,14142,14230,14334,14431,14533,14675,14757,14863", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "8773,8902,9020,9149,9259,9355,9468,9608,9734,9877,9962,10061,10154,10251,10368,10490,10594,10731,10865,10996,11180,11307,11430,11555,11677,11771,11869,11989,12113,12213,12322,12428,12571,12718,12827,12929,13013,13108,13204,13312,13400,13486,13589,13671,13754,13849,13949,14040,14137,14225,14329,14426,14528,14670,14752,14858,14957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,291,372,468,566,651,728,815,907,989,1071,1157,1229,1317,1394,1474,1552,1630,1700", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "286,367,463,561,646,723,810,902,984,1066,1152,1224,1312,1389,1469,1547,1625,1695,1816"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3895,3990,6508,6695,6793,8485,8562,15054,15146,15228,15310,15480,15552,15640,15717,15797,15976,16054,16124", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "3985,4066,6599,6788,6873,8557,8644,15141,15223,15305,15391,15547,15635,15712,15792,15870,16049,16119,16240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,90", "endOffsets": "140,230,321"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3082,16245,16335", "endColumns": "89,89,90", "endOffsets": "3167,16330,16421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5073", "endColumns": "139", "endOffsets": "5208"}}]}]}
{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-77:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2989e2c91f6d0a9894b01384862e89f4\\transformed\\play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5027", "endColumns": "149", "endOffsets": "5172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\718231422f48010e85ca6edf2e677575\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,225", "endColumns": "83,85,88", "endOffsets": "134,220,309"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3022,16128,16214", "endColumns": "83,85,88", "endOffsets": "3101,16209,16298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45f9a3db26e33e80ab043af5a0e43024\\transformed\\browser-1.8.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6343,6820,6924,7029", "endColumns": "104,103,104,107", "endOffsets": "6443,6919,7024,7132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\396ff811c0e00aef5e8ac116f6d99809\\transformed\\material-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14859", "endColumns": "94", "endOffsets": "14949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dff1d73b7cef632e787de4d59cae0ad8\\transformed\\biometric-1.1.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,265,386,521,659,795,923,1073,1167,1301,1437", "endColumns": "108,100,120,134,137,135,127,149,93,133,135,115", "endOffsets": "159,260,381,516,654,790,918,1068,1162,1296,1432,1548"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6234,6540,7137,7258,7393,7531,7667,7795,7945,8039,8173,8309", "endColumns": "108,100,120,134,137,135,127,149,93,133,135,115", "endOffsets": "6338,6636,7253,7388,7526,7662,7790,7940,8034,8168,8304,8420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\75e92b145e13fc673dc76999f901f30d\\transformed\\credentials-1.5.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,113", "endOffsets": "161,275"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2797,2908", "endColumns": "110,113", "endOffsets": "2903,3017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72b42ec2c0a6db09976b88668f84c08b\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "190,282,368,460,556,639,725,819,906,987,1070,1153,1226,1317,1394,1474,1551,1628,1694", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "277,363,455,551,634,720,814,901,982,1065,1148,1221,1312,1389,1469,1546,1623,1689,1806"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3841,3933,6448,6641,6737,8425,8511,14954,15041,15122,15205,15369,15442,15533,15610,15690,15868,15945,16011", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "3928,4014,6535,6732,6815,8506,8600,15036,15117,15200,15283,15437,15528,15605,15685,15762,15940,16006,16123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4dba44ce16815e024cb356c378b89b2b\\transformed\\appcompat-1.2.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,15288", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,15364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55690b61ef7b0489a32a7ec346714862\\transformed\\core-1.16.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3106,3204,3306,3407,3505,3610,3722,15767", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3199,3301,3402,3500,3605,3717,3836,15863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11771762b22044a889273e9ed7a93127\\transformed\\material3-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8605,8724,8840,8952,9069,9168,9265,9379,9521,9639,9778,9863,9965,10057,10155,10273,10395,10502,10644,10788,10920,11096,11222,11343,11463,11582,11675,11775,11898,12036,12135,12241,12347,12491,12636,12743,12842,12925,13020,13114,13225,13310,13394,13495,13575,13658,13757,13857,13952,14054,14141,14245,14344,14449,14580,14660,14764", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "8719,8835,8947,9064,9163,9260,9374,9516,9634,9773,9858,9960,10052,10150,10268,10390,10497,10639,10783,10915,11091,11217,11338,11458,11577,11670,11770,11893,12031,12130,12236,12342,12486,12631,12738,12837,12920,13015,13109,13220,13305,13389,13490,13570,13653,13752,13852,13947,14049,14136,14240,14339,14444,14575,14655,14759,14854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e866c3f6718b50411e7e0aa30260699c\\transformed\\play-services-base-18.5.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4019,4129,4286,4418,4525,4662,4788,4917,5177,5321,5428,5596,5725,5866,6034,6095,6157", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "4124,4281,4413,4520,4657,4783,4912,5022,5316,5423,5591,5720,5861,6029,6090,6152,6229"}}]}]}
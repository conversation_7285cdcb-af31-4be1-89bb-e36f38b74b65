{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\acb9798c8ebd587acff296741d454536\\transformed\\material-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14649", "endColumns": "90", "endOffsets": "14735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61468bcbedfc450e580f06e70db30d2d\\transformed\\play-services-base-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3988,4094,4248,4373,4482,4623,4748,4857,5091,5245,5351,5508,5634,5776,5930,5994,6057", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "4089,4243,4368,4477,4618,4743,4852,4957,5240,5346,5503,5629,5771,5925,5989,6052,6131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49d658310de5de4964d0a45e6cf7e2a2\\transformed\\browser-1.4.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6245,6721,6822,6928", "endColumns": "103,100,105,100", "endOffsets": "6344,6817,6923,7024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bef8df5a53316d0138000accd463da68\\transformed\\biometric-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,253,369,500,631,756,882,1009,1108,1250,1392", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "159,248,364,495,626,751,877,1004,1103,1245,1387,1504"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6136,6448,7029,7145,7276,7407,7532,7658,7785,7884,8026,8168", "endColumns": "108,88,115,130,130,124,125,126,98,141,141,116", "endOffsets": "6240,6532,7140,7271,7402,7527,7653,7780,7879,8021,8163,8280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4adc15677f48e771c055defc45283b3c\\transformed\\core-1.16.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3099,3194,3301,3398,3498,3601,3705,15534", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3189,3296,3393,3493,3596,3700,3811,15630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee8ce6d4d9b244361a0641d37775e7c0\\transformed\\appcompat-1.2.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,15072", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,15148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a819a590a1c3b9b1aa788908f3957125\\transformed\\credentials-1.5.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,119", "endOffsets": "164,284"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2779,2893", "endColumns": "113,119", "endOffsets": "2888,3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaaba50a6f61c3e2d02a4aaf04b349a6\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,228", "endColumns": "85,86,86", "endOffsets": "136,223,310"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3013,15900,15987", "endColumns": "85,86,86", "endOffsets": "3094,15982,16069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1ba429aa6de3be7de3bb6e415af4e51\\transformed\\play-services-basement-18.4.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4962", "endColumns": "128", "endOffsets": "5086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f30b1b16ade526cf11fedcbcce9e4989\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,463,562,647,727,822,911,993,1071,1154,1224,1311,1386,1461,1535,1612,1680", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "278,359,458,557,642,722,817,906,988,1066,1149,1219,1306,1381,1456,1530,1607,1675,1795"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3816,3907,6349,6537,6636,8285,8365,14740,14829,14911,14989,15153,15223,15310,15385,15460,15635,15712,15780", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "3902,3983,6443,6631,6716,8360,8455,14824,14906,14984,15067,15218,15305,15380,15455,15529,15707,15775,15895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1acecd73857259263b94b4bf35ef3376\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8460,8574,8686,8793,8905,9002,9101,9217,9358,9485,9620,9710,9811,9908,10008,10123,10249,10355,10480,10604,10746,10917,11040,11156,11275,11397,11495,11593,11702,11824,11930,12038,12141,12271,12406,12514,12619,12695,12789,12882,12996,13081,13166,13275,13355,13446,13547,13648,13743,13851,13939,14044,14145,14251,14371,14451,14553", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "8569,8681,8788,8900,8997,9096,9212,9353,9480,9615,9705,9806,9903,10003,10118,10244,10350,10475,10599,10741,10912,11035,11151,11270,11392,11490,11588,11697,11819,11925,12033,12136,12266,12401,12509,12614,12690,12784,12877,12991,13076,13161,13270,13350,13441,13542,13643,13738,13846,13934,14039,14140,14246,14366,14446,14548,14644"}}]}]}
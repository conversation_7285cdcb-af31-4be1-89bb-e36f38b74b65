# Supabase Database Setup Instructions

## Step 1: Access Supabase SQL Editor

1. Go to your Supabase dashboard: https://supabase.com/dashboard
2. Select your project: `TaskIQ`
3. Click **"SQL Editor"** in the left sidebar
4. Click **"New query"**

## Step 2: Run Database Schema

Copy and paste the entire content from `app/src/main/assets/supabase_schema.sql` into the SQL editor, then click **"Run"**.

This will create:
- ✅ 5 tables: users, tasks, bills, important_dates, linked_emails
- ✅ Row Level Security policies (users can only access their own data)
- ✅ Indexes for better performance
- ✅ Auto-update triggers for timestamps

## Step 3: Verify Setup

After running the schema, verify in **Table Editor**:
- Go to **"Table Editor"** in left sidebar
- You should see 5 tables listed
- Each table should have the correct columns

## Step 4: Configure Authentication

1. Go to **"Authentication"** in left sidebar
2. Click **"Settings"** tab
3. **Disable** "Enable email confirmations" for testing
4. You can enable it later for production

## Step 5: Test Connection

Your app should now connect to Supabase successfully!

## Security Notes

✅ **Credentials are stored securely**:
- In `local.properties` (not committed to Git)
- Accessed via BuildConfig at runtime
- Anon key is safe for client apps

✅ **Database security**:
- Row Level Security ensures users only see their own data
- All tables have proper access policies

{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-82:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\30370bda1c96ec9fa500b7aba944c555\\transformed\\biometric-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,254,374,521,650,800,925,1078,1177,1319,1478", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "159,249,369,516,645,795,920,1073,1172,1314,1473,1602"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6248,6566,7161,7281,7428,7557,7707,7832,7985,8084,8226,8385", "endColumns": "108,89,119,146,128,149,124,152,98,141,158,128", "endOffsets": "6352,6651,7276,7423,7552,7702,7827,7980,8079,8221,8380,8509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53a84e368518001ab3aa41c1fdeafdf2\\transformed\\play-services-base-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4044,4150,4299,4428,4535,4680,4807,4922,5189,5358,5465,5615,5745,5882,6046,6110,6170", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "4145,4294,4423,4530,4675,4802,4917,5026,5353,5460,5610,5740,5877,6041,6105,6165,6243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b0aa768bf2c77d0f2a11c0fea0a786\\transformed\\browser-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6357,6841,6946,7055", "endColumns": "98,104,108,105", "endOffsets": "6451,6941,7050,7156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aa4239f854065c6cb76db75da03d84d7\\transformed\\material3-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4718,4804,4889,5001,5090,5174,5274,5375,5471,5568,5655,5766,5865,5965,6113,6203,6322", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4713,4799,4884,4996,5085,5169,5269,5370,5466,5563,5650,5761,5860,5960,6108,6198,6317,6425"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8694,8817,8934,9048,9173,9273,9371,9486,9622,9763,9919,10003,10101,10193,10290,10406,10525,10628,10764,10898,11035,11210,11339,11456,11576,11697,11790,11888,12010,12147,12250,12375,12480,12614,12753,12862,12964,13040,13139,13243,13357,13443,13528,13640,13729,13813,13913,14014,14110,14207,14294,14405,14504,14604,14752,14842,14961", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "8812,8929,9043,9168,9268,9366,9481,9617,9758,9914,9998,10096,10188,10285,10401,10520,10623,10759,10893,11030,11205,11334,11451,11571,11692,11785,11883,12005,12142,12245,12370,12475,12609,12748,12857,12959,13035,13134,13238,13352,13438,13523,13635,13724,13808,13908,14009,14105,14202,14289,14400,14499,14599,14747,14837,14956,15064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\764023a455144f62a86bfbce3d6749a4\\transformed\\credentials-1.5.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,122", "endOffsets": "160,283"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2817,2927", "endColumns": "109,122", "endOffsets": "2922,3045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\462f19984ab468993b4f437402a2d36f\\transformed\\play-services-basement-18.4.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5031", "endColumns": "157", "endOffsets": "5184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\63697056f69d193aacd905c2ec4f8045\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,287,371,481,581,666,748,846,935,1020,1105,1192,1265,1352,1426,1499,1572,1651,1719", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "282,366,476,576,661,743,841,930,1015,1100,1187,1260,1347,1421,1494,1567,1646,1714,1832"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3867,3960,6456,6656,6756,8514,8596,15160,15249,15334,15419,15588,15661,15748,15822,15895,16069,16148,16216", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "3955,4039,6561,6751,6836,8591,8689,15244,15329,15414,15501,15656,15743,15817,15890,15963,16143,16211,16329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ab01b5e3b8e2accd167ccfeb3d776bdb\\transformed\\appcompat-1.2.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,15506", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,15583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7be1cd8404036620e7b9e129b051e87f\\transformed\\foundation-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,242", "endColumns": "87,98,103", "endOffsets": "138,237,341"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3050,16334,16433", "endColumns": "87,98,103", "endOffsets": "3133,16428,16532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\502b5ea5387ec613f4fdeabddbbf0af9\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3138,3238,3340,3443,3550,3652,3756,15968", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3233,3335,3438,3545,3647,3751,3862,16064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\904e1630c563f33d0f5478f7716fee6a\\transformed\\material-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "15069", "endColumns": "90", "endOffsets": "15155"}}]}]}
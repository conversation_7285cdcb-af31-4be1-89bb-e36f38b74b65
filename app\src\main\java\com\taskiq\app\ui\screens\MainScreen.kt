package com.taskiq.app.ui.screens

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Backup
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.PrivacyTip
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.DrawerState
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.NavigationDrawerItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.ViewModelProvider
import android.app.Application
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.offset
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import com.taskiq.app.ui.components.BottomNavigationBar
import com.taskiq.app.ui.navigation.BottomNavItem
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.screens.NotificationSettingsScreen
import com.taskiq.app.ui.screens.DataBackupScreen
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalLightBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp
import com.taskiq.app.viewmodel.NotificationSettingsViewModel
import com.taskiq.app.viewmodel.TaskViewModel
import kotlinx.coroutines.launch
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.viewmodel.AuthViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    authViewModel: AuthViewModel,
    onLogout: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    val navController = rememberNavController()
    val context = LocalContext.current
    val taskViewModel: TaskViewModel = viewModel(
        factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
    )
    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
    val scope = rememberCoroutineScope()

    // Get current user data
    val currentUser by authViewModel.user.collectAsState()

    // Helper functions for user display
    val userName = remember(currentUser) {
        val user = currentUser
        if (user != null) {
            "${user.firstName} ${user.lastName}".trim().takeIf { it.isNotBlank() } ?: "User"
        } else {
            "User"
        }
    }

    val userInitials = remember(currentUser) {
        val user = currentUser
        if (user != null) {
            val firstName = user.firstName.trim()
            val lastName = user.lastName.trim()
            val firstInitial = firstName.firstOrNull()?.uppercaseChar() ?: ""
            val lastInitial = lastName.firstOrNull()?.uppercaseChar() ?: ""
            "$firstInitial$lastInitial".takeIf { it.isNotBlank() } ?: "U"
        } else {
            "U"
        }
    }

    val userEmail = remember(currentUser) {
        val user = currentUser
        user?.email?.takeIf { it.isNotBlank() } ?: "<EMAIL>"
    }

    // State for bottom navigation visibility
    var isBottomBarVisible by rememberSaveable { mutableStateOf(true) }
    
    // Track current screen to customize app bar
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    val isTasksScreen = currentRoute == BottomNavItem.Tasks.route
    
    // Track if we're showing the Add/Edit Important Date screen
    var showingAddDateScreen by rememberSaveable { mutableStateOf(false) }
    // Track if we're showing the Add/Edit Task screen
    var showingAddTaskScreen by rememberSaveable { mutableStateOf(false) }
    // Track if we're showing the Add/Edit Bill screen
    var showingEditBillScreen by rememberSaveable { mutableStateOf(false) }
    // Track if we're showing the Email Management screen
    var showingEmailManagementScreen by rememberSaveable { mutableStateOf(false) }
    
    // Handle device back button when drawer is open
    BackHandler(enabled = drawerState.isOpen) {
        scope.launch {
            drawerState.close()
        }
    }

    // Check if we're on a screen that should be rendered outside the Scaffold
    val isFullScreenRoute = currentRoute == Routes.NOTIFICATIONS ||
                            currentRoute == Routes.NOTIFICATION_SETTINGS ||
                            currentRoute == Routes.DATA_BACKUP

    android.util.Log.d("DATABACKUP", "Current route: $currentRoute, isFullScreenRoute: $isFullScreenRoute")
    
    if (isFullScreenRoute) {
        android.util.Log.d("DATABACKUP", "Handling full screen route: $currentRoute")
        
        when (currentRoute) {
            Routes.NOTIFICATIONS -> {
                android.util.Log.d("DATABACKUP", "Showing NotificationScreen")
                NotificationScreen(navController = navController, taskViewModel = taskViewModel)
            }
            Routes.NOTIFICATION_SETTINGS -> {
                android.util.Log.d("DATABACKUP", "Showing NotificationSettingsScreen") 
                NotificationSettingsScreen(navController = navController)
            }
            Routes.DATA_BACKUP -> {
                android.util.Log.d("DATABACKUP", "Showing DataBackupScreen")
                // Create an instance of BackupViewModel
                val backupViewModel = androidx.lifecycle.viewmodel.compose.viewModel<com.taskiq.app.viewmodel.BackupViewModel>(
                    factory = androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory(LocalContext.current.applicationContext as android.app.Application)
                )
                // Show the DataBackupScreen directly
                DataBackupScreen(
                    viewModel = backupViewModel,
                    onNavigateBack = { navController.popBackStack() },
                    navController = navController
                )
            }


            else -> {}
        }
        return
    }
    
    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet(
                modifier = Modifier.fillMaxWidth(),
                windowInsets = WindowInsets(0, 0, 0, 0)
            ) {
                // Header with back button and profile info
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .background(ProfessionalBlue)
                    .padding(top = 4.sdp(), bottom = 12.sdp())
                    .statusBarsPadding()
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(start = 4.sdp(), bottom = 8.sdp())
                    ) {
                        IconButton(
                            onClick = { scope.launch { drawerState.close() } },
                            modifier = Modifier.size(40.sdp())
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Close Drawer",
                                tint = ProfessionalWhite
                            )
                        }
                    }

                    // User profile
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(start = 12.sdp())
                    ) {
                        // Profile icon based on gender
                        Box(
                            modifier = Modifier
                                .size(50.sdp())
                                .clip(CircleShape)
                                .background(ProfessionalWhite.copy(alpha = 0.9f)),
                            contentAlignment = Alignment.Center
                        ) {
                            // Show gender-based icon if gender is selected, otherwise show initials
                            val user = currentUser
                            if (user?.gender != null && user.gender != "Select") {
                                Icon(
                                    imageVector = when (user.gender) {
                                        "Male" -> Icons.Default.Person
                                        "Female" -> Icons.Default.Person
                                        "Other" -> Icons.Default.Person
                                        else -> Icons.Default.Person
                                    },
                                    contentDescription = "Profile (${user.gender})",
                                    tint = when (user.gender) {
                                        "Male" -> ProfessionalBlue
                                        "Female" -> Color(0xFFE91E63) // Pink for female
                                        "Other" -> Color(0xFF9C27B0) // Purple for other
                                        else -> ProfessionalBlue
                                    },
                                    modifier = Modifier.size(30.sdp())
                                )
                            } else {
                                Text(
                                    text = userInitials,
                                    color = ProfessionalBlue,
                                    fontSize = 22.ssp(),
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }

                        // User info
                        Column(modifier = Modifier.padding(start = 12.sdp())) {
                            Text(
                                text = userName,
                                color = ProfessionalWhite,
                                fontSize = 20.ssp(),
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = userEmail,
                                color = ProfessionalWhite.copy(alpha = 0.8f),
                                fontSize = 14.ssp()
                            )
                        }
                    }
                }
                
                // Menu items
                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Edit Profile",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("Edit Profile", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            drawerState.close()
                            onNavigateToProfile()
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 1.sdp())
                )
                
                NavigationDrawerItem(
                    icon = { 
                        Icon(
                            imageVector = Icons.Default.Backup,
                            contentDescription = "Data Backup",
                            tint = ProfessionalBlue
                        ) 
                    },
                    label = { Text("Data Backup", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            try {
                                android.util.Log.d("DATABACKUP", "Starting navigation to Data Backup screen")
                                drawerState.close()
                                android.util.Log.d("DATABACKUP", "Drawer closed, navigating to DATA_BACKUP")
                                
                                // Simplify navigation to just use Routes.DATA_BACKUP
                                navController.navigate(Routes.DATA_BACKUP)
                                
                                android.util.Log.d("DATABACKUP", "Navigation complete")
                            } catch (e: Exception) {
                                android.util.Log.e("DATABACKUP", "Error navigating to Data Backup", e)
                                // Use Toast without LocalContext 
                                android.widget.Toast.makeText(
                                    navController.context,
                                    "Error accessing Data Backup: ${e.message}",
                                    android.widget.Toast.LENGTH_LONG
                                ).show()
                                
                                // Log the error for debugging
                                android.util.Log.e("MainScreen", "Error navigating to Data Backup", e)
                            }
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )

                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "Settings",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("Settings", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            drawerState.close()
                            onNavigateToSettings()
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )

                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = "About Us",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("About Us", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            drawerState.close()
                            navController.navigate(Routes.ABOUT_US)
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )

                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.Default.PrivacyTip,
                            contentDescription = "Privacy Policy",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("Privacy Policy", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            drawerState.close()
                            navController.navigate(Routes.PRIVACY_POLICY)
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )

                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.Default.Description,
                            contentDescription = "Terms of Use",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("Terms of Use", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = {
                        scope.launch {
                            drawerState.close()
                            navController.navigate(Routes.TERMS_OF_USE)
                        }
                    },
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )

                NavigationDrawerItem(
                    icon = {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ExitToApp,
                            contentDescription = "Logout",
                            tint = ProfessionalBlue
                        )
                    },
                    label = { Text("Log Out", fontSize = 14.ssp()) },
                    selected = false,
                    onClick = onLogout,
                    modifier = Modifier.padding(horizontal = 12.sdp(), vertical = 0.sdp())
                )
                
                // Footer with app info
                Spacer(modifier = Modifier.weight(1f))
                
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 34.sdp()),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Made with ❤️ in Bharat",
                        fontSize = 13.ssp(),
                        textAlign = TextAlign.Center
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = 8.sdp())
                    ) {
                        Text(
                            text = "TaskIQ",
                            color = ProfessionalBlue,
                            fontSize = 28.ssp(),
                            fontWeight = FontWeight.Bold
                        )
                    }

                    Text(
                        text = "For minds that multitask",
                        color = Color.DarkGray,
                        fontSize = 10.ssp(),
                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                    )
                    
                    Spacer(modifier = Modifier.height(20.sdp()))
                    
                    Text(
                        text = "Version 1.0.0",
                        color = Color.DarkGray,
                        fontSize = 12.ssp()
                    )
                }
            }
        },
        gesturesEnabled = false // Disable finger gestures - drawer opens only via menu button
    ) {
        // All screens are now handled within the Scaffold
        
        Scaffold(
                topBar = {
                    // Only show TopAppBar when not showing Add/Edit screens or Email Management screen or new pages
                    val hideTopBar = showingAddDateScreen || showingAddTaskScreen || showingEditBillScreen || showingEmailManagementScreen ||
                                   currentRoute == Routes.ABOUT_US || currentRoute == Routes.PRIVACY_POLICY || currentRoute == Routes.TERMS_OF_USE
                    
                    if (!hideTopBar) {
                        if (isTasksScreen) {
                            // Custom top app bar for Tasks screen
                            TopAppBar(
                                title = {
                                    Text(
                                        text = "Tasks",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.ssp()
                                    )
                                },
                                colors = TopAppBarDefaults.topAppBarColors(
                                    containerColor = topAppBarColor(),
                                    titleContentColor = ProfessionalWhite,
                                    navigationIconContentColor = ProfessionalWhite
                                ),
                                navigationIcon = {
                                    IconButton(
                                        onClick = {
                                            navController.navigate(BottomNavItem.Dashboard.route) {
                                                popUpTo(navController.graph.findStartDestination().id)
                                                launchSingleTop = true
                                            }
                                        },
                                        modifier = Modifier.padding(end = 0.sdp())
                                    ) {
                                        Icon(
                                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                            contentDescription = "Back to Dashboard"
                                        )
                                    }
                                },
                                windowInsets = WindowInsets.statusBars
                            )
                        } else if (currentRoute == BottomNavItem.Bills.route || currentRoute == BottomNavItem.Dates.route) {
                            // Custom top app bar for Bills and Dates screens
                            TopAppBar(
                                title = {
                                    Text(
                                        text = if (currentRoute == BottomNavItem.Bills.route) "Bills" else "Dates",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.ssp()
                                    )
                                },
                                colors = TopAppBarDefaults.topAppBarColors(
                                    containerColor = topAppBarColor(),
                                    titleContentColor = ProfessionalWhite,
                                    navigationIconContentColor = ProfessionalWhite
                                ),
                                navigationIcon = {
                                    IconButton(
                                        onClick = {
                                            navController.navigate(BottomNavItem.Dashboard.route) {
                                                popUpTo(navController.graph.findStartDestination().id)
                                                launchSingleTop = true
                                            }
                                        },
                                        modifier = Modifier.padding(end = 0.sdp())
                                    ) {
                                        Icon(
                                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                            contentDescription = "Back to Dashboard"
                                        )
                                    }
                                },
                                windowInsets = WindowInsets.statusBars
                            )
                        } else if (currentRoute == Routes.NOTIFICATION_SETTINGS) {
                            // Custom top app bar for Notification Settings screen
                            TopAppBar(
                                title = {
                                    Text(
                                        text = "Notification Settings",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.ssp()
                                    )
                                },
                                colors = TopAppBarDefaults.topAppBarColors(
                                    containerColor = topAppBarColor(),
                                    titleContentColor = ProfessionalWhite,
                                    navigationIconContentColor = ProfessionalWhite
                                ),
                                navigationIcon = {
                                    IconButton(
                                        onClick = { navController.popBackStack() },
                                        modifier = Modifier.padding(end = 0.sdp())
                                    ) {
                                        Icon(
                                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                            contentDescription = "Back"
                                        )
                                    }
                                },
                                windowInsets = WindowInsets.statusBars
                            )
                        } else {
                            // Standard top app bar for other screens
                            TopAppBar(
                                title = {
                                    Text(
                                        text = "TaskIQ",
                                        modifier = Modifier.fillMaxWidth(),
                                        textAlign = TextAlign.Center,
                                        color = ProfessionalWhite,
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 20.ssp()
                                    )
                                },
                                navigationIcon = {
                                    IconButton(onClick = { scope.launch { drawerState.open() } }) {
                                        Icon(
                                            imageVector = Icons.Default.Menu,
                                            contentDescription = "Menu",
                                            tint = ProfessionalWhite
                                        )
                                    }
                                },
                                actions = {
                                    IconButton(onClick = { navController.navigate(Routes.NOTIFICATIONS) }) {
                                        Icon(
                                            imageVector = Icons.Default.Notifications,
                                            contentDescription = "Notifications",
                                            tint = ProfessionalWhite
                                        )
                                    }
                                },
                                colors = TopAppBarDefaults.topAppBarColors(
                                    containerColor = topAppBarColor(),
                                    titleContentColor = ProfessionalWhite,
                                    navigationIconContentColor = ProfessionalWhite,
                                    actionIconContentColor = ProfessionalWhite
                                ),
                                windowInsets = WindowInsets.statusBars
                            )
                        }
                    }
                },
                bottomBar = {
                    // Show bottom navigation on all main screens (Dashboard, Tasks, Bills, Dates) when not showing Add/Edit screens
                    val currentDestination = navBackStackEntry?.destination
                    val isMainScreen = currentDestination?.route in listOf(
                        BottomNavItem.Dashboard.route,
                        BottomNavItem.Tasks.route,
                        BottomNavItem.Bills.route,
                        BottomNavItem.Dates.route
                    )
                    if (isMainScreen &&
                        !showingAddDateScreen &&
                        !showingAddTaskScreen &&
                        !showingEditBillScreen &&
                        !showingEmailManagementScreen &&
                        isBottomBarVisible) { // Only show when visible
                        com.taskiq.app.ui.components.BottomNavigationBar(navController = navController)
                    }
                },
                containerColor = Color(0xFFF8F8F8), // Off-white background that matches bottom navigation
                contentWindowInsets = WindowInsets(0, 0, 0, 0)
            ) { innerPadding ->
            NavHost(
                navController = navController,
                startDestination = BottomNavItem.Dashboard.route,
                modifier = Modifier.padding(innerPadding)
            ) {
                composable(BottomNavItem.Dashboard.route) {
                    DashboardScreen(
                        onLogout = onLogout,
                        taskViewModel = taskViewModel,
                        onNavigateToTasks = { navController.navigate(BottomNavItem.Tasks.route) },
                        onNavigateToDates = { navController.navigate(BottomNavItem.Dates.route) },
                        onNavigateToBills = { navController.navigate(BottomNavItem.Bills.route) },
                        onScrollStateChanged = { isScrollingDown ->
                            isBottomBarVisible = !isScrollingDown
                        }
                    )
                }
                
                composable(BottomNavItem.Tasks.route) {
                    TasksScreen(
                        taskViewModel = taskViewModel,
                        // No need for the back button in the content since it's in the header
                        onBackClick = {
                            navController.navigate(BottomNavItem.Dashboard.route) {
                                popUpTo(navController.graph.findStartDestination().id)
                                launchSingleTop = true
                            }
                        },
                        onShowAddTaskScreen = { showing -> showingAddTaskScreen = showing },
                        onScrollStateChanged = { isScrollingDown ->
                            isBottomBarVisible = !isScrollingDown
                        }
                    )
                }
                
                composable(BottomNavItem.Dates.route) {
                    DatesScreen(
                        taskViewModel = taskViewModel,
                        onBackClick = {
                            navController.navigate(BottomNavItem.Dashboard.route) {
                                popUpTo(navController.graph.findStartDestination().id)
                                launchSingleTop = true
                            }
                        },
                        onShowAddDateScreen = { showing -> showingAddDateScreen = showing },
                        onScrollStateChanged = { isScrollingDown ->
                            isBottomBarVisible = !isScrollingDown
                        }
                    )
                }
                
                composable(BottomNavItem.Bills.route) {
                    BillsScreen(
                        viewModel = taskViewModel,
                        onShowEditBillScreen = { showing -> showingEditBillScreen = showing },
                        onShowEmailManagementScreen = { showing -> showingEmailManagementScreen = showing },
                        onNavigateToGmailAuth = {
                            navController.navigate(Routes.GMAIL_AUTH)
                        },
                        onScrollStateChanged = { isScrollingDown ->
                            isBottomBarVisible = !isScrollingDown
                        }
                    )
                }
                
                // Add notification screens to the main NavHost
                composable(Routes.NOTIFICATIONS) {
                    NotificationScreen(
                        navController = navController,
                        taskViewModel = taskViewModel
                    )
                }
                
                composable(Routes.NOTIFICATION_SETTINGS) {
                    val notificationSettingsViewModel: NotificationSettingsViewModel = viewModel()
                    NotificationSettingsScreen(
                        navController = navController,
                        viewModel = notificationSettingsViewModel
                    )
                }
                
                // Add DATA_BACKUP route to the main NavHost
                composable(Routes.DATA_BACKUP) {
                    val context = LocalContext.current
                    android.util.Log.d("DATABACKUP", "MainScreen NavHost - DATA_BACKUP route triggered")

                    val backupViewModel = androidx.lifecycle.viewmodel.compose.viewModel<com.taskiq.app.viewmodel.BackupViewModel>(
                        factory = androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory(context.applicationContext as android.app.Application)
                    )

                    DataBackupScreen(
                        viewModel = backupViewModel,
                        onNavigateBack = { navController.popBackStack() },
                        navController = navController
                    )

                    android.util.Log.d("DATABACKUP", "MainScreen NavHost - DataBackupScreen rendered")
                }

                // Add GMAIL_AUTH route to the main NavHost
                composable(Routes.GMAIL_AUTH) {
                    val context = LocalContext.current
                    val gmailAuthViewModel = androidx.lifecycle.viewmodel.compose.viewModel<com.taskiq.app.viewmodel.GmailAuthViewModel>(
                        factory = androidx.lifecycle.ViewModelProvider.AndroidViewModelFactory(context.applicationContext as android.app.Application)
                    )

                    com.taskiq.app.ui.screens.GmailAuthScreen(
                        navController = navController,
                        viewModel = gmailAuthViewModel,
                        taskViewModel = taskViewModel
                    )
                }

                // Add new pages to the main NavHost
                composable(Routes.ABOUT_US) {
                    com.taskiq.app.ui.screens.AboutUsScreen(navController = navController)
                }

                composable(Routes.PRIVACY_POLICY) {
                    com.taskiq.app.ui.screens.PrivacyPolicyScreen(navController = navController)
                }

                composable(Routes.TERMS_OF_USE) {
                    com.taskiq.app.ui.screens.TermsOfUseScreen(navController = navController)
                }
            }
        }
    }
}





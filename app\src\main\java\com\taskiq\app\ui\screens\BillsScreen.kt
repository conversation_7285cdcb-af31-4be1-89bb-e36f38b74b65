package com.taskiq.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.zIndex
import java.time.LocalDate

import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.ui.components.BillDialogContent
import com.taskiq.app.ui.components.BillItem
import com.taskiq.app.ui.components.BillSummary
import com.taskiq.app.ui.screens.EmailManagementScreen
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.uniformContentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformContentPadding
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.uniformInitialPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformItemSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing
import com.taskiq.app.ui.theme.uniformEmptyStatePadding
import com.taskiq.app.ui.theme.uniformBottomSpacing
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveSmallIconSize
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.sdp

enum class BillFilterOption {
    ALL, PENDING, PAID, OVERDUE
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillsScreen(
    viewModel: TaskViewModel,
    onShowEditBillScreen: (Boolean) -> Unit = {}, // New callback to notify parent when bill is being edited
    onShowEmailManagementScreen: (Boolean) -> Unit = {}, // New callback to notify parent when email management is being shown
    onNavigateToGmailAuth: () -> Unit = {}, // New callback to navigate to Gmail auth screen
    onScrollStateChanged: (Boolean) -> Unit = {} // Callback for scroll state changes
) {
    // Use uniform padding system for consistency across all screens

    val bills by viewModel.bills.collectAsState()
    var isLoading by remember { mutableStateOf(false) }
    var showAddBillDialog by remember { mutableStateOf(false) }
    var showEmailScanningDialog by remember { mutableStateOf(false) }

    // Update isLoading from viewModel
    LaunchedEffect(viewModel.isLoading) {
        isLoading = viewModel.isLoading
    }

    // Scroll state for hide-on-scroll behavior
    val listState = rememberLazyListState()
    var previousScrollOffset by remember { mutableStateOf(0) }

    val isScrollingDown by remember {
        derivedStateOf {
            val currentOffset = listState.firstVisibleItemScrollOffset
            val isScrollingDown = currentOffset > previousScrollOffset && currentOffset > 100
            previousScrollOffset = currentOffset
            isScrollingDown
        }
    }

    // Update scroll state - hide when scrolling down, show only when at top
    LaunchedEffect(listState.firstVisibleItemScrollOffset) {
        val isAtTop = listState.firstVisibleItemScrollOffset <= 50
        onScrollStateChanged(!isAtTop)
    }
    
    // For editing bills
    var editingBill by remember { mutableStateOf<Bill?>(null) }
    
    // Effect to notify parent when bill editing dialog is shown
    LaunchedEffect(showAddBillDialog, editingBill) {
        onShowEditBillScreen(showAddBillDialog || editingBill != null)
    }

    // Effect to notify parent when email management screen is shown
    LaunchedEffect(showEmailScanningDialog) {
        onShowEmailManagementScreen(showEmailScanningDialog)
    }

    // Load linked emails when screen is first displayed
    LaunchedEffect(Unit) {
        android.util.Log.d("BillsScreen", "Loading linked emails on screen initialization")
        viewModel.loadLinkedEmails()
    }

    val linkedEmails = viewModel.linkedEmails.collectAsState().value
    val isScanning = viewModel.isEmailScanningInProgress
    val today = LocalDate.now()

    // Debug logging for linked emails
    LaunchedEffect(linkedEmails) {
        android.util.Log.d("BillsScreen", "Linked emails updated: $linkedEmails (count: ${linkedEmails.size})")
    }
    
    // Get all bills
    val filteredBills = bills
    
    // Group bills by category for better organization
    val pendingBills = filteredBills.filter {
        it.status == BillStatus.PENDING && !it.dueDate.isBefore(today)
    }

    val overdueBills = filteredBills.filter {
        it.status == BillStatus.OVERDUE ||
        (it.status == BillStatus.PENDING && it.dueDate.isBefore(today))
    }

    // Limit pending and overdue bills to 100 total
    val combinedPendingOverdue = (pendingBills + overdueBills).take(100)
    val limitedPendingBills = combinedPendingOverdue.filter {
        it.status == BillStatus.PENDING && !it.dueDate.isBefore(today)
    }
    val limitedOverdueBills = combinedPendingOverdue.filter {
        it.status == BillStatus.OVERDUE ||
        (it.status == BillStatus.PENDING && it.dueDate.isBefore(today))
    }

    // Limit paid bills to last 50 paid bills (sorted by payment date)
    val paidBills = filteredBills.filter { it.status == BillStatus.PAID }
        .sortedByDescending { it.paymentDate ?: it.createdAt }
        .take(50)
    
    // Show add/edit bill dialog in full screen mode when triggered
    if (showAddBillDialog || editingBill != null) {
        // Use a completely separate screen for the dialog content
        // This prevents inheriting the top app bar from the parent screen
        BillDialogContent(
            editingBill = editingBill,
            onDismiss = { 
                if (editingBill != null) {
                    editingBill = null
                } else {
                    showAddBillDialog = false
                }
            },
            onBillAdded = { bill ->
                if (editingBill != null) {
                    viewModel.updateBill(bill)
                    editingBill = null
                } else {
                    viewModel.addBill(bill)
                    showAddBillDialog = false
                }
            },
            viewModel = viewModel
        )
        return  // Return early to show only the edit screen
    }
    
    // Show EmailManagementScreen as a complete full-screen replacement
    if (showEmailScanningDialog) {
        // This completely replaces the Bills screen content
        Box(modifier = Modifier.fillMaxSize()) {
            EmailManagementScreen(
                linkedEmails = linkedEmails,
                isScanning = isScanning,
                onAddEmail = { email ->
                    val added = viewModel.addLinkedEmail(email)
                    if (added) {
                        android.util.Log.d("BillsScreen", "Email added successfully: $email")
                        android.util.Log.d("BillsScreen", "Gmail service will be automatically connected via global service")
                    }
                    added
                },
                onRemoveEmail = { email -> viewModel.removeLinkedEmail(email) },
                onScanEmails = { viewModel.scanEmailsForBills() },
                onBack = { showEmailScanningDialog = false },
                onNavigateToGmailAuth = {
                    showEmailScanningDialog = false
                    onNavigateToGmailAuth()
                },
                onVerifyEmail = { email, activity -> viewModel.initiateEmailVerification(email, activity) }
            )
        }
        return // Return early - no Bills screen content should be visible
    }
    
    Scaffold(
        // Removed TopAppBar as per requirement
        floatingActionButton = {
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(uniformSectionSpacing())
            ) {
                // Email icon now above Add bill icon
                FloatingActionButton(
                    onClick = { showEmailScanningDialog = true },
                    containerColor = ProfessionalBlue,
                    contentColor = ProfessionalWhite,
                    modifier = Modifier.size(50.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Email,
                        contentDescription = "Scan Emails"
                    )
                }
                
                // Add bill button
                FloatingActionButton(
                    onClick = { showAddBillDialog = true },
                    containerColor = ProfessionalBlue,
                    contentColor = ProfessionalWhite,
                    modifier = Modifier.size(50.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "Add Bill"
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier.padding(
                start = innerPadding.calculateLeftPadding(LayoutDirection.Ltr),
                end = innerPadding.calculateRightPadding(LayoutDirection.Ltr)
            )
        ) {
            if (isLoading) {
                // Use the same loading state component as other screens
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            } else if (bills.isEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(uniformEmptyStatePadding()), // Standard empty state padding
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        modifier = Modifier.size(responsiveIconSize() * 3),
                        tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f)
                    )
                    Spacer(modifier = Modifier.height(uniformSectionSpacing()))
                    Text(
                        text = "No bills yet",
                        style = MaterialTheme.typography.titleMedium,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.height(uniformSmallSpacing()))
                    Text(
                        text = "Add your first bill to start tracking your expenses",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(uniformLargeSpacing()))
                    Button(
                        onClick = { showAddBillDialog = true },
                        modifier = Modifier.padding(horizontal = uniformHorizontalPadding())
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(responsiveSmallIconSize())
                        )
                        Spacer(modifier = Modifier.width(uniformSmallSpacing()))
                        Text("Add Bill")
                    }
                }
            } else {
                LazyColumn(
                    state = listState,
                    modifier = uniformContentBelowHeaderModifier(),
                    contentPadding = uniformContentPadding(),
                    verticalArrangement = Arrangement.spacedBy(uniformItemSpacing())
                ) {
                    // Initial spacing - uniform across all screens
                    item {
                        Spacer(modifier = Modifier.height(uniformInitialPadding()))
                    }

                    // Bills Summary Card with enhanced styling
                    item {
                        BillSummary(bills = bills)
                    }
                    
                    // Overdue Bills Section
                    if (limitedOverdueBills.isNotEmpty()) {
                        item {
                            Surface(
                                color = MaterialTheme.colorScheme.surface,
                                shape = MaterialTheme.shapes.medium,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = uniformSectionSpacing())
                            ) {
                                Column(modifier = Modifier.padding(vertical = uniformSectionSpacing())) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Text(
                                                text = "Overdue Bills (${limitedOverdueBills.size})",
                                                style = MaterialTheme.typography.titleMedium,
                                                fontWeight = FontWeight.Bold,
                                                color = MaterialTheme.colorScheme.error
                                            )
                                        }
                                    }

                                    Spacer(modifier = Modifier.height(uniformSmallSpacing()))

                                    limitedOverdueBills.forEach { bill ->
                                        BillItem(
                                            bill = bill,
                                            onMarkAsPaid = { viewModel.markBillAsPaid(it) },
                                            onDelete = { viewModel.deleteBill(it) },
                                            onEdit = { editingBill = it }
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // Pending Bills Section
                    if (limitedPendingBills.isNotEmpty()) {
                        item {
                            Surface(
                                color = MaterialTheme.colorScheme.surface,
                                shape = MaterialTheme.shapes.medium,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = uniformSectionSpacing())
                            ) {
                                Column(modifier = Modifier.padding(vertical = uniformSectionSpacing())) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Text(
                                                text = "Pending Bills (${limitedPendingBills.size})",
                                                style = MaterialTheme.typography.titleMedium,
                                                fontWeight = FontWeight.Bold,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }
                                    }

                                    Spacer(modifier = Modifier.height(uniformSmallSpacing()))

                                    limitedPendingBills.forEach { bill ->
                                        BillItem(
                                            bill = bill,
                                            onMarkAsPaid = { viewModel.markBillAsPaid(it) },
                                            onDelete = { viewModel.deleteBill(it) },
                                            onEdit = { editingBill = it }
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // Paid Bills Section
                    if (paidBills.isNotEmpty()) {
                        item {
                            Surface(
                                color = MaterialTheme.colorScheme.surface,
                                shape = MaterialTheme.shapes.medium,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = uniformSectionSpacing())
                            ) {
                                Column(modifier = Modifier.padding(vertical = uniformSectionSpacing())) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Text(
                                                text = "Paid Bills (${paidBills.size})",
                                                style = MaterialTheme.typography.titleMedium,
                                                fontWeight = FontWeight.Bold,
                                                color = MaterialTheme.colorScheme.tertiary
                                            )
                                        }
                                    }
                                    
                                    Spacer(modifier = Modifier.height(uniformSmallSpacing()))
                                    
                                    paidBills.forEach { bill ->
                                        BillItem(
                                            bill = bill,
                                            onMarkAsPaid = { viewModel.markBillAsPaid(it) },
                                            onDelete = { viewModel.deleteBill(it) },
                                            onEdit = { editingBill = it }
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    item {
                        Spacer(modifier = Modifier.height(uniformBottomSpacing())) // Space for FAB
                    }
                }
            }
        }
    }
}

@Composable
fun FilterChips(
    selectedFilter: BillFilterOption,
    onFilterSelected: (BillFilterOption) -> Unit
) {
    Row(
        modifier = Modifier.padding(end = uniformSectionSpacing()),
        horizontalArrangement = Arrangement.spacedBy(uniformSectionSpacing())
    ) {
        BillFilterOption.values().forEach { filter ->
            FilterChip(
                selected = filter == selectedFilter,
                onClick = { onFilterSelected(filter) },
                label = { Text(filter.name) }
            )
        }
    }
}

@Composable
fun EmptyBillsState(
    filter: BillFilterOption,
    onAddBill: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(uniformEmptyStatePadding()), // Standard empty state padding
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier.padding(uniformSectionSpacing())
            ) {
                // Icon based on filter type
                val iconTint = when (filter) {
                    BillFilterOption.ALL -> MaterialTheme.colorScheme.primary
                    BillFilterOption.PENDING -> Color.Blue
                    BillFilterOption.PAID -> Color.Green
                    BillFilterOption.OVERDUE -> Color.Red
                }
                
                Text(
                    text = when (filter) {
                        BillFilterOption.ALL -> "No bills yet"
                        BillFilterOption.PENDING -> "No pending bills"
                        BillFilterOption.PAID -> "No paid bills"
                        BillFilterOption.OVERDUE -> "No overdue bills"
                    },
                    style = MaterialTheme.typography.headlineSmall,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(uniformSectionSpacing()))
                
                Text(
                    text = when (filter) {
                        BillFilterOption.ALL -> "Add your first bill to start tracking your expenses"
                        BillFilterOption.PENDING -> "All your bills are either paid or overdue"
                        BillFilterOption.PAID -> "You haven't marked any bills as paid yet"
                        BillFilterOption.OVERDUE -> "Great! You have no overdue bills"
                    },
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                if (filter == BillFilterOption.ALL) {
                    Spacer(modifier = Modifier.height(uniformLargeSpacing()))
                    
                    Button(
                        onClick = onAddBill,
                        shape = MaterialTheme.shapes.medium
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(uniformSmallSpacing()))
                        Text("Add Bill")
                    }
                }
            }
        }
    }
}
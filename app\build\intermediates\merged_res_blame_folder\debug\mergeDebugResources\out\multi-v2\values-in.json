{"logs": [{"outputFile": "com.taskiq.app-mergeDebugResources-77:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dff1d73b7cef632e787de4d59cae0ad8\\transformed\\biometric-1.1.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,260,375,504,642,776,905,1040,1140,1279,1416", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "157,255,370,499,637,771,900,1035,1135,1274,1411,1535"}, "to": {"startLines": "59,62,68,69,70,71,72,73,74,75,76,77", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6218,6523,7115,7230,7359,7497,7631,7760,7895,7995,8134,8271", "endColumns": "106,97,114,128,137,133,128,134,99,138,136,123", "endOffsets": "6320,6616,7225,7354,7492,7626,7755,7890,7990,8129,8266,8390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\75e92b145e13fc673dc76999f901f30d\\transformed\\credentials-1.5.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2809,2918", "endColumns": "108,117", "endOffsets": "2913,3031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\72b42ec2c0a6db09976b88668f84c08b\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "39,40,61,63,64,78,79,138,139,140,141,143,144,145,146,147,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3845,3935,6425,6621,6721,8395,8478,14950,15037,15122,15204,15372,15444,15536,15613,15690,15864,15942,16008", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "3930,4012,6518,6716,6802,8473,8564,15032,15117,15199,15282,15439,15531,15608,15685,15758,15937,16003,16122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e866c3f6718b50411e7e0aa30260699c\\transformed\\play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4017,4124,4288,4414,4520,4675,4802,4917,5155,5321,5426,5590,5716,5871,6015,6079,6139", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4119,4283,4409,4515,4670,4797,4912,5018,5316,5421,5585,5711,5866,6010,6074,6134,6213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55690b61ef7b0489a32a7ec346714862\\transformed\\core-1.16.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "32,33,34,35,36,37,38,148", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3115,3210,3312,3409,3506,3612,3730,15763", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3205,3307,3404,3501,3607,3725,3840,15859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2989e2c91f6d0a9894b01384862e89f4\\transformed\\play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5023", "endColumns": "131", "endOffsets": "5150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11771762b22044a889273e9ed7a93127\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8569,8688,8803,8915,9031,9129,9235,9358,9505,9628,9778,9865,9969,10062,10166,10284,10404,10513,10653,10791,10920,11098,11220,11340,11463,11586,11680,11781,11901,12034,12136,12243,12350,12492,12639,12748,12848,12924,13020,13115,13233,13322,13407,13506,13586,13669,13768,13867,13964,14064,14151,14254,14353,14457,14574,14654,14759", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "8683,8798,8910,9026,9124,9230,9353,9500,9623,9773,9860,9964,10057,10161,10279,10399,10508,10648,10786,10915,11093,11215,11335,11458,11581,11675,11776,11896,12029,12131,12238,12345,12487,12634,12743,12843,12919,13015,13110,13228,13317,13402,13501,13581,13664,13763,13862,13959,14059,14146,14249,14348,14452,14569,14649,14754,14849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\718231422f48010e85ca6edf2e677575\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,220", "endColumns": "78,85,89", "endOffsets": "129,215,305"}, "to": {"startLines": "31,152,153", "startColumns": "4,4,4", "startOffsets": "3036,16127,16213", "endColumns": "78,85,89", "endOffsets": "3110,16208,16298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\45f9a3db26e33e80ab043af5a0e43024\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "60,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "6325,6807,6905,7014", "endColumns": "99,97,108,100", "endOffsets": "6420,6900,7009,7110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4dba44ce16815e024cb356c378b89b2b\\transformed\\appcompat-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,15287", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,15367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\396ff811c0e00aef5e8ac116f6d99809\\transformed\\material-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "14854", "endColumns": "95", "endOffsets": "14945"}}]}]}
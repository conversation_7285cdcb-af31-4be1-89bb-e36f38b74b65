1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskiq.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:5-71
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:22-68
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:5-72
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:5-80
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:5-81
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
21-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dff1d73b7cef632e787de4d59cae0ad8\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
21-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dff1d73b7cef632e787de4d59cae0ad8\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
22    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dff1d73b7cef632e787de4d59cae0ad8\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dff1d73b7cef632e787de4d59cae0ad8\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
23-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
24-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
25
26    <permission
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
33        android:name="com.taskiq.app.TaskIQApplication"
33-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:17:9-42
34        android:allowBackup="true"
34-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:18:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55690b61ef7b0489a32a7ec346714862\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:19:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:20:9-54
40        android:icon="@mipmap/ic_launcher"
40-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:21:9-43
41        android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:22:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:23:9-54
43        android:supportsRtl="true"
43-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:24:9-35
44        android:testOnly="true"
45        android:theme="@style/Theme.TaskIQ" >
45-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:25:9-44
46        <activity
46-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:27:9-48:20
47            android:name="com.taskiq.app.MainActivity"
47-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:28:13-41
48            android:exported="true"
48-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:29:13-36
49            android:label="@string/app_name"
49-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:31:13-45
50            android:launchMode="singleTask"
50-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:30:13-44
51            android:theme="@style/Theme.TaskIQ.Main" >
51-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:32:13-53
52            <intent-filter>
52-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:33:13-37:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:17-69
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:17-77
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:27-74
56            </intent-filter>
57
58            <!-- OAuth redirect handling -->
59            <intent-filter>
59-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:40:13-47:29
60                <action android:name="android.intent.action.VIEW" />
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
61
62                <category android:name="android.intent.category.DEFAULT" />
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
63                <category android:name="android.intent.category.BROWSABLE" />
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
64
65                <data
65-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
66                    android:host="oauth2redirect"
66-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
67                    android:scheme="taskiq" />
67-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
68            </intent-filter>
69        </activity>
70
71        <!-- Receiver for handling scheduled notifications -->
72        <receiver
72-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:51:9-60:20
73            android:name="com.taskiq.app.service.NotificationReceiver"
73-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:52:13-57
74            android:enabled="true"
74-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:53:13-35
75            android:exported="true" >
75-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:54:13-36
76            <intent-filter>
76-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
77                <action android:name="android.intent.action.BOOT_COMPLETED" />
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
78                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
79                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
80            </intent-filter>
81        </receiver>
82
83        <!-- Receiver for handling device boot completed -->
84        <receiver
84-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:63:9-72:20
85            android:name="com.taskiq.app.service.BootReceiver"
85-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:64:13-49
86            android:enabled="true"
86-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:65:13-35
87            android:exported="true" >
87-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:66:13-36
88            <intent-filter>
88-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
89                <action android:name="android.intent.action.BOOT_COMPLETED" />
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
90                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
91                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
92            </intent-filter>
93        </receiver>
94
95        <activity
95-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e70f3909067f43cab9e0d860fd3a79e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
96            android:name="androidx.compose.ui.tooling.PreviewActivity"
96-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e70f3909067f43cab9e0d860fd3a79e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
97            android:exported="true" />
97-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e70f3909067f43cab9e0d860fd3a79e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
98
99        <service
99-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
100            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
100-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
101            android:enabled="true"
101-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
102            android:exported="false" >
102-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
103            <meta-data
103-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
104                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
104-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
105                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
105-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
106        </service>
107
108        <activity
108-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
109            android:name="androidx.credentials.playservices.HiddenActivity"
109-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
110            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
110-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
111            android:enabled="true"
111-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
112            android:exported="false"
112-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
113            android:fitsSystemWindows="true"
113-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
114            android:theme="@style/Theme.Hidden" >
114-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
115        </activity>
116        <activity
116-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
117            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
117-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
118            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
118-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
119            android:enabled="true"
119-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
120            android:exported="false"
120-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
121            android:fitsSystemWindows="true"
121-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
122            android:theme="@style/Theme.Hidden" >
122-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0950f5df0fe43ee7bd4f4384c48c26e\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
123        </activity>
124        <activity
124-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
125            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
125-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
126            android:excludeFromRecents="true"
126-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
127            android:exported="false"
127-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
128            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
128-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
129        <!--
130            Service handling Google Sign-In user revocation. For apps that do not integrate with
131            Google Sign-In, this service will never be started.
132        -->
133        <service
133-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
134            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
134-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
135            android:exported="true"
135-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
136            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
136-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
137            android:visibleToInstantApps="true" />
137-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aa29a3ed610597815378031a0d430278\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
138
139        <activity
139-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e866c3f6718b50411e7e0aa30260699c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
140            android:name="com.google.android.gms.common.api.GoogleApiActivity"
140-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e866c3f6718b50411e7e0aa30260699c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
141            android:exported="false"
141-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e866c3f6718b50411e7e0aa30260699c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
142-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e866c3f6718b50411e7e0aa30260699c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
143
144        <meta-data
144-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2989e2c91f6d0a9894b01384862e89f4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
145            android:name="com.google.android.gms.version"
145-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2989e2c91f6d0a9894b01384862e89f4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
146            android:value="@integer/google_play_services_version" />
146-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2989e2c91f6d0a9894b01384862e89f4\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
147
148        <provider
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
149            android:name="androidx.startup.InitializationProvider"
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
150            android:authorities="com.taskiq.app.androidx-startup"
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
151            android:exported="false" >
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
152            <meta-data
152-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
153                android:name="androidx.work.WorkManagerInitializer"
153-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
154                android:value="androidx.startup" />
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
155            <meta-data
155-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1893e55916e25f20e35c3ef93de8a574\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
156                android:name="androidx.emoji2.text.EmojiCompatInitializer"
156-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1893e55916e25f20e35c3ef93de8a574\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
157                android:value="androidx.startup" />
157-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1893e55916e25f20e35c3ef93de8a574\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
158            <meta-data
158-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9af314888f115b17ca214d585648147d\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
159                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
159-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9af314888f115b17ca214d585648147d\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
160                android:value="androidx.startup" />
160-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9af314888f115b17ca214d585648147d\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
161            <meta-data
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
162                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
163                android:value="androidx.startup" />
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
164        </provider>
165
166        <service
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
167            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
169            android:enabled="@bool/enable_system_alarm_service_default"
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
170            android:exported="false" />
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
171        <service
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
172            android:name="androidx.work.impl.background.systemjob.SystemJobService"
172-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
174            android:enabled="@bool/enable_system_job_service_default"
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
175            android:exported="true"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
176            android:permission="android.permission.BIND_JOB_SERVICE" />
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
177        <service
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
178            android:name="androidx.work.impl.foreground.SystemForegroundService"
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
179            android:directBootAware="false"
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
180            android:enabled="@bool/enable_system_foreground_service_default"
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
181            android:exported="false" />
181-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
182
183        <receiver
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
184            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
186            android:enabled="true"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
187            android:exported="false" />
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
188        <receiver
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
194                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
194-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
195                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
195-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
199            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
199-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
204                <action android:name="android.intent.action.BATTERY_OKAY" />
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
204-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
205                <action android:name="android.intent.action.BATTERY_LOW" />
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
205-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
209-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
211            android:enabled="false"
211-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
214                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
214-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
215                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
215-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
216            </intent-filter>
217        </receiver>
218        <receiver
218-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
219            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
219-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
221            android:enabled="false"
221-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
222            android:exported="false" >
222-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
223            <intent-filter>
223-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
224                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
224-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
224-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
225            </intent-filter>
226        </receiver>
227        <receiver
227-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
228            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
228-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
229            android:directBootAware="false"
229-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
230            android:enabled="false"
230-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
231            android:exported="false" >
231-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
232            <intent-filter>
232-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
233                <action android:name="android.intent.action.BOOT_COMPLETED" />
233-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
233-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
234                <action android:name="android.intent.action.TIME_SET" />
234-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
234-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
235                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
235-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
235-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
236            </intent-filter>
237        </receiver>
238        <receiver
238-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
239            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
239-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
240            android:directBootAware="false"
240-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
241            android:enabled="@bool/enable_system_alarm_service_default"
241-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
242            android:exported="false" >
242-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
243            <intent-filter>
243-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
244                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
244-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
244-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
245            </intent-filter>
246        </receiver>
247        <receiver
247-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
248            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
248-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
250            android:enabled="true"
250-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
251            android:exported="true"
251-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
252            android:permission="android.permission.DUMP" >
252-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
253            <intent-filter>
253-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
254                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
254-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
254-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d07b00fb55591c6e0ad1832627f0fbd6\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
255            </intent-filter>
256        </receiver>
257
258        <activity
258-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb333869cd7bec7b1fd68b52076be3ad\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
259            android:name="androidx.activity.ComponentActivity"
259-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb333869cd7bec7b1fd68b52076be3ad\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
260            android:exported="true"
260-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb333869cd7bec7b1fd68b52076be3ad\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
261            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
261-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb333869cd7bec7b1fd68b52076be3ad\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
262
263        <service
263-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\be17a48a9fed71bd921da8552f72035c\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
264            android:name="androidx.room.MultiInstanceInvalidationService"
264-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\be17a48a9fed71bd921da8552f72035c\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
265            android:directBootAware="true"
265-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\be17a48a9fed71bd921da8552f72035c\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
266            android:exported="false" />
266-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\be17a48a9fed71bd921da8552f72035c\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
267
268        <receiver
268-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
269            android:name="androidx.profileinstaller.ProfileInstallReceiver"
269-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
270            android:directBootAware="false"
270-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
271            android:enabled="true"
271-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
272            android:exported="true"
272-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
274            <intent-filter>
274-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
275                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
275-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
275-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
276            </intent-filter>
277            <intent-filter>
277-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
278                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
278-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
278-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
279            </intent-filter>
280            <intent-filter>
280-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
281                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
281-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
281-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
282            </intent-filter>
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
284                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
284-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
284-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae0b88a2a876d6d8f8960d6668caa514\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
285            </intent-filter>
286        </receiver>
287    </application>
288
289</manifest>

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskiq.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:5-71
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:22-68
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:5-72
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:5-80
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:5-81
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30370bda1c96ec9fa500b7aba944c555\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
24    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30457fa838953244d5ba9ab13bbeb267\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
26
27    <permission
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
34        android:name="com.taskiq.app.TaskIQApplication"
34-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:17:9-42
35        android:allowBackup="true"
35-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:18:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\502b5ea5387ec613f4fdeabddbbf0af9\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:19:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:20:9-54
41        android:icon="@mipmap/ic_launcher"
41-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:21:9-43
42        android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:22:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:23:9-54
44        android:supportsRtl="true"
44-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:24:9-35
45        android:theme="@style/Theme.TaskIQ" >
45-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:25:9-44
46        <activity
46-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:27:9-48:20
47            android:name="com.taskiq.app.MainActivity"
47-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:28:13-41
48            android:exported="true"
48-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:29:13-36
49            android:label="@string/app_name"
49-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:31:13-45
50            android:launchMode="singleTask"
50-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:30:13-44
51            android:theme="@style/Theme.TaskIQ.Main" >
51-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:32:13-53
52            <intent-filter>
52-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:33:13-37:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:17-69
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:17-77
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:27-74
56            </intent-filter>
57
58            <!-- OAuth redirect handling -->
59            <intent-filter>
59-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:40:13-47:29
60                <action android:name="android.intent.action.VIEW" />
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
61
62                <category android:name="android.intent.category.DEFAULT" />
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
63                <category android:name="android.intent.category.BROWSABLE" />
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
64
65                <data
65-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
66                    android:host="oauth2redirect"
66-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
67                    android:scheme="taskiq" />
67-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
68            </intent-filter>
69        </activity>
70
71        <!-- Receiver for handling scheduled notifications -->
72        <receiver
72-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:51:9-60:20
73            android:name="com.taskiq.app.service.NotificationReceiver"
73-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:52:13-57
74            android:enabled="true"
74-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:53:13-35
75            android:exported="true" >
75-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:54:13-36
76            <intent-filter>
76-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
77                <action android:name="android.intent.action.BOOT_COMPLETED" />
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
78                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
79                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
80            </intent-filter>
81        </receiver>
82
83        <!-- Receiver for handling device boot completed -->
84        <receiver
84-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:63:9-72:20
85            android:name="com.taskiq.app.service.BootReceiver"
85-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:64:13-49
86            android:enabled="true"
86-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:65:13-35
87            android:exported="true" >
87-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:66:13-36
88            <intent-filter>
88-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
89                <action android:name="android.intent.action.BOOT_COMPLETED" />
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
90                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
91                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
92            </intent-filter>
93        </receiver>
94
95        <activity
95-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
96            android:name="androidx.compose.ui.tooling.PreviewActivity"
96-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
97            android:exported="true" />
97-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b44afddf6fae96b8516f7087e64b7204\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
98        <activity
98-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
99            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
100            android:excludeFromRecents="true"
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
101            android:exported="true"
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
102            android:launchMode="singleTask"
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
103            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
103-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
104            <intent-filter>
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
105                <action android:name="android.intent.action.VIEW" />
105-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
105-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
106
107                <category android:name="android.intent.category.DEFAULT" />
107-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
107-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
108                <category android:name="android.intent.category.BROWSABLE" />
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
109
110                <data
110-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
111                    android:host="firebase.auth"
111-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
112                    android:path="/"
113                    android:scheme="genericidp" />
113-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
114            </intent-filter>
115        </activity>
116        <activity
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
117            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
118            android:excludeFromRecents="true"
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
119            android:exported="true"
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
120            android:launchMode="singleTask"
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
121            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
122            <intent-filter>
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
123                <action android:name="android.intent.action.VIEW" />
123-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
123-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
124
125                <category android:name="android.intent.category.DEFAULT" />
125-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
125-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
126                <category android:name="android.intent.category.BROWSABLE" />
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
127
128                <data
128-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
129                    android:host="firebase.auth"
129-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
130                    android:path="/"
131                    android:scheme="recaptcha" />
131-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
132            </intent-filter>
133        </activity>
134
135        <service
135-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
136            android:name="com.google.firebase.components.ComponentDiscoveryService"
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
138            android:exported="false" >
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
139            <meta-data
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
140                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\38d88db0200311d917e820c52abda567\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
142            <meta-data
142-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
143                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
143-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
145            <meta-data
145-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
146                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
146-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baabbe58560e976daf3cb42b1916cb05\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
148            <meta-data
148-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
149                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
149-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
151            <meta-data
151-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
152                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
152-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e33e9cee0640d4525159b0894dbcd77\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
154            <meta-data
154-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
155                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
155-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
157            <meta-data
157-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
158                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
158-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
160            <meta-data
160-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
161                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
161-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14dc29405bd6bece97580238d4c2a11e\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
163            <meta-data
163-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
164                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
164-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56fce361cad8efa56496f167f2ad4792\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
166            <meta-data
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
167                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
169            <meta-data
169-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
170                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
170-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ce2b54ff4dbe0a4abecacf13e72a594\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
172        </service>
173        <service
173-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
174            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
175            android:enabled="true"
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
176            android:exported="false" >
176-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
177            <meta-data
177-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
178                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
178-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
179                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
179-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
180        </service>
181
182        <activity
182-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
183            android:name="androidx.credentials.playservices.HiddenActivity"
183-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
184            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
184-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
185            android:enabled="true"
185-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
186            android:exported="false"
186-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
187            android:fitsSystemWindows="true"
187-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
188            android:theme="@style/Theme.Hidden" >
188-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
189        </activity>
190        <activity
190-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
191            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
191-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
192            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
192-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
193            android:enabled="true"
193-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
194            android:exported="false"
194-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
195            android:fitsSystemWindows="true"
195-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
196            android:theme="@style/Theme.Hidden" >
196-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52735258e3c6a65d4f57a50731a1d8b7\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
197        </activity>
198        <activity
198-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
199            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
199-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
200            android:excludeFromRecents="true"
200-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
201            android:exported="false"
201-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
202            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
202-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
203        <!--
204            Service handling Google Sign-In user revocation. For apps that do not integrate with
205            Google Sign-In, this service will never be started.
206        -->
207        <service
207-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
208            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
208-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
209            android:exported="true"
209-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
210            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
210-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
211            android:visibleToInstantApps="true" />
211-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\564d200644c9719b4609be049e8fe8f0\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
212        <service
212-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
213            android:name="com.google.firebase.sessions.SessionLifecycleService"
213-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
214            android:enabled="true"
214-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
215            android:exported="false" />
215-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c38135bf8c16e3e5d866cba0b53702a4\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
216
217        <provider
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
218            android:name="com.google.firebase.provider.FirebaseInitProvider"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
219            android:authorities="com.taskiq.app.firebaseinitprovider"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
220            android:directBootAware="true"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
221            android:exported="false"
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
222            android:initOrder="100" />
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7b0cf319721cfffcffaebb40905027\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
223
224        <activity
224-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
225            android:name="com.google.android.gms.common.api.GoogleApiActivity"
225-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
226            android:exported="false"
226-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
227-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a84e368518001ab3aa41c1fdeafdf2\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
228
229        <provider
229-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
230            android:name="androidx.startup.InitializationProvider"
230-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
231            android:authorities="com.taskiq.app.androidx-startup"
231-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
233            <meta-data
233-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
234                android:name="androidx.work.WorkManagerInitializer"
234-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
235                android:value="androidx.startup" />
235-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
236            <meta-data
236-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b29ec1b0a3f33503df96abb36e92283\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
237                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
237-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b29ec1b0a3f33503df96abb36e92283\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
238                android:value="androidx.startup" />
238-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b29ec1b0a3f33503df96abb36e92283\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
239            <meta-data
239-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
240                android:name="androidx.emoji2.text.EmojiCompatInitializer"
240-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
241                android:value="androidx.startup" />
241-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5d2d7c606e46ea4542089095dc39ef4\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
242            <meta-data
242-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
243-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
244                android:value="androidx.startup" />
244-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eb3bc9e1744ef54aad499c2d8783e9b\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
245            <meta-data
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
246                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
247                android:value="androidx.startup" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
248            <meta-data
248-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c3e8860f44037666746e14cf8d6618b\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
249                android:name="com.russhwolf.settings.SettingsInitializer"
249-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c3e8860f44037666746e14cf8d6618b\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
250                android:value="androidx.startup" />
250-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c3e8860f44037666746e14cf8d6618b\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
251        </provider>
252
253        <service
253-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
254            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
254-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
255            android:directBootAware="false"
255-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
256            android:enabled="@bool/enable_system_alarm_service_default"
256-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
257            android:exported="false" />
257-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
258        <service
258-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
259            android:name="androidx.work.impl.background.systemjob.SystemJobService"
259-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
261            android:enabled="@bool/enable_system_job_service_default"
261-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
262            android:exported="true"
262-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
263            android:permission="android.permission.BIND_JOB_SERVICE" />
263-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
264        <service
264-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
265            android:name="androidx.work.impl.foreground.SystemForegroundService"
265-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
267            android:enabled="@bool/enable_system_foreground_service_default"
267-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
268            android:exported="false" />
268-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
269
270        <receiver
270-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
271            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
271-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
273            android:enabled="true"
273-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
274            android:exported="false" />
274-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
275        <receiver
275-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
276            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
276-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
277            android:directBootAware="false"
277-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
278            android:enabled="false"
278-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
279            android:exported="false" >
279-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
280            <intent-filter>
280-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
281                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
281-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
281-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
282                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
282-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
282-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
283            </intent-filter>
284        </receiver>
285        <receiver
285-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
286            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
286-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
287            android:directBootAware="false"
287-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
288            android:enabled="false"
288-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
289            android:exported="false" >
289-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
290            <intent-filter>
290-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
291                <action android:name="android.intent.action.BATTERY_OKAY" />
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
292                <action android:name="android.intent.action.BATTERY_LOW" />
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
293            </intent-filter>
294        </receiver>
295        <receiver
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
296            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
298            android:enabled="false"
298-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
299            android:exported="false" >
299-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
300            <intent-filter>
300-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
301                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
302                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
303            </intent-filter>
304        </receiver>
305        <receiver
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
306            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
306-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
308            android:enabled="false"
308-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
309            android:exported="false" >
309-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
310            <intent-filter>
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
311                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
312            </intent-filter>
313        </receiver>
314        <receiver
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
315            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
316            android:directBootAware="false"
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
317            android:enabled="false"
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
318            android:exported="false" >
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
319            <intent-filter>
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
320                <action android:name="android.intent.action.BOOT_COMPLETED" />
320-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
320-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
321                <action android:name="android.intent.action.TIME_SET" />
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
322                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
323            </intent-filter>
324        </receiver>
325        <receiver
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
326            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
328            android:enabled="@bool/enable_system_alarm_service_default"
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
329            android:exported="false" >
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
330            <intent-filter>
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
331                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
332            </intent-filter>
333        </receiver>
334        <receiver
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
335            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
336            android:directBootAware="false"
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
337            android:enabled="true"
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
338            android:exported="true"
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
339            android:permission="android.permission.DUMP" >
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
340            <intent-filter>
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
341                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cbf7ef5683102edbbe4d2ab4ddef9466\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
342            </intent-filter>
343        </receiver>
344
345        <service
345-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
346            android:name="androidx.room.MultiInstanceInvalidationService"
346-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
347            android:directBootAware="true"
347-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
348            android:exported="false" />
348-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b9ac1b7ac96592d414de344e89c8093\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
349
350        <meta-data
350-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
351            android:name="com.google.android.gms.version"
351-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
352            android:value="@integer/google_play_services_version" />
352-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\462f19984ab468993b4f437402a2d36f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
353
354        <activity
354-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
355            android:name="androidx.activity.ComponentActivity"
355-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
356            android:exported="true"
356-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
357            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
357-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\62142e5f5a1b1d2f54bc429ac39b749d\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
358
359        <service
359-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
360            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
360-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
361            android:exported="false" >
361-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
362            <meta-data
362-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
363                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
363-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
364                android:value="cct" />
364-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cd9279fd28172186895d872455373df6\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
365        </service>
366
367        <receiver
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
368            android:name="androidx.profileinstaller.ProfileInstallReceiver"
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
369            android:directBootAware="false"
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
370            android:enabled="true"
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
371            android:exported="true"
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
372            android:permission="android.permission.DUMP" >
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
373            <intent-filter>
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
374                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
375            </intent-filter>
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
377                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
378            </intent-filter>
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
380                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
383                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\629ae0b2d8f5373098399961f7d83252\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
384            </intent-filter>
385        </receiver>
386
387        <service
387-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
388-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
389            android:exported="false"
389-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
390            android:permission="android.permission.BIND_JOB_SERVICE" >
390-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
391        </service>
392
393        <receiver
393-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
394            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
394-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
395            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
395-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cceec5e941b4a29af1b04fe5def86056\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
396        <activity
396-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
397            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
397-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
398            android:exported="false"
398-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
399            android:stateNotNeeded="true"
399-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
400            android:theme="@style/Theme.PlayCore.Transparent" />
400-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f711b7ae48b340dc2447457e04aa08ee\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
401    </application>
402
403</manifest>

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskiq.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:5-71
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:22-68
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:5-72
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:5-80
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:5-81
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
24    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a01fced6cfaebb588867820a3c8ee936\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a01fced6cfaebb588867820a3c8ee936\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
26
27    <permission
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
34        android:name="com.taskiq.app.TaskIQApplication"
34-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:17:9-42
35        android:allowBackup="true"
35-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:18:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:19:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:20:9-54
41        android:icon="@mipmap/ic_launcher"
41-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:21:9-43
42        android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:22:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:23:9-54
44        android:supportsRtl="true"
44-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:24:9-35
45        android:theme="@style/Theme.TaskIQ" >
45-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:25:9-44
46        <activity
46-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:27:9-48:20
47            android:name="com.taskiq.app.MainActivity"
47-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:28:13-41
48            android:exported="true"
48-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:29:13-36
49            android:label="@string/app_name"
49-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:31:13-45
50            android:launchMode="singleTask"
50-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:30:13-44
51            android:theme="@style/Theme.TaskIQ.Main" >
51-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:32:13-53
52            <intent-filter>
52-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:33:13-37:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:17-69
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:17-77
55-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:27-74
56            </intent-filter>
57
58            <!-- OAuth redirect handling -->
59            <intent-filter>
59-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:40:13-47:29
60                <action android:name="android.intent.action.VIEW" />
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
61
62                <category android:name="android.intent.category.DEFAULT" />
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
62-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
63                <category android:name="android.intent.category.BROWSABLE" />
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
64
65                <data
65-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
66                    android:host="oauth2redirect"
66-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
67                    android:scheme="taskiq" />
67-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
68            </intent-filter>
69        </activity>
70
71        <!-- Receiver for handling scheduled notifications -->
72        <receiver
72-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:51:9-60:20
73            android:name="com.taskiq.app.service.NotificationReceiver"
73-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:52:13-57
74            android:enabled="true"
74-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:53:13-35
75            android:exported="true" >
75-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:54:13-36
76            <intent-filter>
76-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
77                <action android:name="android.intent.action.BOOT_COMPLETED" />
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
78                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
79                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
80            </intent-filter>
81        </receiver>
82
83        <!-- Receiver for handling device boot completed -->
84        <receiver
84-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:63:9-72:20
85            android:name="com.taskiq.app.service.BootReceiver"
85-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:64:13-49
86            android:enabled="true"
86-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:65:13-35
87            android:exported="true" >
87-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:66:13-36
88            <intent-filter>
88-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
89                <action android:name="android.intent.action.BOOT_COMPLETED" />
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
90                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
91                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
92            </intent-filter>
93        </receiver>
94
95        <activity
95-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
96            android:name="androidx.compose.ui.tooling.PreviewActivity"
96-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
97            android:exported="true" />
97-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
98        <activity
98-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
99            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
100            android:excludeFromRecents="true"
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
101            android:exported="true"
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
102            android:launchMode="singleTask"
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
103            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
103-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
104            <intent-filter>
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
105                <action android:name="android.intent.action.VIEW" />
105-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
105-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
106
107                <category android:name="android.intent.category.DEFAULT" />
107-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
107-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
108                <category android:name="android.intent.category.BROWSABLE" />
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
109
110                <data
110-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
111                    android:host="firebase.auth"
111-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
112                    android:path="/"
113                    android:scheme="genericidp" />
113-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
114            </intent-filter>
115        </activity>
116        <activity
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
117            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
118            android:excludeFromRecents="true"
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
119            android:exported="true"
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
120            android:launchMode="singleTask"
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
121            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
122            <intent-filter>
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
123                <action android:name="android.intent.action.VIEW" />
123-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
123-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
124
125                <category android:name="android.intent.category.DEFAULT" />
125-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
125-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
126                <category android:name="android.intent.category.BROWSABLE" />
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
127
128                <data
128-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
129                    android:host="firebase.auth"
129-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
130                    android:path="/"
131                    android:scheme="recaptcha" />
131-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
132            </intent-filter>
133        </activity>
134
135        <service
135-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
136            android:name="com.google.firebase.components.ComponentDiscoveryService"
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
138            android:exported="false" >
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
139            <meta-data
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
140                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
142            <meta-data
142-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
143                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
143-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
145            <meta-data
145-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
146                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
146-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
148            <meta-data
148-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
149                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
149-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
151            <meta-data
151-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
152                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
152-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
154            <meta-data
154-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
155                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
155-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
157            <meta-data
157-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
158                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
158-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
160            <meta-data
160-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
161                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
161-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
163            <meta-data
163-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
164                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
164-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
166            <meta-data
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
167                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
169            <meta-data
169-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
170                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
170-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
172        </service>
173        <service
173-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
174            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
175            android:enabled="true"
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
176            android:exported="false" >
176-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
177            <meta-data
177-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
178                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
178-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
179                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
179-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
180        </service>
181
182        <activity
182-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
183            android:name="androidx.credentials.playservices.HiddenActivity"
183-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
184            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
184-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
185            android:enabled="true"
185-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
186            android:exported="false"
186-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
187            android:fitsSystemWindows="true"
187-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
188            android:theme="@style/Theme.Hidden" >
188-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
189        </activity>
190        <activity
190-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
191            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
191-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
192            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
192-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
193            android:enabled="true"
193-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
194            android:exported="false"
194-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
195            android:fitsSystemWindows="true"
195-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
196            android:theme="@style/Theme.Hidden" >
196-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
197        </activity>
198        <activity
198-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
199            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
199-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
200            android:excludeFromRecents="true"
200-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
201            android:exported="false"
201-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
202            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
202-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
203        <!--
204            Service handling Google Sign-In user revocation. For apps that do not integrate with
205            Google Sign-In, this service will never be started.
206        -->
207        <service
207-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
208            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
208-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
209            android:exported="true"
209-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
210            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
210-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
211            android:visibleToInstantApps="true" />
211-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
212        <service
212-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
213            android:name="com.google.firebase.sessions.SessionLifecycleService"
213-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
214            android:enabled="true"
214-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
215            android:exported="false" />
215-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
216
217        <provider
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
218            android:name="com.google.firebase.provider.FirebaseInitProvider"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
219            android:authorities="com.taskiq.app.firebaseinitprovider"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
220            android:directBootAware="true"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
221            android:exported="false"
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
222            android:initOrder="100" />
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
223
224        <activity
224-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
225            android:name="com.google.android.gms.common.api.GoogleApiActivity"
225-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
226            android:exported="false"
226-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
227-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
228
229        <provider
229-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
230            android:name="androidx.startup.InitializationProvider"
230-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
231            android:authorities="com.taskiq.app.androidx-startup"
231-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
233            <meta-data
233-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
234                android:name="androidx.work.WorkManagerInitializer"
234-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
235                android:value="androidx.startup" />
235-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
236            <meta-data
236-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
237                android:name="androidx.emoji2.text.EmojiCompatInitializer"
237-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
238                android:value="androidx.startup" />
238-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
239            <meta-data
239-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
240                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
240-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
241                android:value="androidx.startup" />
241-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
242            <meta-data
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
244                android:value="androidx.startup" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
245        </provider>
246
247        <service
247-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
248            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
248-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
249            android:directBootAware="false"
249-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
250            android:enabled="@bool/enable_system_alarm_service_default"
250-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
251            android:exported="false" />
251-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
252        <service
252-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
253            android:name="androidx.work.impl.background.systemjob.SystemJobService"
253-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
254            android:directBootAware="false"
254-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
255            android:enabled="@bool/enable_system_job_service_default"
255-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
256            android:exported="true"
256-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
257            android:permission="android.permission.BIND_JOB_SERVICE" />
257-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
258        <service
258-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
259            android:name="androidx.work.impl.foreground.SystemForegroundService"
259-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
261            android:enabled="@bool/enable_system_foreground_service_default"
261-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
262            android:exported="false" />
262-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
263
264        <receiver
264-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
265            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
265-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
266            android:directBootAware="false"
266-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
267            android:enabled="true"
267-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
268            android:exported="false" />
268-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
269        <receiver
269-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
270-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
275                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
275-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
275-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
276                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
276-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
276-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
280-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
285                <action android:name="android.intent.action.BATTERY_OKAY" />
285-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
285-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
286                <action android:name="android.intent.action.BATTERY_LOW" />
286-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
286-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
290-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
295                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
296                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
300            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
300-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
302            android:enabled="false"
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
305                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
309            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
309-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
310            android:directBootAware="false"
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
311            android:enabled="false"
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
312            android:exported="false" >
312-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
313            <intent-filter>
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
314                <action android:name="android.intent.action.BOOT_COMPLETED" />
314-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
314-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
315                <action android:name="android.intent.action.TIME_SET" />
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
316                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
320            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
322            android:enabled="@bool/enable_system_alarm_service_default"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
323            android:exported="false" >
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
324            <intent-filter>
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
325                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
326            </intent-filter>
327        </receiver>
328        <receiver
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
329            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
330            android:directBootAware="false"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
331            android:enabled="true"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
332            android:exported="true"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
333            android:permission="android.permission.DUMP" >
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
334            <intent-filter>
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
335                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
336            </intent-filter>
337        </receiver>
338
339        <service
339-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
340            android:name="androidx.room.MultiInstanceInvalidationService"
340-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
341            android:directBootAware="true"
341-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
342            android:exported="false" />
342-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
343
344        <meta-data
344-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
345            android:name="com.google.android.gms.version"
345-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
346            android:value="@integer/google_play_services_version" />
346-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
347
348        <activity
348-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
349            android:name="androidx.activity.ComponentActivity"
349-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
350            android:exported="true"
350-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
351            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
351-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
352
353        <service
353-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
354            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
354-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
355            android:exported="false" >
355-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
356            <meta-data
356-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
357                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
357-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
358                android:value="cct" />
358-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
359        </service>
360
361        <receiver
361-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
362            android:name="androidx.profileinstaller.ProfileInstallReceiver"
362-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
363            android:directBootAware="false"
363-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
364            android:enabled="true"
364-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
365            android:exported="true"
365-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
366            android:permission="android.permission.DUMP" >
366-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
367            <intent-filter>
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
368                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
369            </intent-filter>
370            <intent-filter>
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
371                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
372            </intent-filter>
373            <intent-filter>
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
374                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
375            </intent-filter>
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
377                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
378            </intent-filter>
379        </receiver>
380
381        <service
381-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
382            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
382-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
383            android:exported="false"
383-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
384            android:permission="android.permission.BIND_JOB_SERVICE" >
384-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
385        </service>
386
387        <receiver
387-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
388-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
389            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
389-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
390        <activity
390-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
391            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
391-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
392            android:exported="false"
392-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
393            android:stateNotNeeded="true"
393-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
394            android:theme="@style/Theme.PlayCore.Transparent" />
394-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
395    </application>
396
397</manifest>

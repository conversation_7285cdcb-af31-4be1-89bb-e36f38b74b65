1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskiq.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:5-74
13-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:7:22-71
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:5-67
15-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:9:22-64
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:5-71
17-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:11:22-68
18    <uses-permission android:name="android.permission.READ_CALENDAR" />
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:5-72
18-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:12:22-69
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:5-80
19-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:13:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:5-81
20-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
21-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
22-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
23-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef8df5a53316d0138000accd463da68\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
24    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a01fced6cfaebb588867820a3c8ee936\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
24-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a01fced6cfaebb588867820a3c8ee936\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
26
27    <permission
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.taskiq.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:16:5-74:19
34        android:name="com.taskiq.app.TaskIQApplication"
34-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:17:9-42
35        android:allowBackup="true"
35-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:18:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adc15677f48e771c055defc45283b3c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:19:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:20:9-54
41        android:icon="@mipmap/ic_launcher"
41-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:21:9-43
42        android:label="@string/app_name"
42-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:22:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:23:9-54
44        android:supportsRtl="true"
44-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:24:9-35
45        android:testOnly="true"
46        android:theme="@style/Theme.TaskIQ" >
46-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:25:9-44
47        <activity
47-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:27:9-48:20
48            android:name="com.taskiq.app.MainActivity"
48-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:28:13-41
49            android:exported="true"
49-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:29:13-36
50            android:label="@string/app_name"
50-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:31:13-45
51            android:launchMode="singleTask"
51-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:30:13-44
52            android:theme="@style/Theme.TaskIQ.Main" >
52-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:32:13-53
53            <intent-filter>
53-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:33:13-37:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:17-69
54-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:34:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:17-77
56-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:36:27-74
57            </intent-filter>
58
59            <!-- OAuth redirect handling -->
60            <intent-filter>
60-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:40:13-47:29
61                <action android:name="android.intent.action.VIEW" />
61-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
61-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
62
63                <category android:name="android.intent.category.DEFAULT" />
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
63-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
64                <category android:name="android.intent.category.BROWSABLE" />
64-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
64-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
65
66                <data
66-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
67                    android:host="oauth2redirect"
67-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
68                    android:scheme="taskiq" />
68-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
69            </intent-filter>
70        </activity>
71
72        <!-- Receiver for handling scheduled notifications -->
73        <receiver
73-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:51:9-60:20
74            android:name="com.taskiq.app.service.NotificationReceiver"
74-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:52:13-57
75            android:enabled="true"
75-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:53:13-35
76            android:exported="true" >
76-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:54:13-36
77            <intent-filter>
77-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
78                <action android:name="android.intent.action.BOOT_COMPLETED" />
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
78-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
79                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
79-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
80                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
80-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
80-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
81            </intent-filter>
82        </receiver>
83
84        <!-- Receiver for handling device boot completed -->
85        <receiver
85-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:63:9-72:20
86            android:name="com.taskiq.app.service.BootReceiver"
86-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:64:13-49
87            android:enabled="true"
87-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:65:13-35
88            android:exported="true" >
88-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:66:13-36
89            <intent-filter>
89-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:55:13-59:29
90                <action android:name="android.intent.action.BOOT_COMPLETED" />
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
90-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
91                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:17-82
91-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:57:25-79
92                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
92-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:17-82
92-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:58:25-79
93            </intent-filter>
94        </receiver>
95
96        <activity
96-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
97            android:name="androidx.compose.ui.tooling.PreviewActivity"
97-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
98            android:exported="true" />
98-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b03d6128362d3d2fca86dc6b4fcd18dc\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
99        <activity
99-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
100            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
100-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
101            android:excludeFromRecents="true"
101-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
102            android:exported="true"
102-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
103            android:launchMode="singleTask"
103-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
104            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
104-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
105            <intent-filter>
105-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
106                <action android:name="android.intent.action.VIEW" />
106-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
106-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
107
108                <category android:name="android.intent.category.DEFAULT" />
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
108-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
109                <category android:name="android.intent.category.BROWSABLE" />
109-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
109-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
110
111                <data
111-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
112                    android:host="firebase.auth"
112-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
113                    android:path="/"
114                    android:scheme="genericidp" />
114-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
115            </intent-filter>
116        </activity>
117        <activity
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
118            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
119            android:excludeFromRecents="true"
119-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
120            android:exported="true"
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
121            android:launchMode="singleTask"
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
122            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
122-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
123            <intent-filter>
123-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
124                <action android:name="android.intent.action.VIEW" />
124-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:17-69
124-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:41:25-66
125
126                <category android:name="android.intent.category.DEFAULT" />
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:17-76
126-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:42:27-73
127                <category android:name="android.intent.category.BROWSABLE" />
127-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:17-78
127-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:43:27-75
128
129                <data
129-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:44:17-46:53
130                    android:host="firebase.auth"
130-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:46:21-50
131                    android:path="/"
132                    android:scheme="recaptcha" />
132-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:45:21-44
133            </intent-filter>
134        </activity>
135
136        <service
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
137            android:name="com.google.firebase.components.ComponentDiscoveryService"
137-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:67:13-84
138            android:directBootAware="true"
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
139            android:exported="false" >
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
140            <meta-data
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
141                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
141-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e63d976845ab3fe69bd023084e4f788b\transformed\firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
143            <meta-data
143-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
144                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
144-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
146            <meta-data
146-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
147                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
147-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca7de5eefa042fb37c48df59806eede\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
149            <meta-data
149-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
150                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
150-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
152            <meta-data
152-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
153                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
153-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:19:17-115
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d48840480a141c7da8fcd08d7e166c\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:20:17-82
155            <meta-data
155-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
156                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
156-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
158            <meta-data
158-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
159                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
159-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
161            <meta-data
161-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
162                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
162-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5e4e120472a27a967b5d3d28fb8\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
164            <meta-data
164-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
165                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
165-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c351547741b862f98b84f48e0bd942\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
167            <meta-data
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
168                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
170            <meta-data
170-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
171                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
171-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2ea16e41a2dc0bd411073a5e0d31ad1\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
173        </service>
174        <service
174-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:24:9-32:19
175            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
175-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:25:13-94
176            android:enabled="true"
176-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:26:13-35
177            android:exported="false" >
177-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:27:13-37
178            <meta-data
178-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:29:13-31:104
179                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
179-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:30:17-76
180                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
180-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:31:17-101
181        </service>
182
183        <activity
183-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:34:9-41:20
184            android:name="androidx.credentials.playservices.HiddenActivity"
184-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:35:13-76
185            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
185-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:36:13-87
186            android:enabled="true"
186-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:37:13-35
187            android:exported="false"
187-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:38:13-37
188            android:fitsSystemWindows="true"
188-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:39:13-45
189            android:theme="@style/Theme.Hidden" >
189-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:40:13-48
190        </activity>
191        <activity
191-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:42:9-49:20
192            android:name="androidx.credentials.playservices.IdentityCredentialApiHiddenActivity"
192-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:43:13-97
193            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
193-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:44:13-87
194            android:enabled="true"
194-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:45:13-35
195            android:exported="false"
195-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:46:13-37
196            android:fitsSystemWindows="true"
196-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:47:13-45
197            android:theme="@style/Theme.Hidden" >
197-->[androidx.credentials:credentials-play-services-auth:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48abf8d0bb245a0792f907060c344438\transformed\credentials-play-services-auth-1.5.0\AndroidManifest.xml:48:13-48
198        </activity>
199        <activity
199-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
200            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
200-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
201            android:excludeFromRecents="true"
201-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
202            android:exported="false"
202-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
203-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
204        <!--
205            Service handling Google Sign-In user revocation. For apps that do not integrate with
206            Google Sign-In, this service will never be started.
207        -->
208        <service
208-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
209            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
209-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
210            android:exported="true"
210-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
211            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
211-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
212            android:visibleToInstantApps="true" />
212-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f1cd8a7007882d810a07036b3356013\transformed\play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
213        <service
213-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
214            android:name="com.google.firebase.sessions.SessionLifecycleService"
214-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
215            android:enabled="true"
215-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
216            android:exported="false" />
216-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11b05cd1e9d183a7dfe9965df0e92308\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
217
218        <provider
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
219            android:name="com.google.firebase.provider.FirebaseInitProvider"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
220            android:authorities="com.taskiq.app.firebaseinitprovider"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
221            android:directBootAware="true"
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
222            android:exported="false"
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
223            android:initOrder="100" />
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15ffcf5c34560a6efebe6d43111dafd3\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
224
225        <activity
225-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
226            android:name="com.google.android.gms.common.api.GoogleApiActivity"
226-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
227            android:exported="false"
227-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
228            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
228-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61468bcbedfc450e580f06e70db30d2d\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
229
230        <provider
230-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
231            android:name="androidx.startup.InitializationProvider"
231-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
232            android:authorities="com.taskiq.app.androidx-startup"
232-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
233            android:exported="false" >
233-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
234            <meta-data
234-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
235                android:name="androidx.work.WorkManagerInitializer"
235-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
236                android:value="androidx.startup" />
236-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
237            <meta-data
237-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
238                android:name="androidx.emoji2.text.EmojiCompatInitializer"
238-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
239                android:value="androidx.startup" />
239-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8396ee8a0eabb25f52ad46838eb9755\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
240            <meta-data
240-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
241                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
241-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
242                android:value="androidx.startup" />
242-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\860574c29e7df7ecb840d0124dcf3f8c\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
243            <meta-data
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
244                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
245                android:value="androidx.startup" />
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
246        </provider>
247
248        <service
248-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
249            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
249-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
251            android:enabled="@bool/enable_system_alarm_service_default"
251-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
252            android:exported="false" />
252-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
253        <service
253-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
254            android:name="androidx.work.impl.background.systemjob.SystemJobService"
254-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
255            android:directBootAware="false"
255-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
256            android:enabled="@bool/enable_system_job_service_default"
256-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
257            android:exported="true"
257-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
258            android:permission="android.permission.BIND_JOB_SERVICE" />
258-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
259        <service
259-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
260            android:name="androidx.work.impl.foreground.SystemForegroundService"
260-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
262            android:enabled="@bool/enable_system_foreground_service_default"
262-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
263            android:exported="false" />
263-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
264
265        <receiver
265-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
266            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
266-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
267            android:directBootAware="false"
267-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
268            android:enabled="true"
268-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
269            android:exported="false" />
269-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
270        <receiver
270-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
271-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
276                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
276-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
276-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
277                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
277-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
277-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
281-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
286                <action android:name="android.intent.action.BATTERY_OKAY" />
286-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
286-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
287                <action android:name="android.intent.action.BATTERY_LOW" />
287-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
287-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
288            </intent-filter>
289        </receiver>
290        <receiver
290-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
291            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
293            android:enabled="false"
293-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
294            android:exported="false" >
294-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
295            <intent-filter>
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
296                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
297                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
297-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
297-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
301            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
303            android:enabled="false"
303-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
305            <intent-filter>
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
306                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
306-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
306-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
310            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
312            android:enabled="false"
312-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
313            android:exported="false" >
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
314            <intent-filter>
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
315                <action android:name="android.intent.action.BOOT_COMPLETED" />
315-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:17-79
315-->C:\Users\<USER>\Desktop\Task Reminder2- AugmentCode\app\src\main\AndroidManifest.xml:56:25-76
316                <action android:name="android.intent.action.TIME_SET" />
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
317                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
318            </intent-filter>
319        </receiver>
320        <receiver
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
321            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
323            android:enabled="@bool/enable_system_alarm_service_default"
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
324            android:exported="false" >
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
325            <intent-filter>
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
326                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
327            </intent-filter>
328        </receiver>
329        <receiver
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
330            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
331            android:directBootAware="false"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
332            android:enabled="true"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
333            android:exported="true"
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
334            android:permission="android.permission.DUMP" >
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
335            <intent-filter>
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
336                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1462ff9226cf23fb2bd96fcd62f3f998\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
337            </intent-filter>
338        </receiver>
339
340        <service
340-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
341            android:name="androidx.room.MultiInstanceInvalidationService"
341-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
342            android:directBootAware="true"
342-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
343            android:exported="false" />
343-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dcc07a170e76586d933c61d13082b62\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
344
345        <meta-data
345-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
346            android:name="com.google.android.gms.version"
346-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
347            android:value="@integer/google_play_services_version" />
347-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ba429aa6de3be7de3bb6e415af4e51\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
348
349        <activity
349-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
350            android:name="androidx.activity.ComponentActivity"
350-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
351            android:exported="true"
351-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
352            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
352-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a679d505f25dad7b604731af11c82e14\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
353
354        <service
354-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
355            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
355-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
356            android:exported="false" >
356-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
357            <meta-data
357-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
358                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
358-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
359                android:value="cct" />
359-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d842556c8af93ce28fb10d1a600d37b\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
360        </service>
361
362        <receiver
362-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
363            android:name="androidx.profileinstaller.ProfileInstallReceiver"
363-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
364            android:directBootAware="false"
364-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
365            android:enabled="true"
365-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
366            android:exported="true"
366-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
367            android:permission="android.permission.DUMP" >
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
368            <intent-filter>
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
369                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
370            </intent-filter>
371            <intent-filter>
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
372                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
373            </intent-filter>
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
375                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
376            </intent-filter>
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
378                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd4d43776f7f901eba23124e010b1908\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
379            </intent-filter>
380        </receiver>
381
382        <service
382-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
383            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
383-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
384            android:exported="false"
384-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
385            android:permission="android.permission.BIND_JOB_SERVICE" >
385-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
386        </service>
387
388        <receiver
388-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
389            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
389-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
390            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
390-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94207376451d9af89ca9dad99ed1d320\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
391        <activity
391-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
392            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
392-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
393            android:exported="false"
393-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
394            android:stateNotNeeded="true"
394-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
395            android:theme="@style/Theme.PlayCore.Transparent" />
395-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e6723bf3223f45401a01df88b36a37\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
396    </application>
397
398</manifest>
